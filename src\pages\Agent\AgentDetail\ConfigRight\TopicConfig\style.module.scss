.configButton {
  display: inline-flex;
  align-items: center;
  width: 20px;
  height: 20px;
  color: #6b7280;

  margin-left: -10px;

  svg {
    width: 20px;
    height: 20px;
  }

  &:hover {
    svg {
      color: #1677ff;
    }
  }
}

.configIcon {
  width: 20px;
  height: 20px;
  color: #6b7280;
  transition: color 0.3s;

  &:hover {
    color: #1677ff;
  }
}

.configWrapper {
  display: flex;
  align-items: center;
  margin-left: -10px;
  cursor: pointer;
}

.tagWrap {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.intervItem {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    border-color: #d1d5db;
  }

  .itemContent {
    .title {
      font-size: 14px;
      color: #1f2329;
      margin-bottom: 4px;
    }
  }

  .bottomLine {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .code {
      font-size: 12px;
      color: #6b7280;
    }

    .count {
      font-size: 12px;
      color: #6b7280;

      span {
        color: #6b7280;
        margin-right: 2px;
      }
    }
  }
}

.emptyTip {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
  padding: 32px 0;
}

.otherConfigContainer {
  padding: 20px;
  min-height: 200px;
}

.labelWithIcon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.arrow {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.arrowExpanded {
  composes: arrow;
  transform: rotate(90deg);
}

.buttonWrap {
  height: 25px;
  background: #f5f5f9;
  border-radius: 6px;
  line-height: 25px;
  padding: 0 10px;
  font-size: 12px;
  color: #6b7280;
  span {
    color: #374151;
    font-weight: 600;
  }
}
