import { Collapse } from 'antd'
import styles from './style.module.scss'
import IntervConfigIcon from 'assets/svgs/IntervConfigIcon.svg?react'
import { useState, useEffect } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import OtherConfigModal from './ConfigModal'
import { RightOutlined } from '@ant-design/icons'

function IntervConfig() {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [otherConfigModalOpen, setOtherConfigModalOpen] = useState(false)

  const [activeKey, setActiveKey] = useState(['interv'])

  const onOtherConfigClick = () => {
    setOtherConfigModalOpen(true)
  }

  const items = [
    {
      key: 'other',
      label: <div>其他配置</div>,
      extra: (
        <div
          className={styles.configWrapper}
          onClick={(e) => {
            e.stopPropagation()
            onOtherConfigClick()
          }}
        >
          <span className={styles.configButton}>
            <IntervConfigIcon />
          </span>
          <span>配置</span>
        </div>
      )
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        ghost
        onChange={onChange}
        expandIcon={() => null}
      />

      <OtherConfigModal
        visible={otherConfigModalOpen}
        onClose={() => setOtherConfigModalOpen(false)}
      />
    </>
  )
}

export default IntervConfig
