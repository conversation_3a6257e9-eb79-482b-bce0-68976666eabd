import { <PERSON>lapse, Col, <PERSON>, Space, Button } from 'antd'
import styles from './style.module.scss'
import { useState } from 'react'
import SearchConfigModal from '../SearchModal'
import { useConfigRightResource } from '../hooks/useConfigRight'

import { RightOutlined } from '@ant-design/icons'

function IntervConfig() {
  const [configModalOpen, setConfigModalOpen] = useState(false)

  const [activeKey, setActiveKey] = useState(['search'])

  const { list, fetchResources } = useConfigRightResource('search', true)

  const onAddClick = (e) => {
    e.stopPropagation()
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const items = [
    {
      key: 'search',

      label: (
        <div className={styles.labelWithIcon}>
          <RightOutlined
            className={activeKey.includes('search') ? styles.arrowExpanded : styles.arrow}
          />
          <span>搜索配置</span>
        </div>
      ),
      children: !!list.length && (
        <div className={styles.intervItem}>
          <Row gutter={20} size={40}>
            {list.map((x) => (
              <Col span={8} key={x.id}>
                <p className="truncate" title={x.name}>
                  {x.name}
                </p>
              </Col>
            ))}
          </Row>
        </div>
      ),
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>
            {/* 已配置&nbsp;<span>{(!!list.length && 1) || 0}</span> */}

            {list.length > 0 ? (
              <>
                已配置&nbsp;<span>{!!list.length && 1}</span>
              </>
            ) : (
              <>未配置</>
            )}
          </div>
          <Button type="dashed" onClick={onAddClick} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        size="small"
        ghost
        activeKey={activeKey}
        onChange={onChange}
        expandIcon={() => null}
      />
      <SearchConfigModal
        visible={configModalOpen}
        onClose={onConfigModalCancel}
        onRefresh={fetchResources}
      />
    </>
  )
}

export default IntervConfig
