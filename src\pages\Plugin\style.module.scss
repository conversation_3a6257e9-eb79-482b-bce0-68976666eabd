.agentContent {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 让整个布局占据视口高度 */
  .agentHeader {
    margin-top: var(--agent-header-margin-top, 20px);
    margin-bottom: 10px;
    display: flex;
    .headerLeft {
      color: #333;
      font-size: 22px;
      margin-left: 10px;
      margin-right: auto;
    }
  }
  .agentList {
    height: auto;
    display: grid;
    // grid-template-columns: repeat(3, 1fr);
    grid-template-columns: repeat(auto-fill, minmax(374px, 1fr));
    gap: 18px;
  }

  .footer {
    // margin-top: 20px;
    margin-top: auto;
  }
}
