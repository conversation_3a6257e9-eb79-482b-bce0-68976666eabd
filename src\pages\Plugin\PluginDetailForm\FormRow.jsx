import {
  Form,
  Input,
  Button,
  Space,
  Select,
  Row,
  Col,
  Switch,
  Divider,
  Checkbox,
  message
} from 'antd'

import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  AppstoreAddOutlined,
  ToolOutlined,
  KeyOutlined,
  LockOutlined,
  RightOutlined,
  DownOutlined
} from '@ant-design/icons'
import { v4 as uuidv4, validate } from 'uuid'
import React, { useState, useEffect, useMemo } from 'react'
import useFormStore from './formData.js'
import styles from './style.module.scss'
import { webSchema } from './data.js'
import ajax from '@/utils/http'
import ArrayModal from './arrayModal'

const FormRow = ({ name, fieldKey, restField, remove, area, nestingLevel = 0 }) => {
  const [arrayData, setArrayData] = useState([])
  const [modalOpen, setModalOpen] = useState(false)
  const [rowPosition, setRowPosition] = useState([])

  const form = Form.useFormInstance() // 获取 Form 实例(其实和外面是一个form)

  const [collapsed, setCollapsed] = useState(false) // 新增折叠状态

  const type = Form.useWatch([area, ...name, 'type'], form)
  const children = Form.useWatch([area, ...name, 'children'], form) || []
  // const valueFrom = Form.useWatch([area, ...name, 'from'], form)
  const fatherType = Form.useWatch([area, ...name, 'fatherType'], form)
  const defaultValue = Form.useWatch([area, ...name, 'defaultValue'], form)
  const arraySon = Form.useWatch([area, ...name, 'arraySon'], form) || false
  const isUserAdded = Form.useWatch([area, ...name, 'isUserAdded'], form) || false

  const nameIndex = name[name.length - 1]
  // if(nestingLevel!=0){
  //   console.log('这是子节点',name, nestingLevel,type);
  // }
  // else{
  //   console.log('nestingLevel',name, nestingLevel,type,children);
  // }

  const handleDataSave = (arrayData) => {
    console.log('赋值', arrayData)
    form.setFieldValue(rowPosition, JSON.parse(JSON.stringify(arrayData)))
    setArrayData([])
    setRowPosition([])
    //
    // setTimeout(()=> {
    //   console.log('set后',form.getFieldValue(rowPosition));
    // })
  }

  const handleAddChild = (type) => {
    const currentChildren = form.getFieldValue([area, ...name, 'children']) || []
    // let currentChildren = children
    let newChild = {}
    if (type === 'object') {
      newChild = {
        id: uuidv4(),
        type: 'string', // 默认参数类型
        required: true, // 默认是否必填
        // from: 2, // 默认取值来源
        open: true, // 默认开启,
        fatherType: 'object',
        startDisabled: false,
        nameErrMsg: '',
        descriptionErrMsg: '',
        isUserAdded: true,
        arraySon
      }
    } else if (type === 'array') {
      newChild = {
        id: uuidv4(),
        name: '[Array Item]',
        fatherType: 'array',
        arraySon: true,
        type: 'string', // 默认参数类型
        required: false, // 默认是否必填
        // from: 2, // 默认取值来源
        open: true, // 默认开启,
        startDisabled: false,
        nameErrMsg: '',
        descriptionErrMsg: '',
        isUserAdded: true
      }
      // form.setFieldValue([area, ...name, 'subChild'])
    }
    form.setFieldValue([area, ...name, 'children'], [...currentChildren, newChild])
    // console.log('更新后',children,form.getFieldsValue());
  }

  const handleRemoveChild = (index) => {
    const currentChildren = form.getFieldValue([area, ...name, 'children'])
    const updatedChildren = currentChildren.filter((_, i) => i !== index) // 过滤掉指定项
    form.setFieldValue([area, ...name, 'children'], updatedChildren)
  }

  const handleTypeChange = (val) => {
    form.setFieldValue([area, ...name, 'children'], [])
    checkArray()
    if (val === 'object' || val === 'array') {
      handleAddChild(val)
    }
  }

  const findTopArrayPath = (currentPath) => {
    if (currentPath.length < 2) return null
    // 向上回溯两级（children + index结构）
    const parentPath = currentPath.slice(0, -2)
    const parentType = form.getFieldValue([...parentPath, 'type'])

    if (parentType === 'array') {
      return parentPath // 找到目标路径
    }
    return findTopArrayPath(parentPath) // 递归查找
  }

  const clearDefault = (obj) => {
    obj.defaultValue = ''
    if (obj.children && obj.children.length > 0) {
      for (let child of obj.children) {
        clearDefault(child)
      }
    }
    return obj
  }
  const checkArray = () => {
    if (arraySon) {
      // 清空之前最上层 type=array的父节点children数据，保留一个array item子节点以记录数据结构
      let fullPath = [area, ...name]
      let topArrayPath = findTopArrayPath(fullPath)
      if (topArrayPath) {
        let parentValue = form.getFieldValue(topArrayPath)
        let parenChildrenValue = parentValue.children
        let childItem = JSON.parse(JSON.stringify(parenChildrenValue[0]))
        childItem = clearDefault(childItem)
        form.setFieldValue([...topArrayPath, 'children'], [childItem])
      }
    }
  }

  const handleArrayEdit = (e) => {
    // e.stopPropagation()
    let dataType = children[0][type]
    if (dataType == '') return message.warning('请先定义array元素类型')
    let currentRowValue = form.getFieldValue([area, ...name])
    console.log('currentRowValue', currentRowValue)
    setArrayData(currentRowValue)
    setRowPosition(JSON.parse(JSON.stringify([area, ...name])))
    setModalOpen(true)
  }

  const validateNameUnique = (form, area, currentPath, currentName, currentIndex) => {
    // 获取所有同级字段
    const parentPath = currentPath.slice(0, -1)
    const siblings = form.getFieldValue([area, ...parentPath]) || []

    // 检查同级重复
    const hasDuplicate = siblings.some(
      (item, index) => index !== currentIndex && item?.name === currentName
    )

    if (hasDuplicate) return false

    // 如果是对象/数组，递归检查子级
    if (currentPath.length > 2) {
      return validateNameUnique(
        form,
        area,
        parentPath,
        currentName,
        parentPath[parentPath.length - 1]
      )
    }

    return true
  }

  return (
    <React.Fragment>
      <Row gutter={16} key={fieldKey}>
        <Col span={1}>
          {(type === 'object' || type === 'array') && (
            <Button
              type="text"
              size="small"
              icon={collapsed ? <RightOutlined /> : <DownOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginLeft: `${nestingLevel * 12}px` }}
            />
          )}
        </Col>

        <Col span={area === 'toolRequestInput' ? 3 : 7}>
          <Form.Item
            // {...restField}
            name={[nameIndex, 'name']}
            rules={[
              { required: true, message: '请输入参数名称' },
              {
                max: 32,
                message: '字符数不能超过32'
              },
              // {
              //   pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
              //   message: '只能包含字母、数字或下划线，并且以字母或下划线开头'
              // },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve()

                  // 1. 获取当前字段的完整路径
                  const fullPath = [area, ...name]

                  // 2. 获取父级路径和同级字段
                  let siblings = []
                  let currentIndex = name[name.length - 1]

                  if (name.length === 1) {
                    // 第一层级字段
                    siblings = form.getFieldValue([area]) || []
                  } else {
                    // 嵌套层级字段
                    // 找到包含children数组的父级路径
                    let parentPath = [area]
                    let childrenPath = []

                    // 构建父级路径（去掉最后的索引和可能的'children'）
                    for (let i = 0; i < name.length - 1; i++) {
                      if (name[i] === 'children' && i === name.length - 2) {
                        // 这是children数组中的项，父级路径是上一级
                        break
                      }
                      parentPath.push(name[i])
                    }

                    // 获取父级对象
                    const parent = form.getFieldValue(parentPath)

                    // 确定从父级的哪个属性获取同级字段
                    if (name[name.length - 2] === 'children') {
                      // 当前是children数组中的项
                      siblings = parent?.children || []
                    } else {
                      // 其他情况（理论上不应该发生）
                      siblings = []
                    }
                  }

                  console.log('验证名称唯一性:', {
                    value,
                    fullPath,
                    currentIndex,
                    siblings: siblings.map((s) => s?.name),
                    siblingPaths: siblings.map((_, i) => [...fullPath.slice(0, -1), i])
                  })

                  // 3. 检查同父级下的重复名称（排除自身）
                  const duplicates = siblings.filter(
                    (item, index) => index !== currentIndex && item?.name === value
                  )

                  if (duplicates.length > 0 && fatherType !== 'array') {
                    return Promise.reject('同层级参数名称不能重复')
                  }

                  if (fatherType == 'array') {
                    return Promise.resolve()
                  }
                  if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
                    return Promise.resolve()
                  }
                  return Promise.reject(
                    new Error('只能包含字母、数字或下划线，并且以字母或下划线开头')
                  )
                }
              }
            ]}
            dependencies={[[area, ...name.slice(0, -1)].join('.')]}
            style={{ paddingLeft: `${nestingLevel * 12}px` }}
          >
            <Input
              placeholder="测试输入参数名称"
              //   disabled={fatherType === 'array'}
              disabled={!isUserAdded}
              onChange={(e) => checkArray()}
            />
          </Form.Item>
        </Col>

        <Col span={area === 'toolRequestInput' ? 4 : 8}>
          <Form.Item
            // {...restField}
            name={[nameIndex, 'description']}
            rules={[{ required: true, message: '请输入参数描述' }]}
          >
            <Input
              placeholder="请输入参数描述"
              onChange={(e) => checkArray()}
              disabled={!isUserAdded}
            />
          </Form.Item>
        </Col>

        <Col span={area === 'toolRequestInput' ? 3 : 4}>
          <Form.Item
            // {...restField}
            name={[nameIndex, 'type']}
            rules={[{ required: true, message: '请选择参数类型' }]}
          >
            <Select
              placeholder="选择参数类型"
              onChange={(val) => handleTypeChange(val)}
              disabled={!isUserAdded}
            >
              <Option value="string">string</Option>
              <Option value="number">number</Option>
              <Option value="integer">integer</Option>
              <Option value="boolean">boolean</Option>
              {fatherType != 'array' && <Option value="array">array</Option>}
              <Option value="object">object</Option>
            </Select>
          </Form.Item>
        </Col>
        {area === 'toolRequestInput' && (
          <React.Fragment>
            <Col span={3}>
              {nestingLevel === 0 && (
                <Form.Item
                  // {...restField}
                  name={[nameIndex, 'location']}
                  rules={[{ required: true, message: '请选择传入方法' }]}
                >
                  <Select placeholder="选择参数类型" disabled={!isUserAdded}>
                    <Option value="query">query</Option>
                    <Option value="body">body</Option>
                    <Option value="path">path</Option>
                    <Option value="header">header</Option>
                  </Select>
                </Form.Item>
              )}
            </Col>

            <Col span={3}>
              {fatherType != 'array' && (
                <Form.Item name={[nameIndex, 'required']} valuePropName="checked">
                  <Checkbox onChange={(val) => checkArray()} disabled={!isUserAdded} />
                </Form.Item>
              )}
            </Col>

            {/* <Col span={3}>
                <Form.Item name={[nameIndex, 'from']}>
                  <Select placeholder="选择来源" onChange={(val) => checkArray()}>
                    <Option value={2}>默认值</Option>
                    <Option value={0}>模型识别</Option>
                    <Option value={1}>业务透传</Option>
                  </Select>
                </Form.Item>
              </Col> */}

            <Col span={3}>
              {type != 'object' && type != 'array' && (
                <Form.Item
                  // {...restField}
                  name={[nameIndex, 'defaultValue']}
                  rules={[
                    ({ getFieldValue }) => ({
                      required: getFieldValue([nameIndex, 'required']),
                      message: '请输入默认值'
                    })
                  ]}
                  style={{ visibility: arraySon ? 'hidden' : 'visible' }}
                >
                  <Input placeholder="测试输入默认值" />
                </Form.Item>
              )}

              {type == 'array' && !arraySon && (
                <Form.Item name={[nameIndex, 'defaultValue']}>
                  <Button color="default" type="dashed" onClick={(e) => handleArrayEdit(e)}>
                    编辑数组参数
                  </Button>
                </Form.Item>
              )}
            </Col>
          </React.Fragment>
        )}

        <Col span={2}>
          {/* {type != 'object' && (
              <Form.Item name={[nameIndex, 'open']}>
                <Switch />
              </Form.Item>
            )} */}
          <Form.Item name={[nameIndex, 'open']}>
            <Switch disabled={!isUserAdded} />
          </Form.Item>
        </Col>
        <Col span={2}>
          <Form.Item
            // {...restField}
            // name={[nameIndex, 'operation']}
            dependencies={[area, nameIndex, 'type']}
          >
            {() => (
              <div style={{ display: 'flex' }}>
                {type === 'object' && (
                  //   <AppstoreAddOutlined
                  //     style={{ cursor: 'pointer', marginRight: '8px' }}
                  //     onClick={() => {
                  //       handleAddChild('object')
                  //     }}
                  //   />

                  <Button
                    style={{ cursor: 'pointer', marginRight: '8px' }}
                    icon={<AppstoreAddOutlined />}
                    type="text"
                    disabled={!isUserAdded}
                    onClick={() => {
                      handleAddChild('object')
                    }}
                  ></Button>
                )}

                {/* {fatherType != 'array' && <MinusCircleOutlined onClick={() => remove(name)} />} */}
                {fatherType != 'array' && (
                  <Button
                    type="text"
                    onClick={() => remove(name)}
                    disabled={!isUserAdded}
                    icon={<MinusCircleOutlined />}
                  />
                )}
              </div>
            )}
          </Form.Item>
        </Col>
      </Row>
      {(type === 'object' || type === 'array') && children.length > 0 && !collapsed && (
        <Form.List name={[nameIndex, 'children']}>
          {(childFields, { add, remove }) => (
            <>
              {childFields.map((childField, index) => {
                // console.log('childField', childField)
                if (type === 'array' && index != 0) {
                  return
                } else {
                  return (
                    <FormRow
                      key={`${name}-child-${index}`}
                      name={[...name, 'children', childField.name]}
                      fieldKey={`${fieldKey}-child-${index}`}
                      restField={childField}
                      remove={() => handleRemoveChild(index)}
                      nestingLevel={nestingLevel + 1}
                      area={area}
                    />
                  )
                }
              })}
            </>
          )}
        </Form.List>
      )}

      <ArrayModal
        open={modalOpen}
        arrayData={arrayData}
        handleDataSave={handleDataSave}
        setIsModalOpen={setModalOpen}
      ></ArrayModal>
    </React.Fragment>
  )
}

export default FormRow
