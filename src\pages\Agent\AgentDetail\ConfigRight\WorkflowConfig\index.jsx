import { But<PERSON>, <PERSON>lapse, <PERSON>, Tag, Space } from 'antd'
// import styles from './style.module.scss'
import styles from '../style.module.scss'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import { useState } from 'react'
import { useEffect } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import WorkflowConfigModal from './ConfigModal'
import ResourceTags from '../Common/Tags'

function WorkflowConfig() {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  const [totalCount, setTotalCount] = useState(0)
  const [flows, setFlows] = useState([])

  const [configModalOpen, setConfigModalOpen] = useState(false)

  useEffect(() => {
    if (agentDetail?.boxBot?.botId) {
      getFlows(agentDetail?.boxBot?.botId)
    }
  }, [agentDetail])

  const onRefresh = () => {
    if (agentDetail?.boxBot?.botId) {
      getFlows(agentDetail?.boxBot?.botId)
    }
  }

  const getFlows = async (boxBotId) => {
    const res = await ajax({
      url: '/bot/config/getBotWorkFlows',
      data: {
        botId: boxBotId,
        pageIndex: 1,
        pageSize: 1000
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      // console.log('agent详情页获取的agent详情信息：', res.data?.data?.count)

      setFlows((res.data?.data?.flows || []).filter((item) => item.selected))
      setTotalCount((res.data?.data?.flows || []).filter((item) => item.selected).length)
    }
  }

  const onAddClick = () => {
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const items = [
    {
      key: 'workflow',
      label: <div>工作流</div>,
      children: (
        // <div className={styles.tagWrap}>
        //   {flows.map((it) => {
        //     return <ResourceTag key={it.id} type="flow" name={it.name}></ResourceTag>
        //   })}

        // </div>
        <ResourceTags
          type="flow"
          data={flows.map((it) => {
            return {
              flowId: it.flowId,
              isAuthor: it.isAuthor,
              id: it.id,
              name: it.name
            }
          })}
        ></ResourceTags>
      ),
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>
            {/* 已配置&nbsp;<span>{totalCount}</span> */}

            {totalCount > 0 ? (
              <>
                已配置&nbsp;<span>{totalCount}</span>
              </>
            ) : (
              <>未配置</>
            )}
          </div>
          <Button type="dashed" onClick={onAddClick} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]

  const onChange = (val) => {
    console.log('onChaneg', val)
    setActiveKey(val)
  }

  const [activeKey, setActiveKey] = useState([])
  useEffect(() => {
    if (totalCount > 0) {
      setActiveKey(['workflow'])
    }
  }, [totalCount])

  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        collapsible={'icon'}
        ghost
        className=""
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
        onChange={onChange}
      />
      <WorkflowConfigModal
        visible={configModalOpen}
        onClose={onConfigModalCancel}
        onRefresh={onRefresh}
      />
    </>
  )
}
export default WorkflowConfig
