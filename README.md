# React Vite 项目

## 项目结构

本项目使用 [Vite](https://vitejs.dev/) 搭建，基于 React 进行开发。目录结构如下：

```
project-root/
├── src/
│   ├── assets/       # 静态资源（图片、字体、样式等）
│   ├── components/   # 复用组件，每个组件包含 index.jsx 和 style.module.scss
│   ├── pages/        # 页面级组件，每个页面包含 index.jsx 和 style.module.scss
│   ├── utils/        # 工具函数（封装的通用方法，如格式化、请求封装等）
│   ├── hooks/        # 自定义 React Hooks
│   ├── store/        # Zustand 状态管理相关文件
│   ├── router/       # 路由配置文件
│   ├── main.jsx      # 入口文件
│   ├── App.jsx       # 顶层组件
│   ├── index.css     # 全局样式
│
├── public/           # Vite 公共资源目录（不会被打包）
├── vite.config.js    # Vite 配置文件
├── package.json      # 项目依赖和脚本
├── README.md         # 项目说明文档
```

## 开发规范

### 组件规范

- 所有组件存放在 `components/` 或 `pages/` 目录下。
- 组件命名采用大驼峰（PascalCase）。
- 组件结构：
  ```
  MyComponent/
  ├── index.jsx         # 组件实现
  ├── style.module.scss # 组件样式（CSS Modules）
  ```

### 样式规范

- 采用 `CSS Modules` 进行样式管理，每个组件的样式文件命名为 `style.module.scss`。
- 组件内样式命名遵循 BEM 规范，如：
  ```scss
  .button {
    &--primary {
      background-color: blue;
    }
  }
  ```

### 状态管理

- 使用 `Zustand` 进行状态管理，状态相关代码存放在 `store/` 目录下。
- 每个 store 文件管理一个功能模块的状态，例如：

  ```javascript
  // store/useUserStore.js
  import { create } from 'zustand'

  const useUserStore = create((set) => ({
    user: null,
    setUser: (user) => set({ user })
  }))

  export default useUserStore
  ```

### 路由管理

- 采用 `react-router-dom` 进行路由管理，所有路由配置存放在 `router/` 目录下。

## 运行与构建

### 安装依赖

```sh
npm install
```

### 启动开发服务器

```sh
npm run start
```

### 生产环境构建

```sh
npm run build
```

## 代码提交规范

- 代码提交信息应遵循 [Conventional Commits](https://www.conventionalcommits.org/) 规范，例如：
  ```
  feat: 添加用户登录功能
  fix: 修复页面加载错误
  refactor: 优化 Zustand 状态管理代码
  ```
- 使用 `ESLint` 和 `Prettier` 保持代码风格统一。

## 通用组件说明

- 公共样式位于src/App.scss 中， antd组件主题变量位于src/main.jsx中
- 菜单组件位于pages/Root.jsx中，当前页面对应的高亮菜单通过检查路由路径实现
- 定制组件 src/components 文件夹中，组件通过index.jsx 统一导出使用

---

本项目致力于提供高效、清晰的 React 开发体验，欢迎贡献和优化代码！
