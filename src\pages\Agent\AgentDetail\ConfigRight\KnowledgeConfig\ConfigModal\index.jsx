import React, { useState, useEffect, useRef } from 'react'
import {
  Modal,
  Table,
  Select,
  Button,
  Checkbox,
  message,
  Space,
  InputNumber,
  Tooltip,
  Tag,
  Flex,
  Input,
  Pagination,
  Empty
} from 'antd'
import { TagOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { APP_ENV } from '@/utils/constant'
import ajax from '@/utils/http'
import { MyModal } from '@/components'
import styles from './style.module.scss'
import microAppRouter from '@/hooks/useMicroAppRouter.js'
import CenterLoading from '@/components/CenterLoading'
import { CustomEmpty } from '@/components'

const { Search } = Input

const AgentKnowledgeConfigModal = ({ visible, onClose, onRefresh }) => {
  const { baseRouter } = microAppRouter()
  const navigate = useNavigate()
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [channel, setChannel] = useState(1)

  // const [repoTableDataOrigin, setRepoTableDataOrigin] = useState([]) // 储存原始数据，和修改过后的新数据对比，过滤提交
  const repoTableDataOrigin = useRef([])
  const [repoTableData, setRepoTableData] = useState([])

  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10 // 每页10条数据

  // 计算当前页数据
  const currentData = repoTableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  const changeChannel = (e) => {
    setChannel(e)
  }

  const handleSubmit = () => {
    const addRepos = []
    const delRepos = []
    const updateRepos = []

    // 创建一个原始数据的映射，方便查找
    const originMap = {}
    repoTableDataOrigin.current.forEach((item) => {
      originMap[item.id] = item
    })

    // 遍历当前表格数据，找出变更
    repoTableData.forEach((currentItem) => {
      const originItem = originMap[currentItem.id]

      if (!originItem) {
        // 如果是新增的数据（如果有这种情况）
        addRepos.push(currentItem)
        return
      }

      // 检查selected字段的变化
      if (currentItem.selected !== originItem.selected) {
        if (currentItem.selected) {
          // 从未选中变为选中 -> 添加到addRepos
          addRepos.push(currentItem)
        } else {
          // 从选中变为未选中 -> 添加到delRepos
          delRepos.push(currentItem)
        }
      } else {
        // selected没有变化，检查isTop是否有变化
        if (currentItem.threshold !== originItem.threshold) {
          // 只有isTop变化 -> 添加到updateRepos
          updateRepos.push(currentItem)
        }
      }
    })

    if (addRepos.length === 0 && delRepos.length === 0 && updateRepos.length === 0) {
      return message.warning('无配置更新')
    }

    const params = {
      botId: agentDetail?.boxBot?.botId,
      channel: channel,
      addRepos,
      updateRepos,
      delRepos
    }
    ajax({
      url: '/bot/config/saveRagRepoConfig',
      method: 'post',
      data: params
    }).then((res) => {
      if (res.data?.code === '0') {
        message.success('保存成功')
        onRefresh()
        onClose()
      }
    })
  }

  // 添加知识库 的table
  const getRepoTableData = async (boxBotId) => {
    const parentData = window.microApp?.getData()
    setLoading(true)
    const res = await ajax({
      url: '/bot/config/getBotRagRepos',
      data: {
        botId: boxBotId,
        pageIndex: 1,
        pageSize: 1000
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      console.log(res.data?.data?.repos, '全量数据repos表格')

      const arr = (res.data?.data?.repos || [])
        .filter((it) => {
          if (APP_ENV === 'auto') {
            if (parentData?.orgCode) {
              return it.userGroup === `auto:${parentData?.orgCode}`
            } else {
              return true
            }
          } else {
            return true
          }
        })
        .map((item) => {
          return {
            repoId: item.repoid,
            repoCode: item?.id,
            groupId: item.groupId,
            configId: item?.configId,
            repoName: item?.name,
            knowledgeCount: item?.knowledgeCount,
            threshold: item?.threshold || 0.8,
            selected: item.selected,
            id: item.id,
            knowledgeCount: item?.knowledgeCount,
            description: item?.description
          }
        })
        .sort((a, b) => {
          if (a.selected === b.selected) {
            return 0 // 如果 quote 值相同，保持原有顺序
          }
          return a.selected ? -1 : 1 // 如果 a.quote 为 true，a 排在前面；否则 b 排在前面
        })
      setRepoTableData(arr)
      repoTableDataOrigin.current = (arr || []).slice()
      setLoading(false)
    }
  }

  const gotoCreate = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/repo')
    } else if (APP_ENV === 'auto') {
      baseRouter.push('/agent/repo')
    }
  }

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && visible) {
      getRepoTableData(agentDetail?.boxBot?.botId)
    } else {
      setRepoTableData([])
      setCurrentPage(1)
    }
  }, [visible, agentDetail])

  const onSearch = (value) => {
    const searchItems = repoTableDataOrigin.current.filter((it) =>
      it.repoName.toLowerCase().includes(value.toLowerCase())
    )
    setCurrentPage(1)
    setRepoTableData(searchItems)
  }

  const handleCheck = (checked, record) => {
    setRepoTableData((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          selected: it.id === record.id ? checked : it.selected
        }
      })
    )
  }
  return (
    <MyModal
      open={visible}
      title="添加知识库"
      onCancel={onClose}
      onClose={onClose}
      onOk={handleSubmit}
      width={1000}
      cancelButtonProps={{ style: { display: 'none' } }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          检索方法 ：
          <Select
            value={channel}
            style={{ width: 120, marginRight: '10px' }}
            options={[
              { value: 1, label: '单向量召回' },
              { value: 2, label: '多路召回' }
            ]}
            onChange={changeChannel}
          ></Select>
          使用语义向量召回知识点
        </div>

        <Flex justify={'space-between'} align={'center'} style={{ marginBottom: 10 }}>
          <Button type="link" onClick={gotoCreate}>{`创建知识库`}</Button>

          <Search
            placeholder="输入知识库中文名称搜索"
            onSearch={onSearch}
            allowClear
            style={{ width: 300 }}
          />
        </Flex>
        {currentData.length > 0 ? (
          <>
            {currentData.map((item) => (
              <div key={item.id} className={styles.itemCard}>
                <Checkbox
                  checked={item.selected}
                  // onChange={(e) => {
                  //   const newData = currentData.map((single) =>
                  //     single.id === item.id ? { ...single, selected: e.target.checked } : single
                  //   )
                  //   setRepoTableData(newData)
                  // }}
                  onChange={(e) => handleCheck(e.target.checked, item)}
                  className={styles.checkbox}
                ></Checkbox>

                <div className={styles.content}>
                  <div className={styles.title}>
                    {item.repoName}

                    <span className={styles.subtitle}>
                      <Tag icon={<TagOutlined />} color="purple">
                        {item.knowledgeCount}个知识点
                      </Tag>
                    </span>
                  </div>

                  <Tooltip title={item?.description?.length > 25 ? item.description : ''}>
                    <div className={styles.description}>{item.description}</div>
                  </Tooltip>
                </div>

                <InputNumber
                  min={0}
                  max={1}
                  value={Number(item.threshold) || 0.1}
                  step={0.1}
                  precision={1} // 确保显示1位小数
                  controls={true}
                  onChange={(value) => {
                    const newData = repoTableData.map((single) =>
                      single.id === item.id ? { ...single, threshold: value } : single
                    )
                    setRepoTableData(newData)
                  }}
                ></InputNumber>
              </div>
            ))}

            {/* 分页器 */}
            {repoTableData.length > pageSize && (
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={repoTableData.length}
                onChange={(page) => setCurrentPage(page)}
                style={{ marginTop: 16, textAlign: 'center' }}
                align="end"
              />
            )}
          </>
        ) : loading ? (
          <CenterLoading height={300} />
        ) : (
          <CustomEmpty />
        )}
      </Space>
    </MyModal>
  )
}

export default AgentKnowledgeConfigModal
