import { Form, Modal, Input, message, Space } from 'antd'
import { useState } from 'react'
import { validateTrim } from '@/utils/validateTrim'
import ajax from '@/utils/http'
import IconAgent from 'assets/svgs/icon-agent.svg?react'
import { APP_ENV, cssVariables } from '@/utils/constant'
import { MyModal } from '@/components'

function AgentModal({ open, handleCancel, handleRefresh }) {
  const [createLoading, setCreateLoading] = useState(false)
  const [form] = Form.useForm()
  const handleOk = () => {
    form.validateFields().then((values) => {
      saveData(values)
    })
  }

  const saveData = async ({ name, description }) => {
    setCreateLoading(true)
    let param = {
      name,
      description
    }

    try {
      const result = await ajax({
        url: '/bot/saveBot',
        method: 'post',
        data: param
      })
      console.log('新建结果', result)

      if (result.data?.code === '0') {
        message.success('创建成功')
        setCreateLoading(false)
        handleCancel()
        handleRefresh()
        //   setIsModalOpen(false)
        //   navigate(`/${result.data?.data?.id}`)
      }
    } catch (e) {
      setCreateLoading(false)
    }
  }

  return (
    <MyModal
      title={
        APP_ENV === 'base' ? (
          <Space size={4}>
            <IconAgent /> {'创建智能体'}
          </Space>
        ) : (
          '创建智能体'
        )
      }
      open={open}
      onOk={handleOk}
      okText={'创建'}
      onCancel={handleCancel}
      destroyOnHidden={true}
      confirmLoading={createLoading}
      width={600}
    >
      <Form
        form={form}
        name="basic"
        layout="vertical"
        initialValues={{}}
        autoComplete="off"
        preserve={false}
      >
        <Form.Item
          label="名称"
          name="name"
          rules={[
            {
              required: true,
              message: '请输入智能体名称'
            },
            {
              pattern: /^[\u4e00-\u9fffa-zA-Z0-9_]{0,}$/,
              message: '只支持中文/英文/数字/下划线格式'
            }
          ]}
        >
          <Input placeholder="请输入智能体名称" showCount maxLength={32} />
        </Form.Item>

        <Form.Item
          label="描述"
          name="description"
          rules={[{ required: true, message: '请输入智能体描述' }, { validator: validateTrim }]}
        >
          <Input.TextArea placeholder="请输入智能体描述" showCount maxLength={200} />
        </Form.Item>
      </Form>
    </MyModal>
  )
}
export default AgentModal
