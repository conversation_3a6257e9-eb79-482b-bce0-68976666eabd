import { useState, useEffect } from 'react'
import { Select, Checkbox, message, Button, Dropdown, Switch } from 'antd'
import PropTypes from 'prop-types'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import { APP_ENV, cssVariables } from '@/utils/constant'
import ajax from '@/utils/http'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { EyeInvisibleOutlined, GlobalOutlined } from '@ant-design/icons'

const REJECT_TYPE_MAP = {
  personaAble: { label: '人设拒识', value: 'persona', key: 'personaAble' },
  empathyAble: { label: '共情拒识', value: 'empathy', key: 'empathyAble' },
  arcAble: { label: '实体单说拒识', value: 'arc', key: 'arcAble' },
  referenceAble: { label: '端状态代指拒识', value: 'reference', key: 'referenceAble' }
}

const SEARCH_TYPE_MAP = {
  SearchAggSummary: '聚合搜索摘要版本',
  SearchAggContent: '聚合搜索全文版本'
}

const STRATEGY_MAP = {
  serial: '知识库优先',
  parallel: '知识库和搜索并行'
}

const OtherConfigModal = ({ visible, onClose }) => {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const routerId = useAgentDetailStore((state) => state.routerId)
  const [loading, setLoading] = useState(false)
  const [messageApi, contextHolder] = message.useMessage()
  const [selectedRejects, setSelectedRejects] = useState({})
  const [config, setConfig] = useState({
    rejectionRules: [],
    searchConfig: {
      enabled: false,
      searchType: '',
      strategy: ''
    }
  })

  const fetchConfig = async () => {
    try {
      setLoading(true)
      const res = await ajax({
        url: '/bot/config/getAbleAndSearchConfig',
        method: 'get',
        data: {
          botId: agentDetail?.boxBot?.botId,
          routerId
        }
      })

      if (res.data.code === '0') {
        const { ability, searchType, strategy } = res.data.data

        const rejectionRules = ability.map((item) => {
          const selectedTypes = []
          const supportedTypes = []
          Object.entries(REJECT_TYPE_MAP).forEach(([key, type]) => {
            const abilityStatus = item[key]
            if (abilityStatus > 0) {
              supportedTypes.push({
                ...type,
                disabled: false
              })
              if (abilityStatus === 2) {
                selectedTypes.push(type.value)
              }
            }
          })

          return {
            name: item.abilityName,
            code: item.abilityCode,
            abilityId: item.abilityId,
            recognitionTypes: selectedTypes
              .map(
                (value) =>
                  Object.values(REJECT_TYPE_MAP).find((type) => type.value === value)?.label
              )
              .filter(Boolean),
            supportedTypes
          }
        })

        const initialSelectedRejects = {}
        rejectionRules.forEach((rule) => {
          initialSelectedRejects[rule.abilityId] = rule.recognitionTypes
            .map(
              (label) => Object.values(REJECT_TYPE_MAP).find((type) => type.label === label)?.value
            )
            .filter(Boolean)
        })

        setConfig({
          rejectionRules,
          searchConfig: {
            enabled: !!searchType && searchType !== 'Close',
            searchType: searchType || 'SearchAggSummary',
            strategy: strategy || 'serial'
          }
        })

        setSelectedRejects(initialSelectedRejects)
      }
    } catch {
      messageApi.error('获取配置失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible && agentDetail?.boxBot?.botId && routerId) {
      fetchConfig()
    }
  }, [visible, agentDetail?.boxBot?.botId, routerId])

  const handleRejectChange = (ruleIndex, checkedValues) => {
    const rule = config.rejectionRules[ruleIndex]
    setSelectedRejects((prev) => ({
      ...prev,
      [rule.abilityId]: checkedValues
    }))

    const newRejectionRules = [...config.rejectionRules]
    const selectedTypes = checkedValues
      .map((value) => Object.values(REJECT_TYPE_MAP).find((type) => type.value === value)?.label)
      .filter(Boolean)

    newRejectionRules[ruleIndex] = {
      ...newRejectionRules[ruleIndex],
      recognitionTypes: selectedTypes
    }

    setConfig((prev) => ({
      ...prev,
      rejectionRules: newRejectionRules
    }))
  }

  const handleSearchConfigChange = (field, value) => {
    setConfig((prev) => ({
      ...prev,
      searchConfig: {
        ...prev.searchConfig,
        [field]: value
      }
    }))
  }

  const handleSearchEnabledChange = (checked) => {
    setConfig((prev) => ({
      ...prev,
      searchConfig: {
        ...prev.searchConfig,
        enabled: checked,
        searchType: checked ? 'SearchAggSummary' : 'Close'
      }
    }))
  }

  const handleMainModalOk = async () => {
    if (!agentDetail?.boxBot?.botId) {
      messageApi.error('botId不能为空')
      return
    }

    try {
      setLoading(true)

      const saveData = {
        botId: agentDetail.boxBot.botId
      }
      if (config.searchConfig.enabled) {
        saveData.searchType = config.searchConfig.searchType
        saveData.strategy = config.searchConfig.strategy
      } else {
        saveData.searchType = 'Close'
      }

      config.rejectionRules.forEach((rule) => {
        const selectedTypes = selectedRejects[rule.abilityId] || []

        Object.entries(REJECT_TYPE_MAP).forEach(([key, type]) => {
          if (selectedTypes.includes(type.value)) {
            const ableType = key.replace('Able', 'Ables')
            if (!saveData[ableType]) {
              saveData[ableType] = {}
            }
            saveData[ableType][rule.code] = 2
          }
        })
      })

      await ajax({
        url: '/bot/config/saveAbleAndSearchConfig',
        method: 'post',
        data: saveData
      })

      messageApi.success('保存成功')
      onClose()
    } catch {
      messageApi.error('保存失败')
    } finally {
      setLoading(false)
    }
  }

  const getDropdownContent = (ruleIndex) => {
    const rule = config.rejectionRules[ruleIndex]
    return {
      overlayClassName: styles.rejectDropdown,
      trigger: ['click'],
      overlay: (
        <div className={styles.dropdownContent}>
          <Checkbox.Group
            options={rule.supportedTypes}
            value={selectedRejects[rule.abilityId] || []}
            onChange={(checkedValues) => handleRejectChange(ruleIndex, checkedValues)}
          />
          <div className={styles.note}>备注：勾选为拒识</div>
        </div>
      )
    }
  }

  return (
    <>
      {contextHolder}
      <MyModal
        title="其他配置"
        open={visible}
        onCancel={onClose}
        onOk={handleMainModalOk}
        width={800}
        destroyOnHidden
        confirmLoading={loading}
      >
        <div className={styles.container}>
          <div className={styles.section}>
            <div className={styles.sectionTitle}>
              <EyeInvisibleOutlined /> 拒识配置
            </div>
            <div className={styles.ruleList}>
              {config.rejectionRules.map((rule, index) => (
                <div key={rule.abilityId} className={styles.ruleItem}>
                  <div className={styles.ruleContent}>
                    <div className={styles.ruleName}>{rule.name}</div>
                    <div className={styles.ruleCode}>{rule.code}</div>
                  </div>
                  <Dropdown {...getDropdownContent(index)}>
                    <Button className={styles.rejectButton}>
                      <EyeInvisibleOutlined />
                      拒识 {rule.recognitionTypes.join('、')}
                    </Button>
                  </Dropdown>
                </div>
              ))}
            </div>
          </div>

          {/* 搜索配置 */}
          <div className={styles.section}>
            <div className={styles.sectionTitle}>
              <GlobalOutlined /> 搜索配置
              <Switch
                checked={config.searchConfig.enabled}
                onChange={handleSearchEnabledChange}
                style={{ marginLeft: '12px' }}
              />
            </div>
            {config.searchConfig.enabled && (
              <div className={styles.searchConfig}>
                <Select
                  value={config.searchConfig.searchType}
                  onChange={(value) => handleSearchConfigChange('searchType', value)}
                  style={{ width: 'calc(50% - 8px)' }}
                >
                  {Object.entries(SEARCH_TYPE_MAP)
                    .filter(([value]) => value !== 'Close')
                    .map(([value, label]) => (
                      <Select.Option key={value} value={value}>
                        {label}
                      </Select.Option>
                    ))}
                </Select>
                <Select
                  value={config.searchConfig.strategy}
                  onChange={(value) => handleSearchConfigChange('strategy', value)}
                  style={{ width: 'calc(50% - 8px)' }}
                >
                  {Object.entries(STRATEGY_MAP).map(([value, label]) => (
                    <Select.Option key={value} value={value}>
                      {label}
                    </Select.Option>
                  ))}
                </Select>
              </div>
            )}
          </div>
        </div>
      </MyModal>
    </>
  )
}

OtherConfigModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
}

export default OtherConfigModal
