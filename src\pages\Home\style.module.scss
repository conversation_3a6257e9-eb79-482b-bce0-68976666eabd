.banner {
  width: 100%;
  height: 150px;
  box-shadow:
    0px 1px 3px 0px rgba(0, 0, 0, 0.08),
    0px 0px 2px 0px rgba(0, 0, 0, 0.08),
    0px 0px 1px 0px rgba(0, 0, 0, 0.32);
}

.homeTitle {
  position: relative;
  padding-left: 14px;
  color: #1d1f25;
  line-height: 16px;
  font-size: 13px;
  font-family:
    PingFang SC,
    PingFang SC-500;
  font-weight: 500;
  margin-top: 16px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  &:before {
    content: '';
    background-color: #000000;
    width: 3px;
    height: 12px;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -6px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
  }
  .more {
    font-size: 12px;
    font-family:
      PingFang SC,
      PingFang SC-400;
    font-weight: 400;
    color: #1d1f25;
    cursor: pointer;
  }
}

.cardContainer {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 一行三列，每列等宽
  gap: 12px;
}
