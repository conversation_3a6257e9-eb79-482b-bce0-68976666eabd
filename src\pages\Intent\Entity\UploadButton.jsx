import React, { useState } from 'react'
import { Upload, Button, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import ajax from '@/utils/http'

const UploadButton = ({
  dictId = '',
  name = '',
  options = { isCover: false, text: '上传' },
  limitCount = { entity_file_size: 5 },
  setLoad = () => {},
  getEntryList = () => {},
  setErrInfo = () => {},
  close = () => {}
}) => {
  //覆盖type为1，追加type为0

  const beforeUpload = async (file) => {
    const isValidType = /\.(txt|xls|xlsx)(\?.*)?$/.test(file.name)
    const isWithinSize = file.size < 1024 * 1024 * limitCount['entity_file_size']

    if (!isValidType) {
      message.error('仅支持Excel和txt文件')
      return Upload.LIST_IGNORE
    }
    if (!isWithinSize) {
      message.error(`文件不能超过${limitCount['entity_file_size']}M`)
      return Upload.LIST_IGNORE
    }
    return true
  }

  const customRequest = async ({ file, onSuccess, onError }) => {
    setLoad(true)

    const isExcel = /\.(xls|xlsx)(\?.*)?$/.test(file.name)
    const endpoint = isExcel ? '/doExcelImport' : '/dict/importByDictId'

    const actionUrl = `/bot/entity${endpoint}?dictId=${dictId}&type=${options.type}`

    try {
      const res = await ajax({
        url: actionUrl,
        data: {
          file
        },
        method: 'post',
        useFormData: true
      })

      const data = res.data
      if (data.flag) {
        getEntryList()
        message.success('上传成功')
      } else {
        if (data.code === '106010') {
          // setErrInfo(data.desc, true);
          message.error(data.desc)
        } else {
          message.error(data.desc || '上传失败')
        }
        onError(data)
      }
      close()
    } catch (err) {
      message.error('上传失败')
    } finally {
      setLoad(false)
    }
  }

  return (
    <Upload
      name="file"
      showUploadList={false}
      action={`/iflycloud/api/bot/entity/doExcelImport?dictId=${dictId}&type=${options.type}`}
      beforeUpload={beforeUpload}
      customRequest={customRequest}
    >
      <span style={{ color: options.color }}>{options.text}</span>
    </Upload>
  )
}

export default UploadButton
