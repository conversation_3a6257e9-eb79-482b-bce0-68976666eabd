#root {
  height: 100%;
}

iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* 整体滚动条宽度和高度 */
::-webkit-scrollbar {
  width: 8px; /* 纵向滚动条宽度 */
  height: 8px; /* 横向滚动条高度 */
  // display: none;
}

/* 滚动条轨道（背景） */
::-webkit-scrollbar-track {
  background: #f9fafb; /* 更浅的灰色背景 */
  border-radius: 4px;
  // display: none;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #e6e9ef; /* 更浅的滑块颜色 */
  border-radius: 4px; /* 圆角滑块 */
  // display: none;
}

/* 鼠标悬停时的滚动条滑块 */
::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25); /* 鼠标悬停时略深的滑块颜色 */
}

/* 水平滚动条样式调整 */
::-webkit-scrollbar-horizontal {
  height: 6px; /* 更窄的横向滚动条 */
}

::-webkit-scrollbar-thumb:horizontal {
  border-radius: 3px; /* 横向滑块更小的圆角 */
}

/* 禁用滚动条轨道边框（可选） */
::-webkit-scrollbar-track-piece {
  border: none;
}

.ant-drawer {
  outline: none !important;
}

.main-container {
  background: #ffffff;
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;
  padding: var(--container-padding, 32px);
  border-radius: var(
    --container-border-radius,
    var(--container-radius) 0 0 var(--container-radius)
  );
  border-left: var(--container-border-left);
}

.square {
  width: 1156px;
  margin: 0 auto;
}

a {
  color: var(--sparkos-second-color);
  &:hover {
    color: var(--sparkos-second-color);
  }
}

.ant-btn-icon {
  height: 18px;
}
