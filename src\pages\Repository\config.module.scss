.split-config {
  position: relative;
  width: 100%;
  .option-split {
    border: 1px solid #dbdfe6;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    margin-bottom: 22px;
    padding: 8px 20px;
    .option-radio {
      width: 16px;
      height: 16px;
      border: 1px solid #8290a6;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;
      margin-top: 6px;
      &-inner {
        width: 8px;
        height: 8px;
        background: #ffffff;
        border-radius: 50%;
      }
    }
    .option-title {
      .title {
        font-weight: 500;
        line-height: 24px;
      }
      .dsp {
        color: #8290a6;
        font-weight: 400;
        margin-top: 2px;
        font-size: 12px;
        line-height: 20px;
      }
    }
    .line {
      width: 100%;
      height: 1px;
      background: #ebeff7;
      margin-top: 16px;
      margin-bottom: 8px;
    }
  }
  .option-active {
    cursor: default;
    border: 1px solid var(--sparkos-primary-color);
    padding-bottom: 20px;
    .option-radio {
      background: var(--sparkos-primary-color);
      border: none;
    }
  }
  .flex {
    display: flex;
  }
}
