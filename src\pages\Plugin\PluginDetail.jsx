import { useState, useEffect, useRef } from 'react'
import { Select, Button, message, Space, App, Drawer, Form } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeftOutlined } from '@ant-design/icons'
import styles from './detail.module.scss'
import useFormStore from '@/pages/Plugin/PluginDetailForm/formData.js'
import TestToolForm from '@/pages/Plugin/PluginDetailForm/stepForm2.jsx'
import DebugForm from '@/pages/Plugin/PluginDetailForm/stepForm3.jsx'
import ajax from '@/utils/http'
import IconGoback from 'assets/svgs/goback.svg?react'
import CozeOrMassForm from '@/pages/Plugin/PluginDetailForm/CozeOrMassForm.jsx'
import NewCozeAndMass from './PluginDetailForm/NewCozeAndMass'
import AutoTemForm from '@/pages/Plugin/PluginDetailForm/AutoTemplate.jsx'
import { APP_ENV } from '@/utils/constant'

const PluginDetail = () => {
  const navigate = useNavigate()
  const { pluginId } = useParams()
  const ToolFormRef = useRef(null)
  const MassOrCozeRef = useRef(null)
  const AutoTemRef = useRef(null)
  const { formData, setFormData } = useFormStore()
  const [loading, setLoading] = useState(false)
  // const [isBuildDisabled, setIsBuildDisabled] = useState(true)
  const [isSaveDisabled, setIsSaveDisabled] = useState(true)
  const [drawerVisible, setDrawerVisible] = useState(false)
  const [pluginInfo, setPluginInfo] = useState({})
  const [massOrCozeInfo, setMassOrCozeInfo] = useState({})
  const [autoTemInfo, setAutoTemInfo] = useState({})

  const [pluginName, setPluginName] = useState(null)
  const [pluginDesc, setPluginDesc] = useState(null)

  const [onlyTypeForm] = Form.useForm()
  const [pluginTypeOption, setPluginTypeOption] = useState([])

  const [disabled, setDisabled] = useState(false)

  const pluginType = Form.useWatch('pluginType', onlyTypeForm)

  const [isFormChanged, setIsFormChanged] = useState(false)

  const closeDrawer = () => {
    setDrawerVisible(false)
    setIsSaveDisabled(true)
  }

  const buildPlugin = () => {
    setLoading(true)
    ajax({
      url: '/aiui-agent/plugin/build',
      data: {
        pluginId: pluginId
      },
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('构建成功')
          setLoading(false)
        }
      })
      .catch((err) => {
        setLoading(false)
      })
  }

  const gotoNext = async () => {
    if (ToolFormRef.current) {
      const values = await ToolFormRef.current.validate()
      if (values) {
        setFormData(values)
        setDrawerVisible(true)
        console.log(formData, '点击保存并继续的formData')
      }
    }
  }

  //  开放api 就是工具配置那一块
  const saveOpenApiTool = async () => {
    if (ToolFormRef.current) {
      const values = await ToolFormRef.current.validate()
      if (values) {
        console.log(values, '这个是http协议的表单的values')
        let schemaValue = {
          toolRequestInput: values.toolRequestInput || [],
          toolRequestOutput: values.toolRequestOutput || []
        }
        let params
        params = {
          pluginType: pluginType,
          pluginId: pluginId,
          pluginName: pluginName,
          pluginDesc: pluginDesc,
          endPoint: values.endPoint,
          authType: values.authType,
          method: values.method,
          webSchema: JSON.stringify(schemaValue),
          config: JSON.stringify({
            openapi: {
              template: {}
            }
          })
        }
        if (values.authType === 2) {
          params = {
            ...params,
            authInfo: JSON.stringify({
              location: values?.location,
              parameterName: values?.parameterName,
              serviceToken: values?.serviceToken
            })
          }
          delete params['location']
          delete params['parameterName']
          delete params['serviceToken']
        }
        ajax({
          url: '/aiui-agent/plugin/save',
          data: params,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code === '0') {
              message.success('构建成功')
              getPluginDetail()
              if (APP_ENV === 'base') {
                navigate('/workspace/plugin')
              } else {
                navigate('/plugin')
              }
            }
          })
          .catch((err) => {
            message.warning('构建失败')
            getPluginDetail()
          })
      }
    }
  }

  //   保存汽车模版
  const saveAutoTemplate = async () => {
    if (AutoTemRef.current) {
      const values = await AutoTemRef.current.validate()
      if (values) {
        const params = {
          config: JSON.stringify({
            auto_template: {
              url: values.url,
              method: values.method,
              auth: {
                type: values.type,
                key: values.key
              }
            }
          }),
          pluginId: pluginId,
          pluginType: pluginType
        }
        ajax({
          url: '/aiui-agent/plugin/save',
          data: params,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code == 0) {
              message.success('保存成功')
              getPluginDetail()
              setIsFormChanged(false)
            }
          })
          .catch((err) => {})
      }
    }
  }

  const saveMassOrCoze = async () => {
    if (MassOrCozeRef.current) {
      const values = await MassOrCozeRef.current.validate()
      if (values) {
        console.log(values, '这个是后处理表单的values')
        let params

        params = {
          pluginId: pluginId,
          thirdPartyInfo: {
            httpMethod: values.httpMethod,
            platform: values.platform,
            resourceType: values.resourceType,
            resourceUrl: values.resourceUrl,
            webSchema: JSON.stringify({
              toolRequestInput: values.toolRequestInput || [],
              toolRequestOutput: values.toolRequestOutput || [],
              userAddedFields: {
                input: values.toolRequestInput
                  .filter((item) => item.isUserAdded)
                  .map((item) => item.name),
                outPut: values.toolRequestOutput
                  .filter((item) => item.isUserAdded)
                  .map((item) => item.name)
              }
            })
          }
        }
        // if (pluginType === 3) {
        //   params = {
        //     config: JSON.stringify({
        //       coze_template: {
        //         ...values
        //       }
        //     }),
        //     pluginId: pluginId,
        //     pluginType: pluginType
        //   }
        // } else if (pluginType === 4) {
        //   params = {
        //     config: JSON.stringify({
        //       maas_template: {
        //         ...values
        //       }
        //     }),
        //     pluginId: pluginId,
        //     pluginType: pluginType
        //   }
        // }
        ajax({
          url: '/aiui-agent/plugin/third/save',
          data: params,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code == 0) {
              message.success('保存成功')
              getPluginDetail()
              setIsFormChanged(false)
            }
          })
          .catch((err) => {})
      }
    }
  }

  const debugSuccess = () => {
    // 调试成功，放开保存按钮可以
    setIsSaveDisabled(false)
  }

  const getPluginDetail = () => {
    ajax({
      url: '/aiui-agent/plugin/detail',
      data: {
        pluginId: pluginId
      },
      method: 'post'
    }).then((res) => {
      if (res.data.code === '0') {
        console.log(res, '这个是拿到plugin详情的res')
        const pluginDetail = res.data.data
        onlyTypeForm.setFieldsValue({
          pluginType: 1 // 如果是创建进来 给一个默认值
        })
        setPluginName(pluginDetail.pluginName)
        setPluginDesc(pluginDetail.pluginDesc)

        if (pluginDetail?.pluginType === 1) {
          //  有authInfo 是授权的
          onlyTypeForm.setFieldsValue({
            pluginType: pluginDetail?.pluginType
          })
          setDisabled(true)
          if (pluginDetail?.toolConfig?.authInfo) {
            setFormData({
              authType: pluginDetail?.toolConfig?.authType,
              endPoint: pluginDetail?.toolConfig?.endPoint,
              method: pluginDetail?.toolConfig?.method,
              toolRequestInput: pluginDetail?.toolConfig
                ? JSON.parse(pluginDetail?.toolConfig?.webSchema)?.toolRequestInput
                : [],
              toolRequestOutput: pluginDetail?.toolConfig
                ? JSON.parse(pluginDetail?.toolConfig?.webSchema)?.toolRequestOutput
                : [],
              location: JSON.parse(pluginDetail?.toolConfig?.authInfo)?.location,
              parameterName: JSON.parse(pluginDetail?.toolConfig?.authInfo)?.parameterName,
              serviceToken: JSON.parse(pluginDetail?.toolConfig?.authInfo)?.serviceToken
            })
          } else {
            setFormData({
              authType: pluginDetail?.toolConfig?.authType,
              endPoint: pluginDetail?.toolConfig?.endPoint,
              method: pluginDetail?.toolConfig?.method,
              toolRequestInput: pluginDetail?.toolConfig
                ? JSON.parse(pluginDetail?.toolConfig?.webSchema)?.toolRequestInput
                : [],
              toolRequestOutput: pluginDetail?.toolConfig
                ? JSON.parse(pluginDetail?.toolConfig?.webSchema)?.toolRequestOutput
                : []
            })
          }
        }

        if (pluginDetail?.pluginType === 3) {
          onlyTypeForm.setFieldsValue({
            pluginType: pluginDetail?.pluginType
          })
          setDisabled(true)
          setMassOrCozeInfo(JSON.parse(pluginDetail?.extraParam || '{}'))
        }

        if (pluginDetail?.pluginType === 4) {
          onlyTypeForm.setFieldsValue({
            pluginType: pluginDetail?.pluginType
          })
          setDisabled(true)
          setMassOrCozeInfo(JSON.parse(pluginDetail?.extraParam || '{}'))
        }

        // 汽车模版，官方账号专有
        if (pluginDetail?.pluginType === 0) {
          onlyTypeForm.setFieldsValue({
            pluginType: pluginDetail?.pluginType
          })
          setDisabled(true)
          setAutoTemInfo(JSON.parse(pluginDetail?.extraParam || '{}'))
        }
      }
    })
  }

  const doBack = () => {
    setFormData({})
    if (APP_ENV === 'base') {
      navigate('/workspace/plugin')
    } else if (APP_ENV === 'auto') {
      navigate('/plugin')
    }
  }

  useEffect(() => {
    getPluginDetail()
  }, [pluginId])

  const getPluginTypeList = () => {
    const url = '/aiui-agent/plugin/type/all'
    ajax({
      url,
      method: 'get'
    })
      .then((res) => {
        if (res.data.code === '0') {
          const list = res.data.data.map((item) => {
            return {
              label: item.name,
              value: item.code
            }
          })
          setPluginTypeOption(list)
        }
      })
      .catch((err) => {})
  }

  const getBtnName = () => {
    // pluginType === 1 ? '构建插件' : '保存'
    if (pluginType === 1) {
      return APP_ENV === 'base' ? '构建插件' : '构建智能体'
    } else {
      return '保存'
    }
  }

  useEffect(() => {
    getPluginTypeList()
  }, [])

  return (
    <div style={{ paddingBottom: '20px', background: '#fff' }}>
      <div style={{ display: 'flex', marginBottom: '15px' }}>
        <div className={styles['pluginHeader']}>
          <div className={styles['return']} onClick={doBack}>
            <ArrowLeftOutlined />
          </div>
          我的{APP_ENV === 'base' ? '插件' : '智能体'}
          <span className={styles['division']}>/</span>
          <span className={styles['pluginName']}>{pluginName}</span>
        </div>
        {/* <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <IconGoback onClick={doBack} style={{ cursor: 'pointer' }}></IconGoback>
          <h2 style={{ fontWeight: 600, fontSize: '24px', marginLeft: '10px' }}> {pluginName} </h2>
        </div> */}
        <Space style={{ marginLeft: 'auto' }} size={10}>
          {pluginType === 1 && (
            <Button onClick={gotoNext} type="primary">
              保存并继续
            </Button>
          )}

          {/* {pluginType === 1 && (
            <Button onClick={saveOpenApiTool} type="primary">
              保存
            </Button> // openApi 工具集
          )} */}

          {(pluginType === 3 || pluginType === 4) && (
            <Button type="primary" onClick={saveMassOrCoze}>
              保存
            </Button> // 这个是星辰或者coze的
          )}

          {pluginType === 0 && (
            <Button type="primary" onClick={saveAutoTemplate}>
              保存
            </Button> // 这个是保存汽车的 http Auto模版
          )}

          {pluginType !== 1 && (
            <Button
              type="primary"
              onClick={buildPlugin}
              className={styles.buildBtn}
              loading={loading}
              disabled={isFormChanged}
            >
              {APP_ENV === 'base' ? '构建插件' : '构建智能体'}
            </Button>
          )}
        </Space>
      </div>

      <div className={styles['cardBox']}>
        <Form form={onlyTypeForm} labelAlign="right" layout="vertical">
          <Form.Item
            label="来源:"
            name="pluginType"
            rules={[{ required: true, message: '请选择协议类型' }]}
          >
            <Select
              placeholder="请选择协议类型"
              options={pluginTypeOption}
              disabled={disabled}
            ></Select>
          </Form.Item>
        </Form>

        {/* 开放api --测试工具那一块 */}
        {pluginType === 1 && (
          <TestToolForm ref={ToolFormRef} formData={formData} pluginInfo={pluginInfo} />
        )}

        {/*  星辰或者coze 表单  */}
        {/* {(pluginType === 3 || pluginType === 4) && (
        <CozeOrMassForm
          ref={MassOrCozeRef}
          massOrCozeInfo={massOrCozeInfo}
          onFormValuesChange={(changed) => setIsFormChanged(changed)}
        />
      )} */}

        {(pluginType === 3 || pluginType === 4) && (
          <NewCozeAndMass
            ref={MassOrCozeRef}
            massOrCozeInfo={massOrCozeInfo}
            pluginType={pluginType}
          />
        )}

        {/* 汽车模版  只有官方账号能看到的 */}
        {pluginType === 0 && (
          <AutoTemForm
            ref={AutoTemRef}
            autoTemInfo={autoTemInfo}
            onFormValuesChange={(changed) => setIsFormChanged(changed)}
          />
        )}
      </div>

      <Drawer
        title="调试"
        open={drawerVisible}
        onClose={closeDrawer}
        size="large"
        extra={
          <Button onClick={saveOpenApiTool} type="primary" disabled={isSaveDisabled}>
            {getBtnName()}
          </Button>
        }
      >
        <DebugForm debugSuccess={debugSuccess} />
      </Drawer>
    </div>
  )
}

export default PluginDetail
