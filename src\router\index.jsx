import { createB<PERSON><PERSON><PERSON><PERSON><PERSON>, RouterProvider, Navigate } from 'react-router-dom'
import Login from '@/pages/Login'
import Root from '@/pages/Root'
import Home from '@/pages/Home'
import Workspace from '@/pages/Workspace'
import Plugin from '@/pages/Plugin/plugin.jsx'
import Intent from '@/pages/Intent/index.jsx'
import Corpus from '@/pages/Intent/Corpus.jsx'
import RepoList from '@/pages/Repository'
import RepoModule from '@/pages/Repository/Detail'
import RepoBaseInfo from '@/pages/Repository/Detail/BaseInfo'
import RepoDoc from '@/pages/Repository/Detail/DocManage'
import PointManage from '../pages/Repository/Detail/PointManage'
import PluginDetail from '@/pages/Plugin/PluginDetail.jsx'

import Agent from '@/pages/Agent'
import AgentDetail from '@/pages/Agent/AgentDetail'

import Workflow from '@/pages/Workflow'
import AgentConfig from '@/pages/Agent/AgentDetail/index-auto.jsx'

import ToolAuto from '@/pages/ToolAuto/index.jsx'
import ToolAutoDetail from '@/pages/ToolAuto/PluginDetail.jsx'
import EntityDetail from '@/pages/Intent/Entity/index.jsx'

import NotFound from '@/pages/NotFound'

export const router = createBrowserRouter(
  [
    {
      path: '/login',
      element: <Login />
    },

    {
      path: '/',
      element: <Navigate to="/workspace/agent" replace />
    },
    {
      path: '/workspace',
      element: <Root />,
      children: [
        {
          path: '',
          element: <Workspace />,
          children: [
            { index: true, element: <Agent /> },
            { path: 'repo', element: <RepoList /> },
            { path: 'agent', element: <Agent /> },
            { path: 'flow', element: <Workflow /> },
            { path: 'plugin', element: <Plugin /> },
            {
              path: 'plugin/:pluginId/detail',
              element: <PluginDetail />
            },
            {
              path: 'intent',
              children: [{ index: true, element: <Intent /> }]
            },
            { path: 'intent/:intentId/corpus', element: <Corpus /> },

            //  目前在 本地测试tool
            {
              path: 'tool',
              children: [{ index: true, element: <ToolAuto /> }]
            }
          ]
        }
      ]
    },
    { path: '/workspace/agent/:agentId', element: <AgentDetail /> },
    {
      path: 'entityDeatail/:entityId',
      element: <EntityDetail />
    },
    // 以下是给汽车用
    {
      path: '/repo',
      element: <Root />,
      children: [
        { path: '', element: <RepoList /> },
        {
          path: ':repoId',
          element: <RepoModule />,
          children: [
            { index: true, element: <RepoBaseInfo /> },
            { path: 'baseInfo', element: <RepoBaseInfo /> },
            { path: 'docManage', element: <RepoDoc /> },
            { path: 'chunkManage', element: <PointManage /> }
          ]
        }
      ]
    },

    {
      path: '/plugin',
      children: [{ index: true, element: <Plugin /> }]
    },
    {
      path: '/plugin/:pluginId/detail',
      element: <PluginDetail />
    },

    {
      path: '/tool',
      children: [{ index: true, element: <ToolAuto /> }]
    },
    {
      path: '/tool/:pluginId/detail',
      element: <ToolAutoDetail />
    },

    {
      path: '/intent',
      children: [{ index: true, element: <Intent /> }]
    },
    { path: '/intent/:intentId/corpus', element: <Corpus /> },

    {
      path: '/agentConfig',
      element: <AgentConfig />
    },
    // 以上是给汽车用

    // 其他路由...
    {
      path: '*',
      element: <NotFound />
    }
  ],
  { basename: `${import.meta.env.VITE_ROUTER_BASE_URL}` }
)
