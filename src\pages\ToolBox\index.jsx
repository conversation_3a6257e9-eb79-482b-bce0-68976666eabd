import React, { useState, useEffect } from 'react'
import { Input, Button, Spin, Row, Col, Card } from 'antd'
import { PlusOutlined, ToolOutlined, SearchOutlined } from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import useFormStore from './formData.js'

const ToolBox = () => {
  // const tools = [
  //   { id: 1, name: '主流程构建接口', description: '描述', users: 88 },
  //   { id: 2, name: '数据分析工具', description: '用于分析数据', users: 120 },
  //   { id: 3, name: 'API 连接器', description: '用于 API 集成', users: 56 }
  // ]
  const navigate = useNavigate()
  const { setFormData } = useFormStore()

  const [searchval, setSearchval] = useState('')
  const [tools, setTools] = useState([])
  const [pageData, setPageData] = useState({
    pageIndex: 1,
    pageSize: 8
  })
  const [total, setTotal] = useState(0)

  const inputSearch = () => {
    console.log('inputSearch', searchval)
  }

  const gotoCreate = () => {
    setFormData({})
    navigate('/toolbox/create')
  }
  const gotoDetail = (tool) => {
    setFormData({})
    navigate(`/toolbox/create?tool=${tool.toolId}`)
  }

  const getTools = () => {
    ajax({
      url: '/aiui-agent/aiui-tool/tool/toolPage',
      data: {
        ...pageData,
        toolSquare: false
      },
      method: 'post'
    }).then((res) => {
      console.log('res', res.data)
      if (res.data.code == 0) {
        console.log('result', res.data.data.result)

        setTools(res.data.data.result)
        setTotal(res.data.data.totalSize)
      }
    })
  }

  useEffect(() => {
    getTools()
  }, [])

  return (
    <div>
      <Input
        className={styles.input}
        placeholder="请输入"
        prefix={<SearchOutlined style={{ color: '#bbb' }} />}
        onKeyUp={inputSearch}
      ></Input>

      <Row gutter={[16, 16]} wrap className={styles.list}>
        <Col span={6}>
          <Card hoverable style={{ textAlign: 'center', height: 150 }} onClick={gotoCreate}>
            <PlusOutlined style={{ fontSize: 32, color: '#1677ff' }} />
            <div style={{ fontWeight: 'bold', marginTop: 10 }}>新增工具</div>
          </Card>
        </Col>

        {tools.map((tool) => (
          <Col span={6} key={tool.id} onClick={() => gotoDetail(tool)}>
            <Card hoverable style={{ height: 150 }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <ToolOutlined style={{ fontSize: 24, color: '#1677ff', marginRight: 8 }} />
                <div>
                  <div style={{ fontWeight: 'bold' }}>{tool.toolName}</div>
                  <div style={{ fontSize: 12, color: '#888' }}>{tool.description}</div>
                  <div style={{ fontSize: 12, color: '#aaa' }}>{tool.toolUseCount} 关联应用</div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  )
}

export default ToolBox
