import { useState } from 'react'
import { <PERSON>ton, Table, Radio } from 'antd'
import PropTypes from 'prop-types'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import { APP_ENV, cssVariables } from '@/utils/constant'
import IconEdit from '@/assets/svgs/icon-edit.svg?react'

const PreConfigModal = ({ visible, onClose, onSave }) => {
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [selectedPackageId, setSelectedPackageId] = useState(null)

  // 将 configItems 转换为状态
  const [configItems, setConfigItems] = useState([
    {
      key: 'cbm_denial_auto',
      name: '大模型拒识-汽车',
      description: 'cbm_denial_auto',
      currentPackage: null
    },
    {
      key: 'cbm_tidy_auto',
      name: '语义规整-汽车',
      description: 'cbm_tidy_auto',
      currentPackage: null
    },
    {
      key: 'cbm_intent_domain_3rd',
      name: '三方落域',
      description: 'cbm_intent_domain_3rd',
      currentPackage: null
    },
    {
      key: 'cbm_intent_domain_inner_auto',
      name: '内置落域-汽车',
      description: 'cbm_intent_domain_inner_auto',
      currentPackage: null
    },
    {
      key: 'cbm_retrieval_classify_auto',
      name: '检索分类-汽车',
      description: 'cbm_retrieval_classify_auto',
      currentPackage: null
    },
    {
      key: 'cbm_reply_quick',
      name: '快速回复-汽车',
      description: 'cbm_reply_quick',
      currentPackage: null
    }
  ])

  // 预包列表数据
  const packageList = [
    {
      id: 'GZ2025031701',
      name: '智能汽车_T68_三方落域',
      version: '1.0',
      description: 'T68三方落域'
    },
    {
      id: 'GZ2025031401',
      name: '智能汽车 LK1Anev_三方落域',
      version: '1.0',
      description: 'LK1Anev三方落域'
    },
    {
      id: 'GZ2025031304',
      name: '智能汽车_坤逸_落域',
      version: '1.0',
      description: '坤逸落域'
    },
    {
      id: 'GZ2025012202',
      name: '智能汽车_奇瑞_落域',
      version: '1.1',
      description: '奇瑞落域'
    },
    {
      id: 'GZ2025011401',
      name: 'AI交互_小谷_落域',
      version: '1.1',
      description: 'AI交互小谷落域'
    }
  ]

  const handleEditClick = (item) => {
    setSelectedItem(item)
    setSelectedPackageId(item.currentPackage?.id || null)
    setEditModalVisible(true)
  }

  const handleEditModalClose = () => {
    setEditModalVisible(false)
    setSelectedItem(null)
    setSelectedPackageId(null)
  }

  const handleEditModalOk = () => {
    if (!selectedPackageId) {
      return
    }

    // 更新选中的预包信息
    const selectedPackage = packageList.find((pkg) => pkg.id === selectedPackageId)
    const updatedConfigItems = configItems.map((item) => {
      if (item.key === selectedItem.key) {
        return {
          ...item,
          currentPackage: {
            id: selectedPackage.id,
            name: selectedPackage.name
          }
        }
      }
      return item
    })

    // 更新状态
    setConfigItems(updatedConfigItems)

    // 调用保存回调
    onSave?.(updatedConfigItems)
    handleEditModalClose()
  }

  const columns = [
    {
      title: '预包编号',
      dataIndex: 'id',
      key: 'id'
    },
    {
      title: '预包名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '勾选',
      key: 'select',
      width: 80,
      render: (_, record) => (
        <Radio
          checked={selectedPackageId === record.id}
          onChange={() => setSelectedPackageId(record.id)}
        />
      )
    }
  ]

  return (
    <>
      <MyModal
        title="预配置"
        open={visible}
        onCancel={onClose}
        width={600}
        footer={null}
        destroyOnHidden
      >
        <div className={styles.container}>
          {configItems.map((item) => (
            <div key={item.key} className={styles.itemCard}>
              <div className={styles.content}>
                <div className={styles.title}>
                  {item.name}
                  <span className={styles.subtitle}>{item.description}</span>
                </div>
                {item.currentPackage && (
                  <div className={styles.selectedPackage}>已选择：{item.currentPackage.name}</div>
                )}
              </div>
              <Button
                type="link"
                className={styles.editButton}
                onClick={() => handleEditClick(item)}
                icon={<IconEdit />}
              />
            </div>
          ))}
        </div>
      </MyModal>

      <MyModal
        title="选择文法预包"
        open={editModalVisible}
        onCancel={handleEditModalClose}
        onOk={handleEditModalOk}
        width={900}
        destroyOnHidden
      >
        <div className={styles.editContainer}>
          <Table columns={columns} dataSource={packageList} pagination={false} rowKey="id" />
        </div>
      </MyModal>
    </>
  )
}

PreConfigModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func
}

export default PreConfigModal
