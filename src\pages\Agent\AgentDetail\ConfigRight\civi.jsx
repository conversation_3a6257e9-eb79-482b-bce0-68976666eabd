import {
  <PERSON>,
  Collapse,
  Button,
  Empty,
  Skeleton,
  Tooltip,
  Space,
  Row,
  Col,
  Tag,
  Switch
} from 'antd'
import { useState } from 'react'
import styles from './civi.style.module.scss'
import { RightCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { useConfigRightChina, useConfigRightResource } from './hooks/useConfigRight'
import PluginConfigModal from './PluginConfig/ConfigModal/index.jsx'
import WorkflowConfigModal from './WorkflowConfig/ConfigModal/index.jsx'
import KnowledgeConfigModal from './KnowledgeConfig/ConfigModal/index.jsx'
import SearchModal from './SearchModal/index.jsx'
import InterModal from './InterModal/index.jsx'
import AgentBeforeModal from './AgentBeforeModal'
import microAppRouter from '@/hooks/useMicroAppRouter.js'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import {
  REJECT_CONFIG_OPTION,
  MULTI_REJECT_CONFIG_OPTION,
  REJECT_CONFIG_KEYS,
  MULTI_REJECT_CONFIG_KEYS
} from './constant'

import ajax from '@/utils/http'
import { CustomEmpty } from '@/components'

const ConfigRightCIVI = () => {
  const [pluginVisible, setPluginVisible] = useState(false)

  const [workflowVisible, setWorkflowVisible] = useState(false)

  const [intentVisible, setIntentVisible] = useState(false)

  const [atomicAbilityVisible, setAtomicAbilityVisible] = useState(false)

  const [searchVisible, setSearchVisible] = useState(false)

  const [agentBeforeVisible, setAgentBeforeVisible] = useState(false)

  const [agentAfterVisible, setAgentAfterVisible] = useState(false)

  const { baseRouter } = microAppRouter()

  const setRouterId = useAgentDetailStore((state) => state.setRouterId)
  const setRouter = useAgentDetailStore((state) => state.setRouter)
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  // 链路
  const { chains: chainOptions, currentChain } = useConfigRightChina()

  const router = useAgentDetailStore((state) => state.router)

  // 插件
  const {
    list: pluginOptions,
    totalCount: pluginSelectedNum,
    loading: pluginLoading,
    fetchResources: pluginFetchResource
  } = useConfigRightResource('plugin', true)

  // 多阶插件
  const {
    list: multiPluginOptionsTemp,
    totalCount: multiPluginSelectedNum,
    loading: multiPluginLoading,
    fetchResources: multiPluginFetchResource
  } = useConfigRightResource('multiPlugin', router?.auth?.multiPlugin === 1)

  const multiPluginOptions = multiPluginOptionsTemp.map((item) => ({ ...item, multi: true }))

  // 知识库
  const {
    list: intentOptions,
    totalCount: intentSelectedNum,
    loading: intentLoading,
    fetchResources: intentFetchResource
  } = useConfigRightResource('intent', true)

  // 工作流
  const {
    list: workflowOptions,
    totalCount: workflowSelectedNum,
    loading: workflowLoading,
    fetchResources: workflowFetchResource
  } = useConfigRightResource('workflow', true)

  // 原子能力
  const {
    list: atomicAbilityOptions,
    totalCount: atomicAbilitySelectedNum,
    loading: atomicAbilityLoading,
    fetchResources: atomicAbilityFetchResource
  } = useConfigRightResource('atomicAbility', true)

  // 搜索配置
  const {
    list: searchOptions,
    totalCount: searchSelectedNum,
    loading: searchLoading,
    fetchResources: searchFetchResource
  } = useConfigRightResource('search', true)

  // topic 话题生成
  const {
    list: topicOptions,
    loading: topicLoading,
    fetchResources: topicFetchResource
  } = useConfigRightResource('topic', router?.auth?.topicGeneration === 1)

  const [topicSwitchLoading, setTopicSwitchLoading] = useState(false)

  // 模型前处理配置
  const {
    list: beforeOptions,
    loading: beforeLoading,
    fetchResources: beforeFetchResource
  } = useConfigRightResource('before', true)

  // 模型后处理配置
  const {
    list: afterOptions,
    loading: afterLoading,
    fetchResources: afterFetchResource
  } = useConfigRightResource('after', true)

  const refreshOptionsMap = new Map([
    [
      'plugin',
      {
        list: pluginOptions,
        totalCount: pluginSelectedNum,
        loading: pluginLoading,
        api: () => {
          pluginFetchResource()
          multiPluginFetchResource()
        },
        name: '智能体',
        setModalVisible: setPluginVisible
      }
    ],
    [
      'multiPlugin',
      {
        list: multiPluginOptions,
        totalCount: multiPluginSelectedNum,
        loading: multiPluginLoading,
        api: multiPluginFetchResource,
        name: '智能体',
        setModalVisible: setPluginVisible
      }
    ],
    [
      'intent',
      {
        list: intentOptions,
        totalCount: intentSelectedNum,
        loading: intentLoading,
        api: intentFetchResource,
        name: '知识库',
        setModalVisible: setIntentVisible
      }
    ],
    [
      'workflow',
      {
        list: workflowOptions,
        totalCount: workflowSelectedNum,
        loading: workflowLoading,
        api: workflowFetchResource,
        name: '工作流',
        setModalVisible: setWorkflowVisible
      }
    ],
    [
      'atomicAbility',
      {
        list: atomicAbilityOptions,
        totalCount: atomicAbilitySelectedNum,
        loading: atomicAbilityLoading,
        api: atomicAbilityFetchResource,
        name: '原子能力配置',
        setModalVisible: setAtomicAbilityVisible
      }
    ],
    [
      'search',
      {
        list: searchOptions,
        totalCount: searchSelectedNum,
        loading: searchLoading,
        api: searchFetchResource,
        name: '搜索配置',
        setModalVisible: setSearchVisible
      }
    ],
    [
      'before',
      {
        list: beforeOptions,
        totalCount: beforeOptions.length,
        loading: beforeLoading,
        api: beforeFetchResource,
        name: '模型前处理配置',
        setModalVisible: setAgentBeforeVisible
      }
    ],
    [
      'after',
      {
        list: afterOptions,
        totalCount: afterOptions.length,
        loading: afterLoading,
        api: afterFetchResource,
        name: '模型后处理配置',
        setModalVisible: setAgentAfterVisible
      }
    ]
  ])

  // 打开模态窗
  const setModalVisible = (type, bool) => {
    const { setModalVisible } = refreshOptionsMap.get(type)
    setModalVisible(bool)
  }

  // 新增
  const onAddClick = (e, type) => {
    e.stopPropagation()
    setModalVisible(type, true)
  }

  // 点击查看详情跳转
  const onDetailClick = (e, id, type, name) => {
    e.stopPropagation()
    const typeMappings = {
      plugin: {
        path: '/agent/plugin',
        page: 'detail'
      },
      workflow: {
        path: '/agent/flow',
        page: ''
      },
      intent: {
        path: '/agent/repo',
        page: ''
      }
    }

    const { path, page } = typeMappings[type] || typeMappings.intent

    if (type === 'plugin') {
      baseRouter.open(`/agent/${id}/detail?agentName=${name}`)
    } else {
      baseRouter.open(`${path}?id=${id}&page=${page}`)
    }
  }

  // 模态窗保存成功后刷新接口获取最新数据
  const onModalRefresh = (type) => {
    let data = window.microApp?.getData() || {}
    const { appid, scene } = data
    window.microApp?.dispatch({
      type: 'CONFIG_CHANGE',
      data: {
        appid,
        scene,
        isChange: true
      }
    })

    const { api } = refreshOptionsMap.get(type)

    return api()
  }

  const CollapseExtra = (type, num) => (
    <Button onClick={(e) => onAddClick(e, type)} icon={<PlusOutlined />}>
      {num ? '已配置' : '未配置'} {num > 0 ? num : ''}
    </Button>
  )

  const CollapseItem = (x, type) => (
    <div
      key={x.id}
      className="bg-[#F7F8FA] px-[16px] py-[12px] rounded-[4px] flex items-center justify-between hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] transition-shadow duration-300"
    >
      <span>{x.name}</span>
      <Tooltip title={x.official ? '官方插件禁止跳转' : ''}>
        <Button
          disabled={!!x.official}
          onClick={(e) => onDetailClick(e, x.id, type, x.name)}
          type="link"
          className="!p-[0px] text-[0.12rem]"
        >
          查看
        </Button>
      </Tooltip>
    </div>
  )

  const CollapseChildrenItem = (list, type, ItemComponent) => {
    return (
      <div className="space-y-[10px]">
        {ItemComponent ? ItemComponent(list) : list.map((x) => CollapseItem(x, type))}
      </div>
    )
  }

  const CollapseChildren = (list, loading, type, ItemComponent) => {
    return (
      <Skeleton active loading={loading}>
        {list.length ? CollapseChildrenItem(list, type, ItemComponent) : <CustomEmpty />}
      </Skeleton>
    )
  }

  // 搜索配置展开项
  const SearchCollapseChildrenItem = (list) => {
    return (
      <Row
        className="bg-[#F7F8FA] px-[16px] py-[12px] rounded-[4px] w-full hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] transition-shadow duration-300"
        size={40}
      >
        {list.map((x) => (
          <Col span={7} key={x.id}>
            {x.name}
          </Col>
        ))}
      </Row>
    )
  }

  // 插件配置展开项
  const PluginCollapseChildrenItem = (list) => {
    return list.map((x) => {
      return (
        <div
          key={x.id}
          className="bg-[#F7F8FA] px-[16px] py-[12px] rounded-[4px] flex items-center justify-between hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] transition-shadow duration-300"
        >
          <div>
            <span>{x.name}</span>
            <Tag color={x.multi ? 'blue' : 'green'} style={{ marginLeft: 2 }}>
              {x.multi ? '多阶' : '单阶'}
            </Tag>
          </div>

          <Tooltip title={x.official || x.multi ? '官方插件禁止跳转' : ''}>
            <Button
              disabled={!!x.official || !!x.multi}
              onClick={(e) => onDetailClick(e, x.id, type, x.name)}
              type="link"
              className="!p-[0px] text-[0.12rem]"
            >
              查看
            </Button>
          </Tooltip>
        </div>
      )
    })
  }

  // 原子能力配置展开项
  const AtomicAbilityCollapseChildrenItem = (list) => {
    return list.map((x) => {
      const { abilityCode, packages, abilityName, interventions } = x
      const { number, name, version } = packages?.find((y) => y.id == interventions?.[0]?.id) || {}

      const rejects = REJECT_CONFIG_KEYS.filter((y) => x[y] == 2)
      const multiRejects = MULTI_REJECT_CONFIG_KEYS.filter((y) => x[y] == 2)

      return (
        <Row
          className="bg-[#F7F8FA] px-[16px] py-[12px] rounded-[4px] w-full hover:shadow-[0_2px_8px_rgba(0,0,0,0.15)] transition-shadow duration-300 items-center"
          key={x.id}
        >
          <Col span={7} className="flex flex-col justify-center">
            <p>{abilityName}</p>
            <p className="mt-[4px]">{abilityCode}</p>
          </Col>
          <Col span={7} className="flex items-center">
            <Space>
              <span>{number}</span>
              <span>{name}</span>
              <span>{version}</span>
            </Space>
          </Col>
          <Col span={10} className="flex items-center">
            <Space className="flex flex-col items-start w-full">
              {rejects.length > 0 && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ minWidth: 70 }}>单人拒识：</div>
                  <div style={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    {rejects.map((y) => (
                      <Tag color="processing" className="text-[12px] mr-[2px]" key={y}>
                        {REJECT_CONFIG_OPTION.find((z) => z.value == y)?.label}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

              {multiRejects.length > 0 && (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <div style={{ minWidth: 70 }}>多人拒识：</div>
                  <div style={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    {multiRejects.map((y) => (
                      <Tag color="green" className="text-[12px] mr-[2px]" key={y}>
                        {MULTI_REJECT_CONFIG_OPTION.find((z) => z.value == y)?.label}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}
            </Space>
          </Col>
        </Row>
      )
    })
  }

  const CollapseLabel = (type, num) => {
    const { name } = refreshOptionsMap.get(type)
    return (
      <div className="flex items-center">
        <span className="mr-1">{name}</span> {CollapseExtra(type, num)}
      </div>
    )
  }

  const resourceCollapseItems = [
    {
      key: 'plugin',
      label:
        router?.auth?.multiPlugin === 1
          ? CollapseLabel('plugin', pluginSelectedNum + multiPluginSelectedNum)
          : CollapseLabel('plugin', pluginSelectedNum),
      children:
        router?.auth?.multiPlugin === 1
          ? CollapseChildren(
              [...pluginOptions, ...multiPluginOptions],
              pluginLoading || multiPluginLoading,
              'plugin',
              PluginCollapseChildrenItem
            )
          : CollapseChildren(pluginOptions, pluginLoading, 'plugin', PluginCollapseChildrenItem),
      expand: true
    },
    {
      key: 'intent',
      label: CollapseLabel('intent', intentSelectedNum),
      children: CollapseChildren(intentOptions, intentLoading, 'intent'),
      expand: true
    },
    {
      key: 'workflow',
      label: CollapseLabel('workflow', workflowSelectedNum),
      children: CollapseChildren(workflowOptions, workflowLoading, 'workflow'),
      expand: true
    }
  ]

  // 存在拒识或者干预包的展示出来
  const filterAtomicAbilityOptions = atomicAbilityOptions.filter(
    (x) =>
      !!x.interventionSelected ||
      REJECT_CONFIG_KEYS.some((y) => x[y] == 2) ||
      MULTI_REJECT_CONFIG_KEYS.some((y) => x[y] == 2)
  )

  const onTopCheckedChange = async (checked) => {
    console.log('checked', checked)
    setTopicSwitchLoading(true)
    try {
      const res = await ajax({
        url: '/bot/config/saveAbleAndSearchConfig',
        data: {
          botId: agentDetail?.boxBot?.botId,
          topicGeneration: checked ? 1 : 0
        },
        method: 'post'
      })
      if (res.data.code === '0') {
        topicFetchResource()
      }
    } catch (e) {
    } finally {
      setTopicSwitchLoading(false)
    }
  }
  const advanceCollapseItems = [
    {
      key: 'atomicAbility',
      label: CollapseLabel('atomicAbility', filterAtomicAbilityOptions.length),
      children: CollapseChildren(
        filterAtomicAbilityOptions,
        atomicAbilityLoading,
        atomicAbilityFetchResource,
        AtomicAbilityCollapseChildrenItem
      ),
      expand: true
    },
    {
      key: 'search',
      label: CollapseLabel('search', searchSelectedNum ? 1 : 0),
      children: CollapseChildren(
        searchOptions,
        searchLoading,
        searchFetchResource,
        SearchCollapseChildrenItem
      ),
      expand: true
    },

    router?.auth?.topicGeneration === 1
      ? {
          key: 'topic',
          label: (
            <div className="flex items-center">
              <span className="mr-1">{'话题生成'}</span>
              <Switch
                loading={topicSwitchLoading}
                value={topicOptions[0] === 1}
                onChange={onTopCheckedChange}
              />
            </div>
          ),
          children: [],
          expand: false,
          showArrow: false,
          collapsible: 'icon'
        }
      : null,

    {
      key: 'before',
      label: CollapseLabel('before', beforeOptions.length),
      children: [],
      expand: false,
      showArrow: false,
      collapsible: 'icon'
    },
    {
      key: 'after',
      label: CollapseLabel('after', afterOptions.length),
      children: [],
      expand: false,
      showArrow: false,
      collapsible: 'icon'
    }
  ].filter(Boolean)

  const moduleList = [
    {
      key: 'resource',
      label: '资源配置',
      collapseItems: resourceCollapseItems,
      defaultCollapseActive: resourceCollapseItems.filter((x) => x.expand).map((x) => x.key)
    },
    {
      key: 'advance',
      label: '高级配置',
      collapseItems: advanceCollapseItems,
      defaultCollapseActive: advanceCollapseItems.filter((x) => x.expand).map((x) => x.key)
    }
  ]

  const onRouterChange = (value) => {
    ajax({
      url: '/bot/config/saveAgentRouterConfig',
      data: {
        routerId: value,
        botId: agentDetail?.boxBot?.botId
      },
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          setRouterId(value)
          setRouter(chainOptions.find((c) => c.chain_code === value))
        }
      })
      .catch((err) => {})
  }

  return (
    <div className="relative">
      <Select
        className={`${styles['civi-config-right__select']} absolute right-0 top-[8px]`}
        value={router?.chain_code}
        options={chainOptions}
        onChange={onRouterChange}
        placeholder="请选择链路"
      ></Select>
      {moduleList.map((module) => {
        return (
          <div key={module.key} className={styles['civi-config-right']}>
            <div className={styles['civi-config-right__header']}>
              <span className={styles['civi-config-right__title']}>{module.label}</span>
            </div>
            <Collapse
              className="!bg-[#fff]"
              defaultActiveKey={module.defaultCollapseActive}
              expandIcon={({ isActive }) => (
                <RightCircleOutlined className="text-[0.16rem]" rotate={isActive ? 90 : 0} />
              )}
              items={module.collapseItems}
            />
          </div>
        )
      })}

      {/* 插件新增弹窗 */}
      <PluginConfigModal
        visible={pluginVisible}
        onClose={() => setModalVisible('plugin', false)}
        onRefresh={() => onModalRefresh('plugin')}
      />
      {/* 工作流弹窗 */}
      <WorkflowConfigModal
        visible={workflowVisible}
        onClose={() => setModalVisible('workflow', false)}
        onRefresh={() => onModalRefresh('workflow')}
      />
      {/* 知识库弹窗 */}
      <KnowledgeConfigModal
        visible={intentVisible}
        onClose={() => setModalVisible('intent', false)}
        onRefresh={() => onModalRefresh('intent')}
      />
      {/* 搜索配置弹窗 */}
      <SearchModal
        visible={searchVisible}
        onClose={() => setModalVisible('search', false)}
        onRefresh={() => onModalRefresh('search')}
      />
      {/* 干预配置弹窗 */}
      <InterModal
        visible={atomicAbilityVisible}
        onClose={() => setModalVisible('atomicAbility', false)}
        onRefresh={() => onModalRefresh('atomicAbility')}
      />
      {/* 模型前处理配置弹窗 */}
      <AgentBeforeModal
        visible={agentBeforeVisible}
        type={2}
        onClose={() => setModalVisible('before', false)}
        onRefresh={() => onModalRefresh('before')}
      />
      {/* 模型后处理配置弹窗 */}
      <AgentBeforeModal
        visible={agentAfterVisible}
        type={3}
        onClose={() => setModalVisible('after', false)}
        onRefresh={() => onModalRefresh('after')}
      />
    </div>
  )
}

export default ConfigRightCIVI
