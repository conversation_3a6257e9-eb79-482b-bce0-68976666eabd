import { Modal } from 'antd'
import IconArrowRight from 'assets/svgs/arrow-right.svg?react'
import { APP_ENV } from '@/utils/constant'

const MyModal = (props) => {
  const baseFooterStyles = {
    footer: {
      background: '#fafafa',
      borderTop: '1px solid #eaeaea',
      height: '68px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-end',
      padding: '0 24px',
      margin: '0 -24px -24px -24px', // 负边距来抵消弹窗的内边距，使背景色延伸到完整宽度
      borderBottomLeftRadius: '8px', // 添加底部左圆角
      borderBottomRightRadius: '8px' // 添加底部右圆角
    }
  }

  const autoFooterStyles = {
    footer: {
      textAlign: 'center'
    }
  }
  return (
    <Modal
      styles={APP_ENV === 'auto' ? autoFooterStyles : baseFooterStyles}
      cancelButtonProps={{ style: { display: 'none' } }}
      okButtonProps={
        APP_ENV === 'base'
          ? {
              icon: <IconArrowRight style={{ lineHeight: 18 }} />,
              iconPosition: 'end'
            }
          : null
      }
      {...props}
    />
  )
}

export default MyModal
