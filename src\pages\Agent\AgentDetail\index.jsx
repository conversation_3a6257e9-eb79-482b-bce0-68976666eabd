import {
  SaveOutlined,
  WarningOutlined,
  FormOutlined,
  CheckOutlined,
  <PERSON><PERSON>eftOutlined,
  LeftOutlined,
  LoadingOutlined,
  DownOutlined
} from '@ant-design/icons'
import { Button, Space, Dropdown } from 'antd'
import IconPublish from 'assets/svgs/publish.svg?react'
import ConfigRight from './ConfigRight'
import ConfigLeft from './ConfigLeft'
import Debug from './Debug'
import PublishModal from './PublishModal'
import styles from './style.module.scss'
import { useEffect, useState } from 'react'
import ajax from '@/utils/http'
import { useParams, useNavigate } from 'react-router-dom'
import { useAgentDetailStore } from './store'
import dayjs from 'dayjs'

function AgentDetail() {
  const { agentId } = useParams()
  const setAgentDetail = useAgentDetailStore((state) => state.setAgentDetail)
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const setRouterId = useAgentDetailStore((state) => state.setRouterId)
  const setRouter = useAgentDetailStore((state) => state.setRouter)
  const routerId = useAgentDetailStore((state) => state.routerId)
  const agentRouter = useAgentDetailStore((state) => state.router)
  const [chainItems, setChains] = useState([])
  const [publishModalVisible, setPublishModalVisible] = useState(false)

  // 智能体详情页获取智能体详情

  useEffect(() => {
    getAgentDetail()
    return () => setAgentDetail(null) // 清理定时器
  }, [])

  useEffect(() => {
    // 获取智能体详情
    if (agentDetail?.boxBot?.botId) {
      getChains(agentDetail?.boxBot?.botId)
    }
  }, [agentDetail])

  const getAgentDetail = async () => {
    const res = await ajax({
      url: '/bot/getBotInfo',
      data: {
        id: agentId
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      console.log('agent详情页获取的agent详情信息：', res.data?.data)
      // const boxBotId = res.data?.data?.boxBot?.botId
      setAgentDetail(res.data?.data)
    }
  }

  const getChains = (boxBotId) => {
    ajax({
      url: '/bot/config/getBotChainInfo',
      data: {
        botId: boxBotId
      },
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        setChains(
          (res.data?.data || []).map((item) => {
            return {
              // ...item,
              data: item, //menu节点不能添加自定义属性，统一放到data里
              label: item.chain_name,
              value: item.chain_code,
              key: item.chain_code
            }
          })
        )
        // 设置当前agentboxId
        // const cc = (res.data?.data || []).find((item) => item.selected)?.chain_code
        const cc = (res.data?.data || []).find((item) => item.selected)
        setRouter(cc)
        setRouterId(cc.chain_code)
      }
    })
  }

  const handleChainChange = (val) => {
    setRouterId(val)
    // setRouter(chainItems.find((it) => it.chain_code === val))
    setRouter(chainItems.find((it) => it.value === val)?.data)
    console.log('router数据', chainItems, val, chainItems.find((it) => it.value === val)?.data)
    ajax({
      url: '/bot/config/saveAgentRouterConfig',
      data: {
        routerId: val,
        botId: agentDetail?.boxBot?.botId
      },
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
        }
      })
      .catch((err) => {})
  }

  const agentPublish = () => {
    setPublishModalVisible(true)
    console.log('点击发布')

    // ajax({
    //   url: '/bot/version/publish',
    //   data: {
    //     appid: agentDetail?.appId,
    //     scene: agentDetail?.boxBot?.scene,
    //   },
    //   method: 'post'
    // })
  }

  const navigate = useNavigate()
  const onBackClick = () => {
    navigate('/workspace/agent')
  }

  return (
    <div className={styles.main}>
      <div className={styles.page}>
        <div className={styles['page-top']}>
          <div className={styles['page-top-left']}>
            <div className={styles['page-title-operate']} id="page-title-operate">
              <div
                className="text-[--flow-desc-color]"
                style={{ cursor: 'pointer', marginRight: 20 }}
                onClick={onBackClick}
              >
                <LeftOutlined />
              </div>
              <div>
                <div className="flex items-center leading-[24px]">
                  <span className="mr-1">{agentDetail?.name}</span>
                  {/* <FormOutlined className="text-[--flow-desc-color]" /> */}
                </div>
                <p className={styles['page-title-operate-time']}>
                  创建于 {dayjs(agentDetail?.createTime).format('YYYY-MM-DD HH:mm:ss')}
                </p>
              </div>
            </div>
          </div>
          <div className={styles['page-top-right']}>
            <Space size={16}>
              {/* <Button icon={<IconDebug />}>调试</Button> */}

              <Button type="primary" icon={<IconPublish />} loading={false} onClick={agentPublish}>
                发布
              </Button>
            </Space>
          </div>
        </div>
        <div className={styles['page-scroll']} style={{ height: 'calc(100% - 60px)' }}>
          <div className={styles['config-pannel']}>
            <div className={styles['config-title']}>
              <span>配置</span>
              <Dropdown
                menu={{
                  items: chainItems,
                  selectable: true,
                  selectedKeys: [routerId],
                  onClick: (info) => {
                    // console.log('选中的完整数据:', info)
                    handleChainChange(info.key)
                  }
                }}
                trigger="click"
                placement="bottom"
              >
                <Space className={styles['config-chain']}>
                  {agentRouter?.chain_name}
                  <DownOutlined />
                </Space>
              </Dropdown>
            </div>
            <div className={styles['config-content']}>
              <div className={styles['config-left']}>
                <ConfigLeft />
              </div>
              <div className={styles['config-right']}>
                <ConfigRight />
              </div>
            </div>
          </div>

          <div className={styles['debug-pannel']}>
            <Debug />
          </div>
        </div>
      </div>
      {publishModalVisible && (
        <PublishModal open={publishModalVisible} setVisible={setPublishModalVisible} />
      )}
    </div>
  )
}
export default AgentDetail
