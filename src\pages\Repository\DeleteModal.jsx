import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Button,
  Checkbox,
  Radio,
  Select,
  Tooltip,
  InputNumber,
  message
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { APP_ENV, cssVariables } from '@/utils/constant'
import { MyModal } from '@/components'

const DeleteModal = (props) => {
  const { visible, setVisible, modalConfig } = props
  const [delLoading, setDelLoading] = useState(false)

  const Title = (name) => {
    return <div>确认是否删除 {modalConfig?.titleName || ''}</div>
  }
  return (
    <MyModal
      title={Title()}
      open={visible}
      onCancel={() => setVisible(false)}
      destroyOnHidden
      width={460}
      okButtonProps={{
        loading: delLoading
      }}
      onOk={async () => {
        if (delLoading) return
        setDelLoading(true)
        try {
          await modalConfig?.delAction?.()
          setDelLoading(false)
          setVisible(false) // 可选：关闭modal
        } catch (err) {
          setVisible(false)
          setDelLoading(false)
        }
      }}
    >
      {modalConfig?.content || ''}
    </MyModal>
  )
}

export default DeleteModal
