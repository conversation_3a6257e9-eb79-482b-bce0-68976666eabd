@import 'reset-css/reset.css';
@import './App.scss';
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #4d53e8;
  --second-color: rgba(0, 0, 0, 0.8);
  --desc-color: #667085;
  --title-color: #101828;
  --title-color2: #354052;
  --content-color: #495464;
  --placeholder-color: #98a2b2;
  --bg-color: #f5f6fb;
  --bg-color2: #f9fafb;
  --bg-color3: #fcfcfd;
  --bg-color4: #f1f7ff;
  --success-color: rgba(0, 178, 60, 1);
}

/* 根据 APP_ENV 设置表单 label 样式 */
[data-app-env='base'] .ant-form-item-label > label {
  color: #111827;
  font-weight: 600;
}

[data-app-env='auto'] .ant-form-item-label > label {
  /* 保持默认样式，不需要额外设置 */
}

html,
body {
  height: 100%;
}
img {
  display: inline-block;
}
