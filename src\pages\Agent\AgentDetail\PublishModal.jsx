import { useEffect, useState } from 'react'
import { MyModal } from '@/components'
import styles from './style.module.scss'
import { Form, Space, Input, message } from 'antd'
import ajax from '@/utils/http'
import { useParams } from 'react-router-dom'

const PublishModal = ({ open, setVisible }) => {
  const { agentId } = useParams()
  const [versionOnline, setVersionOnline] = useState(null)
  const [form] = Form.useForm()
  // 自定义校验函数：只能输入数字，不能为空
  const validateNumber = (_, value) => {
    if (value === undefined || value === null || value === '') {
      return Promise.reject(new Error('请输入数字'))
    }
    if (!/^\d+$/.test(value)) {
      return Promise.reject(new Error('只能输入数字'))
    }
    return Promise.resolve()
  }

  const getOnlineVersion = async () => {
    let res = await ajax({
      url: '/bot/version/get',
      data: {
        id: agentId
      },
      method: 'get'
    })
    let versionData = res.data.data
    if (versionData.number) {
      setVersionOnline(versionData)
      let versionArr = versionData.number.split('.')
      let lastVersionNum = parseInt(versionArr[versionArr.length - 1]) + 1
      form.setFieldsValue({
        version1: versionArr[0],
        version2: versionArr[1],
        version3: lastVersionNum
      })
    }
    // console.log('vres',res.data)
  }

  const handlePublish = () => {
    form.validateFields().then((values) => {
      if (versionOnline) {
        //todo：校验版本号递增
      }
      const { version1, version2, version3, updateLog } = values
      ajax({
        url: '/bot/version/publish',
        data: {
          id: agentId,
          number: `${version1}.${version2}.${version3}`,
          updateLog
        },
        method: 'post'
      })
        .then((res) => {
          if (res.data.code === '0') {
            message.success('发布成功')
            setVisible(false)
          }
        })
        .catch((err) => {
          setVisible(false)
        })
    })
  }

  useEffect(() => {
    getOnlineVersion()
  }, [])

  return (
    <MyModal
      title="发布"
      open={true}
      onCancel={() => setVisible(false)}
      width={640}
      onOk={handlePublish}
    >
      <p className={styles['pub-tip']}>
        应用配置和引用技能版本的修改只在测试环境中生效，发布后才会对在生产环境中生效
      </p>
      <p className={styles['pub-tip']}>
        为了避免对用户造成困扰，在发布前，请保证你已经对应用进行了完善的测试
      </p>
      <Form
        form={form}
        name="basic"
        layout="horizontal"
        initialValues={{}}
        autoComplete="off"
        labelCol={{ span: 4 }}
        style={{ marginTop: '18px' }}
      >
        <Form.Item label="版本号" required>
          <Space>
            <Form.Item name="version1" noStyle rules={[{ validator: validateNumber }]}>
              <Input style={{ width: 80 }} maxLength={3} placeholder="" />
            </Form.Item>
            <span>.</span>
            <Form.Item name="version2" noStyle rules={[{ validator: validateNumber }]}>
              <Input style={{ width: 80 }} maxLength={3} placeholder="" />
            </Form.Item>
            <span>.</span>
            <Form.Item name="version3" noStyle rules={[{ validator: validateNumber }]}>
              <Input style={{ width: 80 }} maxLength={3} placeholder="" />
            </Form.Item>
            {versionOnline && <span>线上版本&nbsp;{versionOnline.number}</span>}
          </Space>
        </Form.Item>
        <Form.Item
          label="更新说明"
          name="updateLog"
          rules={[
            { required: true, message: '请输入更新说明' },
            { max: 2000, message: '不能超过2000个字符' }
          ]}
        >
          <Input.TextArea
            placeholder="简要说明本次的更新点，不超过2000字符"
            showCount
            maxLength={2000}
            rows={6}
          />
        </Form.Item>
      </Form>
    </MyModal>
  )
}

export default PublishModal
