import { Form, Input, Radio, Button, Space, Select, Row, Col, Checkbox, message } from 'antd'
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import useFormStore from '@/pages/ToolBox/formData.js'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import _ from 'lodash'

const AutoTemForm = forwardRef((props, ref) => {
  const { autoTemInfo, onFormValuesChange } = props
  const [randomKeyValue, setRandomKeyValue] = useState('')
  const [autoForm] = Form.useForm()
  const { formData, setFormData } = useFormStore()

  useImperativeHandle(ref, () => ({
    validate: async () => {
      try {
        const values = await autoForm.validateFields()
        setFormData(values)
        return values
      } catch (error) {
        console.log('Validation failed', error)
        return null
      }
    }
  }))

  const refreshValueKey = () => {
    const newKey = generateRandomString()
    setRandomKeyValue(newKey)
    autoForm.setFieldsValue({ key: newKey })

    setTimeout(() => {
      onFormValuesChange(true) // 传递实际比较结果
    }, 0)
  }

  const generateRandomString = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_'
    let result = ''
    const length = 30
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length)
      result += characters[randomIndex]
    }
    return result
  }

  const handleValuesChange = (changedValues, allValues) => {
    console.log(changedValues, allValues, 'changedValues和allValues')
    if ('key' in changedValues) {
      setRandomKeyValue(changedValues.key)
      onFormValuesChange(false)
    }

    //  这里表单的值 不是直接对应的回显的时候的值，要拼接一下  再对比
    const assembleValues = {
      method: allValues?.method,
      url: allValues?.url,
      auth: {
        key: allValues?.key,
        type: allValues?.type
      }
    }
    const isFormChanged = !_.isEqual(autoTemInfo?.auto_template, assembleValues)
    onFormValuesChange(isFormChanged)
  }

  useEffect(() => {
    if (autoTemInfo) {
      console.log(autoTemInfo, '子组件看看autoTemInfo')
      autoForm.setFieldsValue({
        method: autoTemInfo?.auto_template?.method,
        url: autoTemInfo?.auto_template?.url,
        key: autoTemInfo?.auto_template?.auth?.key,
        type: autoTemInfo?.auto_template?.auth?.type
      })
      setRandomKeyValue(autoTemInfo?.auto_template?.auth?.key)
    }
  }, [autoTemInfo])

  useEffect(() => {
    if (!autoTemInfo?.auto_template?.auth?.key) {
      refreshValueKey()
    }
  }, [])

  return (
    <div>
      <Form
        form={autoForm}
        labelCol={{
          span: 2
        }}
        wrapperCol={{
          span: 22
        }}
        onValuesChange={handleValuesChange}
      >
        <Form.Item
          name="url"
          rules={[
            { required: true, message: '请填写url', trigger: 'blur' },
            // { validator: this.validateHttpUrl, message: '请填写符合http协议的url', trigger: 'blur' },
            {
              pattern:
                /^(https?:\/\/)?((([a-zA-Z0-9-]+\.)+[a-zA-Z0-9-]+)|(\d{1,3}\.){3}\d{1,3})(:\d+)?(\/[a-zA-Z0-9-_]*)*(\?[a-zA-Z0-9-_&=]*)?$/,
              message: '请输入符合的HTTP协议的URL',
              trigger: 'blur'
            }
          ]}
          label="URL"
        >
          <Input></Input>
        </Form.Item>

        <Form.Item
          name="method"
          rules={[{ required: true, message: '请选择Method' }]}
          label="Method"
        >
          <Select>
            <Select.Option value="POST">POST</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="type" rules={[{ required: true, message: '请选择Type' }]} label="type">
          <Select>
            <Select.Option value="bearer">bearer</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item name="key" rules={[{ required: true, message: '必须生成key' }]} label="key">
          <Space.Compact size="small">
            <Input
              variant="borderless"
              readOnly
              value={randomKeyValue}
              style={{ width: '400px' }}
              onChange={(e) => autoForm.setFieldsValue({ key: e.target.value })}
            ></Input>

            <CopyToClipboard text={randomKeyValue} onCopy={() => message.success('复制成功')}>
              <Button onClick={(e) => e.stopPropagation()} type="text">
                复制
              </Button>
            </CopyToClipboard>

            {/* <Button type="link" onClick={copyValueKey}>
              复制
            </Button> */}
          </Space.Compact>
        </Form.Item>

        <Form.Item label="">
          <Button onClick={refreshValueKey} style={{ marginLeft: '100px' }}>
            重新生成
          </Button>
        </Form.Item>
      </Form>
    </div>
  )
})

export default AutoTemForm
