import { useEffect, useState, useCallback } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import { has } from 'lodash'
import { awaitTo } from '@/utils/util'
import { SEARCH_TYPE__OPTIONS, STRATEGY_OPTIONS, REJECT_CONFIG_KEYS } from '../constant'

const request = {
  get: (url, data) => ajax({ url, data, method: 'get' })
}

const getSearchOptions = (index) => ({ 0: SEARCH_TYPE__OPTIONS, 1: STRATEGY_OPTIONS })[index]

// 资源项配置
const RESOURCE_CONFIG = {
  plugin: {
    path: '/bot/config/getBotAgentPlugins',
    dataKey: 'data',
    nameKey: 'pluginName',
    idKey: 'pluginId'
  },
  multiPlugin: {
    path: '/bot/config/getMultiPlugins',
    dataKey: 'data',
    nameKey: 'pluginName',
    idKey: 'pluginId'
  },
  intent: {
    path: '/bot/config/getBotRagRepos',
    dataKey: 'data.repos',
    nameKey: 'name',
    idKey: 'id'
  },
  workflow: {
    path: '/bot/config/getBotWorkFlows',
    dataKey: 'data.flows',
    nameKey: 'name',
    idKey: 'id'
  },
  atomicAbility: {
    path: '/bot/config/getInterventionAndAble',
    dataKey: 'data.ability',
    nameKey: 'name',
    idKey: 'id',
    // transform: (data) =>
    //   data?.data?.ability?.filter((x) => {
    //     // 筛掉不支持干预包和拒识配置的能力
    //     return x.interventions?.length || REJECT_CONFIG_KEYS.some((y) => !!x[y])
    //   })
    transform: (data) =>
      data?.data?.ability?.filter((x) => {
        // 筛掉不支持干预包和拒识配置的能力
        return x.packages?.length || REJECT_CONFIG_KEYS.some((y) => !!x[y])
      })
  },
  search: {
    path: '/bot/config/getAbleAndSearchConfig',
    dataKey: 'data',
    nameKey: 'name',
    idKey: 'id',
    transform: (data) => {
      const { searchType, strategy } = data.data
      if (!searchType || !strategy) return []
      const result = [searchType, strategy].map((x, i) => {
        const options = getSearchOptions(i)
        const { label, value } = options.find((y) => y.value === x)
        return { name: label, id: value }
      })
      return result
    }
  },
  topic: {
    path: '/bot/config/getAbleAndSearchConfig',
    dataKey: 'data',
    nameKey: 'name',
    idKey: 'id',
    transform: (data) => {
      const { topicGeneration } = data.data
      return [topicGeneration]
    }
  },
  before: {
    path: '/bot/config/handle/get',
    query: { type: 2 },
    transform: (data) => (data.data.swtich ? [data.data] : [])
  },
  after: {
    path: '/bot/config/handle/get',
    query: { type: 3 },
    transform: (data) => (data.data.swtich ? [data.data] : [])
  }
}

// 首页
export const useConfigRightChina = () => {
  const [chains, setChains] = useState([])
  const [currentChain, setCurrentChain] = useState(null)
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const setRouterId = useAgentDetailStore((state) => state.setRouterId)
  const setRouter = useAgentDetailStore((state) => state.setRouter)

  const getChains = useCallback(
    async (botId) => {
      if (!botId) return
      const [err, res] = await awaitTo(request.get('/bot/config/getBotChainInfo', { botId }))
      if (err) return
      const data = res.data?.data || []
      const result = data.map((x) => ({ ...x, label: x.chain_name, value: x.chain_code }))
      const selected = result.find((x) => x.selected)?.chain_code
      setChains(result)
      setCurrentChain(selected)
      setRouterId(selected)

      setRouter((res.data?.data || []).find((item) => item.selected))
    },
    [setRouterId]
  )

  useEffect(() => {
    getChains(agentDetail?.boxBot?.botId)
  }, [agentDetail, getChains])

  return { chains, currentChain, setCurrentChain }
}

// 获取资源
export const useConfigRightResource = (type, shouldFetch) => {
  const [totalCount, setTotalCount] = useState(0)
  const [list, setList] = useState([])
  const [apiResponse, setApiResponse] = useState(null)
  const [loading, setLoading] = useState(false)
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const routerId = useAgentDetailStore((state) => state.routerId)

  const fetchResources = useCallback(async () => {
    const { path, dataKey, nameKey, idKey, transform, query } = RESOURCE_CONFIG[type]
    const botId = agentDetail?.boxBot?.botId
    if (!botId) return
    const params = { botId, pageIndex: 1, pageSize: 1000, routerId, ...(query || {}) }
    const [err, res] = await awaitTo(request.get(path, params), setLoading)
    if (err) return
    let result = []
    let total = 0
    if (transform instanceof Function) {
      result = transform(res.data)
    } else {
      const data = dataKey.split('.').reduce((obj, key) => obj?.[key], res.data) || []
      result = data
        .filter((x) => x.selected)
        .map((x) => ({ ...x, name: x[nameKey] || x.name, id: x[idKey] || x.id }))
    }

    total = result?.length * 1
    setApiResponse(res.data)
    setList(result)
    setTotalCount(total)
  }, [agentDetail?.boxBot?.botId, type, routerId, shouldFetch])

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && routerId && shouldFetch) {
      fetchResources()
    }
  }, [agentDetail?.boxBot?.botId, fetchResources, routerId, shouldFetch])

  return { totalCount, list, fetchResources, loading, setList, setLoading, apiResponse }
}
