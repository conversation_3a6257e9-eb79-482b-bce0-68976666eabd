import React, { useEffect, useState } from 'react'
import { Button, Row, Col, Form, Input, Space, Modal, Tag } from 'antd'
import {
  AppstoreAddOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  MinusCircleOutlined
} from '@ant-design/icons'
import { v4 as uuidv4 } from 'uuid'
import { MyModal } from '@/components'

const ArrayModal = (props) => {
  const { arrayData, open, setIsModalOpen, handleDataSave } = props
  const [arrayItem, setArrayItem] = useState({}) //arrayItem 一定含有children，children的name 为[Array Item]
  const [itemChild, setItemChild] = useState({}) //name 为[Array Item]的子单元
  const [form] = Form.useForm()
  // const formName = Form.useWatch('name', form);
  // const formType = Form.useWatch('type', form);
  useEffect(() => {
    // console.log('arrayData变化',arrayData)
    if (!arrayData.children) return
    setArrayItem(arrayData)
    let child = JSON.parse(JSON.stringify(arrayData.children[0]))
    setItemChild(clearDefault(child))
    form.setFieldsValue({
      ...arrayData
    })
  }, [arrayData])

  const clearDefault = (obj) => {
    //清空默认值
    obj.defaultValue = ''
    if (obj.children && obj.children.length > 0) {
      for (let child of obj.children) {
        clearDefault(child)
      }
    }
    return obj
  }

  const handleOk = () => {
    let formValues = form.getFieldsValue()
    // 因为需要根据children留存arrayItem的结构，所以需要保留一个item,清除item中的默认值
    console.log('获取的modal 表单值', formValues)
    if (!formValues.children || formValues.children.length == 0) {
      console.log('执行保留arrayitem')
      formValues = JSON.parse(JSON.stringify(clearDefault(arrayItem)))
    }
    handleDataSave({
      ...arrayItem,
      ...formValues
    })
    // console.log('当前存储的值', { ...arrayItem,...formValues});
    setIsModalOpen(false)
  }

  const handleCancel = () => {
    setIsModalOpen(false)
  }

  const addArrayItem = () => {
    let childrenCurrent = form.getFieldValue('children')
    console.log('childrenCurrent', form.getFieldsValue(), childrenCurrent)
    let newChild = {
      ...itemChild,
      id: uuidv4()
    }
    form.setFieldValue('children', [...childrenCurrent, newChild])
  }
  const handleItemRemove = (index, removeFunction) => {
    let childrenCurrent = form.getFieldValue('children') || []
    let childNum = childrenCurrent.length
    if (childNum > 1) {
      removeFunction(index)
    } else {
      // 因为需要根据children留存arrayItem的结构，所以需要保留一个item
      // 能点删除的情况下
      form.setFieldValue('children', [{ ...childrenCurrent[0], isDefine: true }])
    }
  }

  const LineRow = ({ name, fieldKey, remove, nestingLevel = 0 }) => {
    const form = Form.useFormInstance() // 获取当前Form 实例
    const nameIndex = name[name.length - 1]

    const type = Form.useWatch(['children', ...name, 'type'], form)
    const valueName = Form.useWatch(['children', ...name, 'name'], form)
    const children = Form.useWatch(['children', ...name, 'children'], form) || []
    const fatherType = Form.useWatch(['children', ...name, 'fatherType'], form)
    const [showChildren, setShowChildren] = useState(true)

    const handleRemove = () => {
      remove()
    }

    const handleAddChild = () => {
      // todo: 处理内嵌array的情况
      let childrenCurrent = JSON.parse(JSON.stringify(children))
      let childItem = clearDefault(childrenCurrent[0])
      form.setFieldValue(['children', ...name, 'children'], [...childrenCurrent, childItem])
    }
    return (
      <React.Fragment>
        <Row gutter={12} key={fieldKey} style={{ width: '100%' }}>
          <Col span={10}>
            <Form.Item
              name={[nameIndex, 'name']}
              style={{ paddingLeft: `${nestingLevel * 12}px` }}
              shouldUpdate
            >
              <>
                {children.length > 0 &&
                  (showChildren ? (
                    <MenuUnfoldOutlined
                      onClick={() => setShowChildren(false)} // 更新状态
                      style={{ marginRight: 8, cursor: 'pointer' }}
                    />
                  ) : (
                    <MenuFoldOutlined
                      onClick={() => setShowChildren(true)} // 更新状态
                      style={{ marginRight: 8, cursor: 'pointer' }}
                    />
                  ))}
                <span>
                  {valueName}
                  <Tag>{type}</Tag>
                </span>
              </>
            </Form.Item>
          </Col>
          <Col span={12}>
            {type != 'object' && type != 'array' && (
              <Form.Item
                name={[nameIndex, 'defaultValue']}
                // rules={[{ required: true, message: '请输入参数值' }]}
              >
                <Input placeholder="请输入参数值" />
              </Form.Item>
            )}
          </Col>

          <Col span={2}>
            {type === 'array' && (
              <AppstoreAddOutlined
                style={{ cursor: 'pointer', marginRight: '8px' }}
                onClick={() => {
                  handleAddChild()
                }}
              />
            )}
            {fatherType == 'array' && <MinusCircleOutlined onClick={handleRemove} />}
          </Col>
        </Row>
        {(type === 'object' || type === 'array') && children.length > 0 && showChildren && (
          <Form.List name={[nameIndex, 'children']}>
            {(childFields, { add, remove }) => (
              <div>
                {childFields.map((childField, index) => {
                  // console.log('childField', childField)
                  return (
                    <LineRow
                      key={`${name}-child-${index}`}
                      name={[...name, 'children', childField.name]}
                      fieldKey={`${fieldKey}-child-${index}`}
                      nestingLevel={nestingLevel + 1}
                      remove={() => remove(childField.name)}
                    />
                  )
                })}
              </div>
            )}
          </Form.List>
        )}
      </React.Fragment>
    )
  }

  const ChildRow = () => {}

  return (
    <MyModal title="默认值设置" open={open} onOk={handleOk} onCancel={handleCancel} width={720}>
      <Row gutter={12} style={{ width: '100%' }}>
        <Col span={10}>参数名称</Col>
        <Col span={12}>参数值</Col>
        <Col span={2}>操作</Col>
      </Row>

      <Form
        layout="vertical"
        style={{ marginTop: '20px', maxHeight: '80vh', overflow: 'auto' }}
        form={form}
      >
        <Row gutter={12} style={{ width: '100%' }}>
          <Col span={10}>
            <Form.Item name={'name'}>
              <span>
                {arrayItem?.name}
                <Tag>{arrayItem?.type}</Tag>
              </span>
            </Form.Item>
          </Col>
          <Col span={12}></Col>
          <Col sapn={2}>
            <AppstoreAddOutlined
              style={{ cursor: 'pointer', marginRight: '8px' }}
              onClick={addArrayItem}
            />
          </Col>
        </Row>
        <Form.List name="children">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <LineRow key={key} fieldKey={key} name={[name]} remove={() => remove(name)} />
              ))}
            </>
          )}
        </Form.List>
      </Form>
    </MyModal>
  )
}

export default ArrayModal
