import styles from './style.module.scss'
import Card from './Card'
import { useEffect, useState } from 'react'
import { RightOutlined } from '@ant-design/icons'

const Home = () => {
  const [cardList, setCardList] = useState([
    { name: '智能体1', type: '官方', description: '这是一个智能体' },
    { name: '智能体2', type: '官方', description: '这是一个智能体' },
    { name: '智能体3', type: '官方', description: '这是一个智能体' }
  ])

  return (
    <div className="main-container">
      <div className={`square`}>
        <div className={styles.banner}>这里是banner占位</div>

        <div className={styles.homeTitle}>
          <span>智能体推荐</span>
          <span className={styles.more}>
            查看更多
            <RightOutlined style={{ color: '#666666' }} />
          </span>
        </div>
        <div className={styles.cardContainer}>
          {cardList.map((item, index) => {
            return <Card key={index} cardData={item} />
          })}
        </div>

        <div className={styles.homeTitle}>
          <span>精品bot</span>
        </div>
        <div className={styles.cardContainer}>
          {cardList.map((item, index) => {
            return <Card key={index} cardData={item} />
          })}
        </div>

        <div className={styles.homeTitle}>
          <span>工具推荐</span>
        </div>
        <div className={styles.cardContainer}>
          {cardList.map((item, index) => {
            return <Card key={index} cardData={item} />
          })}
        </div>
      </div>
    </div>
  )
}

export default Home
