.content-right {
  height: calc(100% - 56px);

  &.content-right-hide {
    bottom: -100vh;
    opacity: 0;
    visibility: hidden !important;
    z-index: -1 !important;
    transform: translateY(40px);
  }

  .title {
    height: 56px;
    line-height: 56px;
    font-size: 16px;
    font-family:
      PingFang SC,
      PingFang SC-Heavy;
    font-weight: 400;
    color: #222222;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #b5ceef;
    padding: 0 20px;
    cursor: move;
  }

  //   .top-guide {
  //     display: flex;
  //     padding: 20px;

  //     .top-guide-head {
  //       width: 65px;
  //       height: 72px;
  //       background: url(assets/images/robot.png) center/contain no-repeat;
  //     }

  //     .top-guide-body {
  //       margin-left: 20px;

  //       .top-guide-title {
  //         font-size: 18px;
  //         font-weight: 600;
  //         color: #222222;
  //         line-height: 25px;
  //       }

  //       .top-guide-desc {
  //         margin-top: 4px;
  //         font-size: 12px;
  //         font-weight: 400;
  //         color: #8093ad;
  //         line-height: 17px;
  //       }
  //     }
  //   }

  .rightChatboxContainer {
    width: 100%;
    height: calc(100% - 80px);
    padding-top: 10px;
    position: relative;
    overflow: auto;
  }

  .rightChatbox {
    width: 100%;
    // height: 100%;
    // overflow: auto;
    box-sizing: border-box;
    padding: 0 50px;

    &::-webkit-scrollbar-track {
      // background-color: rgba(255, 255, 255, 0.3);
      display: none;
    }

    &::-webkit-scrollbar-thumb {
      // background: #bacff2;
      display: none;
    }
  }

  .empty-chat {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 100px;
    .agent-img {
      width: 72px;
      height: 72px;
      border-radius: 12px;
    }
    .topic {
      font-size: 24px;
      font-family:
        PingFang SC,
        PingFang SC-500;
      font-weight: 500;
      color: #111827;
      line-height: 32px;
      margin-top: 12px;
      margin-bottom: 24px;
    }
    .tip-text {
      font-family:
        PingFang SC,
        PingFang SC-500;
      font-weight: 500;
      background: #f5f5f9;
      border-radius: 12px;
      padding: 12px 16px;
      font-size: 14px;
    }
  }

  .control-bar-wrap {
    .msgSend {
      display: flex;
      align-items: center;
      .clear {
        min-width: 38px;
        width: 38px;
        height: 38px;
        border: 1px solid #e8e9eb;
        border-radius: 18px;
        color: #afb2c0;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
      }
      :global {
        .ant-input {
          padding: 5px 0px;
        }
      }
      .send-suffix {
        display: flex;
        align-items: center;
        .line {
          width: 1px;
          height: 16px;
          background: #e8e9eb;
          margin-right: 12px;
        }
      }
    }
    .chat-send {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
    .tip {
      font-size: 12px;
      font-family:
        PingFang SC,
        PingFang SC-400;
      font-weight: 400;
      text-align: center;
      color: #afb2c0;
      line-height: 35px;
    }
  }
}
