.container {
  // padding: 24px;
  // max-height: 600px;
  // overflow-y: auto;
}

.section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.sectionHeader {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .title {
    font-size: 14px;
    color: #1f2329;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 2px;
      height: 14px;
      background: #1677ff;
      margin-right: 8px;
    }
  }

  :global {
    .ant-switch {
      margin-left: 12px;
    }
  }
}

.description {
  color: #999;
  font-size: 12px;
  margin-bottom: 16px;
}

.formItem {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    width: 120px;
    text-align: right;
    padding-right: 12px;
    color: #666;
    font-size: 14px;

    &.required::before {
      content: '*';
      color: #ff4d4f;
      margin-right: 4px;
    }
  }

  .inputWrapper {
    flex: 1;
    display: flex;
    align-items: center;

    :global {
      .ant-input {
        flex: 1;
      }
    }
  }

  .retryCount {
    margin-left: 12px;
    color: #666;
    font-size: 14px;
    white-space: nowrap;
    display: flex;
    align-items: center;

    :global {
      .ant-select {
        margin-left: 8px;

        .ant-select-selector {
          border-radius: 4px;
          height: 32px;
          padding: 0 8px;

          .ant-select-selection-item {
            line-height: 30px;
          }
        }
      }
    }
  }
}

.tokenInput {
  display: flex;
  align-items: center;
  gap: 8px;

  .tokenText {
    flex: 1;
    padding: 4px 0;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.88);
    min-height: 32px;
    display: flex;
    align-items: center;
  }

  :global {
    .ant-btn {
      padding: 0 12px;
      height: 32px;
      border-radius: 4px;
      color: #1677ff;
      border-color: #1677ff;

      &:hover {
        color: #4096ff;
        border-color: #4096ff;
      }
    }
  }
}
