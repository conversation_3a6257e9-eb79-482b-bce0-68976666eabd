import { But<PERSON>, Collapse, Select } from 'antd'
import styles from './style.module.scss'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import PluginConfig from './PluginConfig'
import KnowledgeConfig from './KnowledgeConfig'
import WorkflowConfig from './WorkflowConfig'
import IntervConfig from './IntervConfig'
import OtherConfig from './OtherConfig'
import PostProcessConfig from './PostProcessConfig'
import SearchConfig from './SearchConfig'
import TopicConfig from './TopicConfig'
import AgentConfig from './AgentConfig'

import { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import ajax from '@/utils/http'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'

function ConfigRight() {
  const { agentId } = useParams()

  // const [chains, setChains] = useState([])
  // const [currentChain, setCurrentChain] = useState(null)
  // const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  // const setRouterId = useAgentDetailStore((state) => state.setRouterId)
  // const setRouter = useAgentDetailStore((state) => state.setRouter)
  // const routerId = useAgentDetailStore((state) => state.routerId)
  const router = useAgentDetailStore((state) => state.router)

  // useEffect(() => {
  //   // 获取智能体详情
  //   if (agentDetail?.boxBot?.botId) {
  //     getChains(agentDetail?.boxBot?.botId)
  //   }
  // }, [agentDetail])

  // const getChains = (boxBotId) => {
  //   ajax({
  //     url: '/bot/config/getBotChainInfo',
  //     data: {
  //       botId: boxBotId
  //     },
  //     method: 'get'
  //   }).then((res) => {
  //     if (res.data.code === '0') {
  //       setChains(
  //         (res.data?.data || []).map((item) => {
  //           return {
  //             ...item,
  //             label: item.chain_name,
  //             value: item.chain_code
  //           }
  //         })
  //       )
  //       // 设置当前agentboxId
  //       const cc = (res.data?.data || []).find((item) => item.selected)?.chain_code
  //       // setCurrentChain(cc)
  //       setRouterId(cc)

  //       setRouter((res.data?.data || []).find((item) => item.selected))
  //     }
  //   })
  // }

  // const handleChainChange = (val) => {
  //   setRouterId(val)
  //   setRouter(chains.find((it) => it.chain_code === val))
  //   ajax({
  //     url: '/bot/config/saveAgentRouterConfig',
  //     data: {
  //       routerId: val,
  //       botId: agentDetail?.boxBot?.botId
  //     },
  //     method: 'post'
  //   })
  //     .then((res) => {
  //       if (res.data.code === '0') {
  //       }
  //     })
  //     .catch((err) => {})
  // }

  return (
    <div className={styles['config-scroll']}>
      {/* <Select
        style={{ width: 120, marginBottom: 10 }}
        placeholder="请选择链路"
        onChange={handleChainChange}
        options={chains}
        value={currentChain}
      /> */}
      {/* <div className={styles['config-wrap']}>
        <span className={styles['resouce-title']}>资源配置</span>
        <PluginConfig />
        <KnowledgeConfig />
        <WorkflowConfig />
      </div>

      <div className={styles['config-wrap']}>
        <span className={styles['resouce-title']}>高级配置</span>
        <IntervConfig />
        <SearchConfig />

        {router?.auth?.topicGeneration === 1 && <TopicConfig />}
        <AgentConfig type={2} />
        <AgentConfig type={3} />
      </div> */}
      <span className={styles['resouce-title']}>资源配置</span>
      <PluginConfig />
      <KnowledgeConfig />
      <WorkflowConfig />
      <div className={styles['resource-line']}></div>
      <span className={styles['resouce-title']}>高级配置</span>
      <IntervConfig />
      <SearchConfig />
      {router?.auth?.topicGeneration === 1 && <TopicConfig />}
      <AgentConfig type={2} />
      <AgentConfig type={3} />
    </div>
  )
}
export default ConfigRight
