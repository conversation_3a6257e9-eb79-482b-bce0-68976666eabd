import React, { useState, useEffect } from 'react'
import {
  Modal,
  Form,
  Input,
  Button,
  Checkbox,
  Radio,
  Select,
  Tooltip,
  InputNumber,
  message,
  Space
} from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import SplitConfig from './SplitConfig'
import { createRepo } from './service'
import { APP_ENV, cssVariables } from '@/utils/constant'
import IconRepo from 'assets/svgs/icon-repo.svg?react'
import { MyModal } from '@/components'

const { TextArea } = Input

const CreateKnowledgeModal = (props) => {
  const { visible, setVisble } = props
  const [form] = Form.useForm()
  const [parseType, setParseType] = useState(1)
  const [addLoading, setAddLoading] = useState(false)

  useEffect(() => {
    if (visible) {
      form.resetFields()
      console.log('清空数据')

      setParseType(1)
      form.setFieldsValue({
        symbolType: 'cutOff'
      })
    }
  }, [visible])

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      console.log('提交表单', values)
      setAddLoading(false)
      let data = {
        name: values.addName,
        description: values.addDsp || '',
        fromSource: 'aiCloud'
      }
      // 拼接配置参数
      let parseModelInfo = {
        layout: true,
        parseType
      }
      if (parseType === 2) {
        parseModelInfo.parseConfig = {
          chunkType: values.strategy.join(','),
          lengthRange: JSON.stringify([16, values.maxLength]),
          [values.symbolType]: JSON.stringify(values.identifier)
        }
      }
      let repoConfig = {
        chunkSize: 1024,
        strategyModel: { id: -1 },
        vectorModel: {
          id: 10,
          modelInfo: JSON.stringify({
            name: 'SPARK',
            domain: 'spark',
            defaultDim: 23
          })
        },
        dimModel: {
          id: 23,
          modelInfo: JSON.stringify({
            name: '1024',
            chunkSize: [16, 1024],
            defaultChunk: 1024,
            version: '2.0.1024'
          })
        },
        parseSplitModel: {
          id: 46,
          modelInfo: JSON.stringify(parseModelInfo)
        }
      }
      data.repoConfig = JSON.stringify(repoConfig)
      createRepo(data).then((data) => {
        let res = data.data
        if (res.code == 0) {
          setAddLoading(false)
          message.success('创建知识库成功')
          setVisble(false)
          props.getList()
        }
      })
    })
  }

  return (
    <MyModal
      title={
        APP_ENV === 'base' ? (
          <Space size={4}>
            <IconRepo /> {'创建知识库'}
          </Space>
        ) : (
          '创建知识库'
        )
      }
      open={visible}
      onCancel={() => setVisble(false)}
      onOk={handleSubmit}
      okText={'创建'}
      confirmLoading={addLoading}
      width={600}
      // footer={[
      //   <Button key="cancel" onClick={() => setVisble(false)}>
      //     取消
      //   </Button>,
      //   <Button key="primary" type="primary" loading={addLoading} onClick={handleSubmit}>
      //     确定
      //   </Button>
      // ]}
      destroyOnHidden
    >
      <Form form={form} layout="vertical" labelCol={{ span: 5 }}>
        <Form.Item
          label="知识库名称："
          name="addName"
          rules={[
            { required: true, message: '请输入知识库名称' },
            { max: 32, message: '名称不得超过32个字符', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文 数字 下划线
                if (!regExp.test(value)) {
                  callback(new Error('名称只能包含中英文/数字/下划线'))
                } else {
                  callback()
                }
              },
              trigger: 'change'
            }
          ]}
        >
          <Input
            placeholder="输入自定义库名称，中文、英文不超过32个字符"
            showCount
            maxLength={32}
          />
        </Form.Item>
        <Form.Item
          label="描述："
          name="addDsp"
          rules={[{ max: 50, message: '文档描述不得超过50个字符', trigger: 'change' }]}
        >
          <TextArea
            placeholder="输入文档描述，非必填，50字内"
            autoSize={{ minRows: 3 }}
            showCount
            maxLength={50}
          />
        </Form.Item>
        <Form.Item label="拆分方法：">
          <SplitConfig form={form} parseType={parseType} setParseType={setParseType} />
        </Form.Item>
      </Form>
    </MyModal>
  )
}

export default CreateKnowledgeModal
