import React, { useEffect, useState } from 'react'
import { Form, Input, Radio, Button, Space, Typography, Select } from 'antd'
import { ToolOutlined, KeyOutlined, LockOutlined } from '@ant-design/icons'
import useFormStore from './formData.js'

const StepForm1 = (props) => {
  const { handleStepChange } = props
  const [form] = Form.useForm()
  const authType = Form.useWatch('authType', form)
  const location = Form.useWatch('location', form)

  const { formData, setFormData } = useFormStore()

  const { Text } = Typography

  const protocolTypes = [
    { value: 1, label: '固定http协议' },
    { value: 2, label: '自定义协议' }
  ]

  const requestMethods = [
    {
      value: 'get',
      label: 'get'
    },
    {
      value: 'post',
      label: 'post'
    },
    {
      value: 'put',
      label: 'put'
    },
    {
      value: 'delete',
      label: 'delete'
    },
    {
      value: 'patch',
      label: 'patch'
    }
  ]

  const next = () => {
    form.validateFields().then((values) => {
      console.log(values, '这个是values')
      setFormData({
        ...formData,
        ...values
      })
      handleStepChange(1)
    })
  }

  useEffect(() => {
    console.log('step1 formData', formData)
    if (formData && formData.toolName) {
      console.log('step1 formData 123', formData)

      form.setFieldsValue(formData)
    }
  }, [formData])

  return (
    <div>
      <Form
        form={form}
        layout="vertical"
        autoComplete="off"
        initialValues={{
          authType: -1,
          location: 'header'
        }}
      >
        <Form.Item
          label="协议类型"
          name="protocolType"
          rules={[{ required: true, message: '请输入工具描述' }]}
        >
          <Select options={protocolTypes} placeholder="请选择协议"></Select>
        </Form.Item>

        <Form.Item
          label="工具名称"
          name="toolName"
          rules={[{ required: true, message: '请输入工具名称' }]}
        >
          <Input prefix={<ToolOutlined />} placeholder="请输入" maxLength={20} showCount />
        </Form.Item>

        <Form.Item
          label="工具描述"
          name="description"
          rules={[{ required: true, message: '请输入工具描述' }]}
          extra={
            <span
              style={{
                textAlign: 'left',
                fontSize: '12px',
                color: '#757575',
                display: 'block',
                marginBottom: 8
              }}
            >
              通过自然语言描述工具的作用，请尽量给出示例，例：“此工具用于完成特定的功能。如帮我发一封邮件给张三”
            </span>
          }
        >
          <Input.TextArea placeholder="请输入" maxLength={200} showCount />
        </Form.Item>

        <Form.Item
          label="工具路径"
          name="endPoint"
          rules={[{ required: true, message: '请输入工具路径' }]}
        >
          <Input placeholder="请输入" />
        </Form.Item>

        <Form.Item label="授权方式" name="authType" rules={[{ required: true }]}>
          <Radio.Group>
            <Space size="large">
              <Radio.Button
                value={-1}
                style={{
                  width: 500,
                  height: 100,
                  padding: '16px',
                  textAlign: 'left',
                  borderRadius: '8px',
                  border:
                    authType === -1
                      ? '1px solid var(--sparkos-primary-color)'
                      : '1px solid #d9d9d9',
                  boxShadow: authType === -1 ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                  background: authType === -1 ? '#f0faff' : '#fff'
                }}
              >
                <LockOutlined
                  style={{ fontSize: 18, marginRight: 8, color: 'var(--sparkos-primary-color)' }}
                />
                <strong>不需要授权</strong>
                <div>
                  <Text type="secondary">无需额外授权就可以使用 API</Text>
                </div>
              </Radio.Button>

              <Radio.Button
                value={2}
                style={{
                  width: 500,
                  height: 100,
                  padding: '16px',
                  textAlign: 'left',
                  borderRadius: '8px',
                  border:
                    authType === 2 ? '1px solid var(--sparkos-primary-color)' : '1px solid #d9d9d9',
                  boxShadow: authType === 2 ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                  background: authType === 2 ? '#f0faff' : '#fff'
                }}
              >
                <KeyOutlined
                  style={{ fontSize: 18, marginRight: 8, color: 'var(--sparkos-primary-color)' }}
                />
                <strong>Service</strong>
                <div>
                  <Text type="secondary">
                    需要在请求头 (header) 或查询参数 (query) 时携带密钥来获取授权
                  </Text>
                </div>
              </Radio.Button>
            </Space>
          </Radio.Group>
        </Form.Item>

        {authType === 2 && (
          <Form.Item
            label="位置"
            name="location"
            rules={[{ required: true, message: '请选择密钥传递方式' }]}
            extra={
              <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                Header代表在请求头中传递密钥，Query代表在查询中传递密钥
              </div>
            }
          >
            <Radio.Group optionType="radio">
              <Space size="large">
                <Radio.Button
                  value="header"
                  style={{
                    width: 500,
                    height: 36,
                    padding: '3px 16px',
                    textAlign: 'left',
                    borderRadius: '8px',
                    border:
                      location === 'header'
                        ? '1px solid var(--sparkos-primary-color)'
                        : '1px solid #d9d9d9',
                    boxShadow: location === 'header' ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                    background: location === 'header' ? '#f0faff' : '#fff'
                  }}
                >
                  Header
                </Radio.Button>
                <Radio.Button
                  value="query"
                  style={{
                    width: 500,
                    height: 36,
                    padding: '3px 16px',
                    textAlign: 'left',
                    borderRadius: '8px',
                    border:
                      location === 'query'
                        ? '1px solid var(--sparkos-primary-color)'
                        : '1px solid #d9d9d9',
                    boxShadow: location === 'query' ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                    background: location === 'query' ? '#f0faff' : '#fff'
                  }}
                >
                  Query
                </Radio.Button>
              </Space>
            </Radio.Group>
          </Form.Item>
        )}

        {authType === 2 && (
          <Form.Item
            name="parameterName"
            label="Parameter name"
            rules={[{ required: true, message: '请输入' }]}
            extra={
              <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                密钥的参数，您需要传递Service
                Token的参数名。其作用是告诉API服务，您将在哪个参数中提供授权信息
              </div>
            }
          >
            <Input></Input>
          </Form.Item>
        )}

        {authType === 2 && (
          <Form.Item
            name="serviceToken"
            label="Service token / APl key"
            rules={[{ required: true, message: '请输入' }]}
            extra={
              <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                密钥的参数值，代表您的身份或给定的服务权限。API服务会验证此Token，以确保您有权进行相应的操作
              </div>
            }
          >
            <Input></Input>
          </Form.Item>
        )}

        <Form.Item
          label="请求方法"
          name="method"
          rules={[{ required: true, message: '请选择请求方法' }]}
        >
          <Select options={requestMethods} placeholder="请选择请求方法"></Select>
        </Form.Item>

        {/* <Form.Item>
          <Button type="primary" onClick={() => next()}>
            保存并继续
          </Button>
        </Form.Item> */}
      </Form>
    </div>
  )
}

export default StepForm1
