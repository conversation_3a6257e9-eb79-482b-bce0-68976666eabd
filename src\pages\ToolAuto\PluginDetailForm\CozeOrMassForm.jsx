import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { LockOutlined, KeyOutlined } from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import { Input, Button, Space, App, Form, Select, Radio } from 'antd'
import ajax from '@/utils/http'
import useFormStore from '@/pages/ToolBox/formData.js'
import _ from 'lodash'

const CozeOrMassForm = forwardRef((props, ref) => {
  const [form] = Form.useForm()
  const { Option } = Select
  const [agentInfo, setAgentInfo] = useState({})
  const { source, setSource } = useState('coze')
  const { pluginId } = useParams()

  const { formData, setFormData } = useFormStore()
  const { massOrCozeInfo, onFormValuesChange } = props

  useImperativeHandle(ref, () => ({
    validate: async () => {
      try {
        const values = await form.validateFields()
        setFormData(values)
        return values
      } catch (error) {
        return null
      }
    }
  }))

  const handleValuesChange = (changedValues, allValues) => {
    console.log(
      changedValues,
      allValues,
      massOrCozeInfo?.maas_template,
      'changedValues和allValues和massOrCozeInfo?.maas_template'
    )
    const isFormChanged = !_.isEqual(
      massOrCozeInfo?.maas_template || massOrCozeInfo?.coze_template,
      allValues
    )
    onFormValuesChange(isFormChanged)
  }

  useEffect(() => {
    if (massOrCozeInfo) {
      console.log(massOrCozeInfo, '在子组件中的massOrCozeInfo')
      form.setFieldsValue({
        key: massOrCozeInfo?.maas_template?.key || massOrCozeInfo?.coze_template?.key,
        id: massOrCozeInfo?.maas_template?.id || massOrCozeInfo?.coze_template?.id,
        type: massOrCozeInfo?.maas_template?.type || massOrCozeInfo?.coze_template?.type
      })
    }
  }, [massOrCozeInfo])

  return (
    <App>
      <div>
        <Form
          form={form}
          labelWrap
          labelCol={{
            span: 2
          }}
          wrapperCol={{
            span: 22
          }}
          onValuesChange={handleValuesChange}
        >
          <Form.Item label="api类型" name="type" rules={[{ required: true, message: '请选择' }]}>
            <Select placeholder="请选择api类型">
              <Option value="workflow">工作流类型 </Option>
              <Option value="bot">智能体类型 </Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="api唯一ID:"
            name="id"
            rules={[
              { required: true, message: '请输入工作流id或者智能体id' },
              {
                max: 250,
                message: '请输入250个以内的字符'
              }
            ]}
          >
            <Input placeholder="请输入工作流id或者智能体id"></Input>
          </Form.Item>

          <Form.Item
            label="授权信息-token:"
            name="key"
            rules={[
              { required: true, message: '请填入授权信息' },
              {
                max: 250,
                message: '请输入250个以内的字符'
              }
            ]}
          >
            <Input placeholder="请填入授权信息"></Input>
          </Form.Item>
        </Form>
      </div>
    </App>
  )
})

export default CozeOrMassForm
