.main {
  height: 100%;
  flex: auto;
  min-width: 0;
  overflow: auto;
}

.page {
  height: 100%;
  background: linear-gradient(90deg, #fbf7f4, #eef7ff 100%, #efeffb 100%, #fbf7f4 100%);
  // background: #fff;
  .page-top {
    padding: 0px 24px;
    width: 100%;
    height: 60px;
    font-size: 20px;
    display: flex;
    align-items: center;
    position: relative;
    // background: linear-gradient(90deg, #fbf7f4, #eef7ff 100%, #efeffb 100%, #fbf7f4 100%);
    // &::after {
    //   content: '';
    //   position: absolute;
    //   left: 0;
    //   bottom: -1px;
    //   width: 100%;
    //   height: 1px;
    //   background-color: #e4e7ed;
    //   z-index: 1;
    // }
    .page-top-left {
      -webkit-box-flex: 1;
      -ms-flex: auto;
      flex: auto;
      padding-right: 48px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .page-title-operate {
        display: flex;
        align-items: center;
        span {
          display: inline-block;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .page-title-operate-time {
        font-size: 12px;
        font-weight: 400;
        color: var(--flow-desc-color);
        line-height: 16px;
      }
    }
    .page-top-right {
      display: flex;
      align-items: center;
    }
  }
  .page-scroll {
    padding: 0px;
    height: calc(100% - 60px);
    flex: 1 1 auto;
    transition: max-width 0.3s ease 0s;
    overflow-y: auto;
    display: flex;
    background: #fcfcfc;
    .config-pannel {
      flex: auto;
      border: 1px solid rgba(0, 0, 0, 0.1);
      border-radius: 0px 16px 16px 0px;
      background: #fff;
      .config-title {
        height: 56px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-family:
          PingFang SC,
          PingFang SC-500;
        font-weight: 500;
        padding: 0 20px;
        .config-chain {
          font-weight: 400;
          font-size: 14px;
          line-height: 24px;
          cursor: pointer;
          color: #111827;
        }
      }
      .config-content {
        display: flex;
        padding: 16px;
        height: calc(100% - 56px);
        position: relative;
        gap: 16px;

        .config-left {
          width: 50%;
          height: 100%;
          overflow: hidden;
          background: #fcfcfc;
          border: 1px solid #e5e5e5;
          border-radius: 16px;
          padding: 18px 0px;
        }
        .config-right {
          width: 50%;
          height: 100%;
          overflow: hidden;
          background: #fcfcfc;
          border: 1px solid #e5e5e5;
          border-radius: 16px;
          padding: 18px 0px;
        }
      }
    }
    .debug-pannel {
      width: 420px;
      padding: 0px 20px;
    }

    @media (min-width: 1920px) {
      .debug-pannel {
        width: 560px;
      }
    }
  }
}

.pub-tip {
  font-weight: 400;
  color: #6b7280;
}
