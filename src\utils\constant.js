import { v4 as uuidv4 } from 'uuid'

export const DATA_TYPES_MAP = [
  { value: 'String', label: 'String' },
  { value: 'Number', label: 'Number' },
  { value: 'Integer', label: 'Integer' },
  { value: 'Boolean', label: 'Boolean' },
  { value: 'Array', label: 'Array' },
  { value: 'Object', label: 'Object' }
]

export const METHOD_TYPES_MAP = [
  { value: 'Body', label: 'Body' },
  { value: 'Path', label: 'Path' },
  { value: 'Query', label: 'Query' },
  { value: 'Header', label: 'Header' }
]

export const SOURCE_TYPES_MAP = [
  { value: 'default', label: '默认值' },
  { value: 'model', label: '模型识别' },
  { value: 'business', label: '业务透传' }
]

export const getRandomId = () => {
  const id = uuidv4()
  return id
}

export const APP_ENV = import.meta.env.VITE_APP_ENV

export const cssVariables = {
  base: {
    // '--sparkos-primary-color': '#31353E',
    '--sparkos-primary-color': '#4D53E8',
    '--sparkos-button-color': '#1D1F25', // 黑色
    '--sparkos-second-color': '#4D53E8', //辅色，紫色
    '--container-radius': '16px',
    '--container-border-left': '1px solid rgba(0, 0, 0, 0.1)',
    '--modal-footer-text-align': 'end',

    '--sparkos-text-color': '#111827',
    '--sparkos-desc-color': '#6B7280',
    // 定义卡片列表卡片的样式
    '--sparkos-card-border-color': '#d1d5db',
    '--sparkos-card-border-radius': '12px',
    '--sparkos-card-background': 'linear-gradient(180deg,#ffffff, #fafafa 100%)',
    '--sparkos-card-hover-box-shadow':
      '0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 6px -1px rgba(0,0,0,0.10)',

    '--sparkos-resource-width': '100%'
  },
  auto: {
    '--sparkos-primary-color': '#3271fa',
    '--sparkos-button-color': '#3271fa',
    '--sparkos-second-color': '#3271fa', //辅色
    '--container-radius': '0px',
    '--container-border-left': 'none',
    '--modal-footer-text-align': 'center',

    '--sparkos-text-color': '#333',
    '--sparkos-desc-color': '#666',
    // 定义卡片列表卡片的样式
    '--sparkos-card-border-color': '#d1d5db',
    '--sparkos-card-border-radius': '12px',
    '--sparkos-card-background': 'linear-gradient(180deg,#ffffff, #fafafa 100%)',
    '--sparkos-card-hover-box-shadow':
      '0px 2px 4px -1px rgba(0,0,0,0.06), 0px 4px 6px -1px rgba(0,0,0,0.10)',
    '--sparkos-resource-width': 'fit-content'
  }
}
