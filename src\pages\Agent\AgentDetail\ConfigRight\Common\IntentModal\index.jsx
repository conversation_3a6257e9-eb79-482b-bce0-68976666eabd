import { useState, useEffect, useRef } from 'react'
import {
  Space,
  Table,
  Input,
  Button,
  Modal,
  message,
  Checkbox,
  Select,
  Pagination,
  Empty,
  Tooltip,
  Flex
} from 'antd'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import { APP_ENV } from '@/utils/constant'
import { useNavigate } from 'react-router-dom'
import microAppRouter from '@/hooks/useMicroAppRouter.js'
import { MyModal } from '@/components'
import CenterLoading from '@/components/CenterLoading'
import { CustomEmpty } from '@/components'

const { Option } = Select
const { Search } = Input

const IntentModal = ({
  open,
  onCancel,
  botId,
  bussinessId,
  bussinessType,
  onSubmit,
  selectIntents
}) => {
  const navigate = useNavigate()

  const { baseRouter } = microAppRouter()

  const [loading, setLoading] = useState(false)
  const [items, setItems] = useState([])

  const rawItems = useRef([])

  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10 // 每页10条数据

  // 计算当前页数据
  const currentData = items.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  useEffect(() => {
    if (open && bussinessId) {
      fetchData()
    } else {
      setItems([])
    }
  }, [bussinessId, botId, open])

  const fetchData = () => {
    const parentData = window.microApp?.getData()
    setLoading(true)
    let param = {
      pageSize: 9999,
      pageIndex: 1,
      botId
    }
    if (bussinessType === 'flow') {
      param.flowId = bussinessId
    } else {
      param.pluginId = bussinessId
    }
    ajax({
      url: bussinessType === 'flow' ? '/bot/config/getFlowIntents' : '/bot/config/getPluginIntents',
      data: param,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        const allItems = (res.data?.data?.data || [])
          .filter((it) => {
            if (APP_ENV === 'auto') {
              if (it.official === 1) {
                return true
              } else {
                if (parentData?.orgCode) {
                  return it.groupName === `auto:${parentData?.orgCode}`
                } else {
                  return true
                }
              }
            } else {
              return true
            }
          })
          .map((item) => {
            const version = item.version ? item.version : item.versionList[0]?.version
            const versionObj = item.versionList.find((it) => it.version === version)
            const isMatch = !!selectIntents.find((a) => a.intentId === item.intentId)

            return {
              ...item,
              version,
              intentName: versionObj.intentName,
              intentNameEn: versionObj.intentNameEn,
              intentDesc: versionObj.intentDesc,
              corpusCount: versionObj.corpusCount,
              quote: isMatch,
              entityCount: versionObj.entityCount
            }
          })
          .sort((a, b) => {
            if (a.quote === b.quote) {
              return 0 // 如果 quote 值相同，保持原有顺序
            }
            return a.quote ? -1 : 1 // 如果 a.quote 为 true，a 排在前面；否则 b 排在前面
          })
        setItems(allItems)
        rawItems.current = allItems.slice()
        setLoading(false)
      }
    })
  }

  // 处理 Checkbox 变化
  const handleCheckboxChange = (intentId) => {
    setItems((prevItems) =>
      prevItems.map((item) => (item.intentId === intentId ? { ...item, quote: !item.quote } : item))
    )
  }

  const handleVersionChange = (intentId, val) => {
    setItems((prevItems) => {
      return prevItems.map((item) => {
        return item.intentId === intentId
          ? {
              ...item,
              version: val,
              corpusCount: item.versionList.find((it) => it.version === val)?.corpusCount,
              entityCount: item.versionList.find((it) => it.version === val)?.entityCount
            }
          : item
      })
    })
  }

  // 处理搜索
  const onSearch = (value) => {
    // setTableParams((prev) => ({
    //   ...prev,
    //   searchVal: value,
    //   pagination: { ...prev.pagination, current: 1 }
    // }))
    const searchItems = rawItems.current.filter(
      (it) =>
        it.intentName.toLowerCase().includes(value.toLowerCase()) ||
        it.intentNameEn.toLowerCase().includes(value.toLowerCase())
    )
    setCurrentPage(1)
    setItems(searchItems)
  }

  const handleOk = async () => {
    console.log('handleOk', items)
    const intents = items
      .filter((it) => it.quote)
      .map((it) => {
        let obj = {
          intentId: it.intentId,
          // operation: it.quote ? 'open' : 'close'
          version: it.version
        }
        // if (it.quote) {
        //   obj.version = it.version
        // }
        return obj
      })
    onSubmit(bussinessId, intents)
  }

  const handleCancel = () => {
    onCancel?.()
  }

  const gotoCreate = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/intent')
    } else if (APP_ENV === 'auto') {
      baseRouter.open(`/agent/intent`)
    }
  }

  const gotoIntentDetail = (isAuthor, intentId) => {
    // isAuthor    false 没权限跳转 ，true 可以跳转
    if (isAuthor) {
      if (APP_ENV === 'base') {
        navigate(`/workspace/intent/${intentId}/corpus`)
      } else if (APP_ENV === 'auto') {
        baseRouter.open(`/agent/intent?id=${intentId}&page=corpus`)
      }
    } else {
      message.warning('暂无权限')
    }
  }

  return (
    <MyModal
      title="关联意图"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnHidden
      width={762}
      cancelButtonProps={{ style: { display: 'none' } }}
    >
      <Flex justify={'space-between'} align={'center'}>
        <Button type="link" onClick={gotoCreate} style={{ margin: '8px 0' }}>
          创建意图
        </Button>
        <Search
          placeholder="输入意图中英文名称搜索"
          onSearch={onSearch}
          allowClear
          style={{ width: 300 }}
        />
      </Flex>

      {currentData.length > 0 ? (
        <>
          <div className={styles.container}>
            {currentData.map((item) => (
              <div key={item.intentId} className={styles.itemCard}>
                <Checkbox
                  checked={item.quote}
                  onChange={() => handleCheckboxChange(item.intentId)}
                  className={styles.checkbox}
                />
                <div className={styles.content}>
                  <div className={styles.title}>
                    {item.intentName} <span className={styles.subtitle}>{item.intentNameEn}</span>
                  </div>

                  <Tooltip title={item.intentDesc.length > 20 ? item.intentDesc : ''}>
                    <div className={styles.description}>{item.intentDesc}</div>
                  </Tooltip>
                </div>
                <Select
                  value={item.version}
                  className={styles.select}
                  style={{ width: 200 }}
                  placeholder="请选择版本"
                  onChange={(val) => handleVersionChange(item.intentId, val)}
                >
                  {item.versionList.map((it, index) => {
                    return (
                      <Option value={it.version} key={index}>
                        {it.version} <span>{it.remark}</span>
                      </Option>
                    )
                  })}
                </Select>

                {(item.corpusCount && item.corpusCount > 0) ||
                (item.entityCount && item.entityCount > 0) ? (
                  <Button type="default" className={styles.button}>
                    {item.corpusCount}示例说法
                    {item.entityCount > 0 ? ` , ${item.entityCount}实体` : ''}
                  </Button>
                ) : null}

                <Button type="link" onClick={() => gotoIntentDetail(item.isAuthor, item.intentId)}>
                  修改
                </Button>
              </div>
            ))}
          </div>
          {/* 分页器 */}
          {items.length > pageSize && (
            <Pagination
              current={currentPage}
              pageSize={pageSize}
              total={items.length}
              onChange={(page) => setCurrentPage(page)}
              style={{ marginTop: 16, textAlign: 'center' }}
              align="end"
            />
          )}
        </>
      ) : loading ? (
        <CenterLoading height={300} />
      ) : (
        <CustomEmpty />
      )}
    </MyModal>
  )
}

export default IntentModal
