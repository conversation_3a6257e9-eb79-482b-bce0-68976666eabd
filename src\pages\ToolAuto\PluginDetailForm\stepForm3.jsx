import React, { useState, useEffect } from 'react'
import useFormStore from './formData.js'
import styles from './style.module.scss'
import { Button, Row, Col, Form, Input, Space, message, Tooltip } from 'antd'
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  AppstoreAddOutlined,
  MinusCircleOutlined
} from '@ant-design/icons'
import ajax from '@/utils/http'
import { useNavigate } from 'react-router-dom'
import { v4 as uuidv4, validate } from 'uuid'

const StepForm3 = (props) => {
  const { debugSuccess } = props
  const { formData, setFormData } = useFormStore()
  const [form] = Form.useForm()
  const [jsonData, setJsonData] = useState('')

  const [loading, setLoading] = useState(false)

  const exeDebugger = async () => {
    let inputValueCurrent = await form.validateFields()

    let schemaValue = {
      toolRequestInput: inputValueCurrent.toolRequestInput || [],
      toolRequestOutput: formData.toolRequestOutput || []
    }
    console.log('schemaValue', schemaValue)
    console.log('formData', formData)
    // let params = {
    //   ...formData,
    //   webSchema: JSON.stringify(schemaValue)
    // }

    let params
    if (formData?.authType === 2) {
      params = {
        ...formData,
        webSchema: JSON.stringify(schemaValue),
        authInfo: JSON.stringify({
          location: formData.location,
          parameterName: formData.parameterName,
          serviceToken: formData.serviceToken
        })
      }
      delete params['location']
      delete params['parameterName']
      delete params['serviceToken']
    } else {
      params = {
        ...formData,
        webSchema: JSON.stringify(schemaValue)
      }
    }

    delete params['toolRequestInput']
    delete params['toolRequestOutput']
    setLoading(true)
    ajax({
      url: '/aiui-agent/plugin/debugPluginCustom',
      data: params,
      method: 'post'
    })
      .then((res) => {
        let data = res.data
        let strData
        if (data.code === '0') {
          let dataObj
          dataObj = JSON.parse(data?.data)
          strData = JSON.stringify(dataObj, null, 2)
          setLoading(false)
          message.success('调试成功')
          debugSuccess()
          setJsonData(strData)
        } else {
          console.log('走到else里面')
          message.error(data?.desc)
          strData = data?.data
          setJsonData(strData)
          setLoading(false)
        }
        setLoading(false)
      })
      .catch((err) => {
        console.log('err的信息', err)
        setJsonData(err?.data?.desc)
        setLoading(false)
      })
  }

  useEffect(() => {
    form.setFieldsValue({
      toolRequestInput: formData.toolRequestInput
    })
  }, [formData])

  const FormRow = ({ name, fieldKey, area, remove, nestingLevel = 0 }) => {
    const form = Form.useFormInstance() // 获取当前Form 实例
    const type = Form.useWatch([area, ...name, 'type'], form)
    const children = Form.useWatch([area, ...name, 'children'], form) || []
    const fatherType = Form.useWatch([area, ...name, 'fatherType'], form)

    const nameIndex = name[name.length - 1]
    // 提取参数值
    const valueName = Form.useWatch([area, ...name, 'name'], form)
    // const valueType = Form.useWatch([area, ...name, 'type'], form);
    const valueRequired = Form.useWatch([area, ...name, 'required'], form)
    // console.log('提取的参数值',valueName,valueType,valueRequired);
    const [showChildren, setShowChildren] = useState(true)

    const handleRemove = () => {
      remove()
    }

    const addArrayItem = (namePath) => {
      const arrayPath = ['toolRequestInput', ...namePath, 'children']
      const currentArray = [].concat(form.getFieldValue(arrayPath) || [])

      // 3. 获取模板项（优先使用现有第一个元素，否则创建默认结构）
      const templateItem =
        currentArray.length > 0
          ? currentArray[0]
          : {
              id: uuidv4(),
              name: '[Array Item]',
              type: 'string',
              required: false,
              defaultValue: ''
            }

      const newItem = {
        ...JSON.parse(JSON.stringify(templateItem)),
        id: uuidv4(),
        name: `[Array Item]`,
        defaultValue: ''
      }

      // 5. 更新数组
      form.setFieldValue(arrayPath, [...currentArray, newItem])
    }

    return (
      <React.Fragment>
        <Row gutter={12} key={fieldKey} style={{ width: '100%' }}>
          <Col span={8}>
            <Form.Item
              name={[nameIndex, 'name']}
              style={{
                paddingLeft: `${nestingLevel * 12}px`,
                wordBreak: 'break-word', // 强制换行
                whiteSpace: 'normal' // 允许换行
              }}
              shouldUpdate
            >
              <>
                {children.length > 0 &&
                  (showChildren ? (
                    <MenuUnfoldOutlined
                      onClick={() => setShowChildren(false)} // 更新状态
                      style={{ marginRight: 8, cursor: 'pointer' }}
                    />
                  ) : (
                    <MenuFoldOutlined
                      onClick={() => setShowChildren(true)} // 更新状态
                      style={{ marginRight: 8, cursor: 'pointer' }}
                    />
                  ))}

                <Tooltip title={valueName}>
                  <span style={{ display: 'inline-block', maxWidth: '80%' }}>{valueName}</span>
                </Tooltip>
                {/* <span>{valueName}</span> */}
              </>
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item
              // {...restField}
              shouldUpdate
              name={[nameIndex, 'type']}
              rules={[{ required: true, message: '请选择参数类型' }]}
            >
              <span>{type}</span>
            </Form.Item>
          </Col>

          <Col span={2}>
            <Form.Item name={[nameIndex, 'required']} valuePropName="checked" shouldUpdate>
              <span>{valueRequired ? '是' : '否'}</span>
            </Form.Item>
          </Col>

          {type != 'object' && type != 'array' && (
            <Col span={8}>
              <Form.Item
                name={[nameIndex, 'defaultValue']}
                rules={[{ required: valueRequired, message: '请输入参数值' }]}
              >
                <Input placeholder="请输入参数值" />
              </Form.Item>
            </Col>
          )}
          <Col span={2}>
            <Form.Item dependencies={[area, nameIndex, 'type']}>
              {() => (
                <div>
                  {type === 'array' && (
                    <AppstoreAddOutlined
                      style={{ cursor: 'pointer', marginRight: '8px' }}
                      onClick={() => addArrayItem(name)}
                    />
                  )}

                  {fatherType == 'array' && <MinusCircleOutlined onClick={handleRemove} />}
                </div>
              )}
            </Form.Item>
          </Col>
        </Row>
        {(type === 'object' || type === 'array') && children.length > 0 && showChildren && (
          <Form.List name={[nameIndex, 'children']}>
            {(childFields, { add, remove }) => (
              <div>
                {childFields.map((childField, index) => {
                  // console.log('childField', childField)
                  return (
                    <FormRow
                      key={`${name}-child-${index}`}
                      name={[...name, 'children', childField.name]}
                      fieldKey={`${fieldKey}-child-${index}`}
                      nestingLevel={nestingLevel + 1}
                      area={area}
                      remove={() => remove(childField.name)}
                    />
                  )
                })}
              </div>
            )}
          </Form.List>
        )}
      </React.Fragment>
    )
  }

  return (
    <div style={{ width: '100%' }}>
      <div className={styles.label}>
        <span>参数配置</span>
        <Button type="primary" className={styles.debugBtn} onClick={exeDebugger} loading={loading}>
          调试
        </Button>
      </div>
      <Row gutter={12} style={{ width: '100%' }}>
        <Col span={7}>参数名称</Col>
        <Col span={4}>参数类型</Col>
        <Col span={3}>是否必填</Col>
        <Col span={8}>参数值</Col>
        <Col span={2}>操作</Col>
      </Row>
      <Form layout="vertical" style={{ marginTop: 20 }} form={form}>
        <Form.List name="toolRequestInput">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <FormRow
                  key={key}
                  fieldKey={key}
                  name={[name]}
                  area={'toolRequestInput'}
                  remove={remove}
                />
              ))}
            </>
          )}
        </Form.List>
      </Form>

      <div className={styles.label}>
        <span>调试结果</span>
      </div>
      <div className={styles.jsonShow}>
        <pre>
          <code>{jsonData}</code>
        </pre>
      </div>

      {/* <Space>
        <Button type="primary" onClick={save}>
          保存
        </Button>
      </Space> */}
    </div>
  )
}

export default StepForm3
