import { useState, useEffect, useRef } from 'react'
import { List, Button, App, Spin, Modal, message, Space, Popconfirm, Tag } from 'antd'
import { ExclamationCircleFilled, TagsOutlined } from '@ant-design/icons'
import { getRepoList, deleteRepo } from './service'
import { useNavigate } from 'react-router-dom'
import { useUserStore } from '@/store/user'
import styles from './style.module.scss'
import CreateKnowledgeModal from './CreateKnowledgeModal'
import DeleteModal from './DeleteModal'
import Card from './Card'
import CardList from '@/components/CardList'
import docImg from '@/assets/images/doc-num.png'
import { APP_ENV } from '@/utils/constant'
import IconEdit from '@/assets/svgs/icon-edit.svg?react'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'

import { modalEvents } from '@/utils/eventSystem.js'

const repoList = () => {
  const navigate = useNavigate()
  const [list, setList] = useState([])
  const [total, setTotal] = useState(0)
  const [pageData, setPageData] = useState({ pageIndex: 1, pageSize: 12 })
  const [visible, setVisble] = useState(false)
  const [loading, setLoading] = useState(false)

  const [delVisible, setDelVisible] = useState(false)
  const [modalConfig, setModalConfig] = useState()

  // const observerRef = useRef(null)

  const cardListRef = useRef(null) // 用于获取 CardList 的 ref

  const currentSelected = useRef(null)

  const renderFooterTag = (cardData) => {
    return (
      <div style={{ display: 'flex', alignContent: 'center', gap: '3px' }}>
        {APP_ENV === 'auto' && cardData?.orgName && (
          <Tag color="orange" icon={<TagsOutlined />}>
            {cardData?.orgName}
          </Tag>
        )}
        <div className={styles.docNum}>
          <img src={docImg} />
          {cardData.docCount} 文档
        </div>
      </div>
    )
  }

  const getCardList = () => {
    cardListRef?.current.reload()
  }

  const getList = (isLoadMore = false) => {
    if (loading) return
    setLoading(true)
    getRepoList({
      ...pageData
    })
      .then((data) => {
        let res = data.data
        // console.log(data, data.data)
        setTotal(res.data.repoPage.total)
        setLoading(false)
        setList((prevList) =>
          isLoadMore ? [...prevList, ...res.data.repoPage.records] : res.data.repoPage.records
        )
      })
      .catch(() => setLoading(false))
  }

  const lookRepo = (item) => {
    navigate(`/repo/${item.id}/docManage`, { replace: true })
  }

  const onDeleteClick = async () => {
    let data = await deleteRepo({ repoId: currentSelected.current.id })
    if (data.data.code == 0) {
      message.success('操作成功')
      cardListRef?.current.reload()
    }
  }

  const [isHovered, setIsHovered] = useState(false)

  const optItems = [
    {
      key: 'edit',
      label: (
        <Space>
          <IconEdit />
          <span>编辑</span>
        </Space>
      )
    },
    {
      key: 'delete',
      label: (
        <Space
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className={'delete-menu-item'}
        >
          <Popconfirm
            title="删除知识库"
            description={
              <span>
                将删除该知识库下的所有数据，<strong>删除后不可恢复</strong>，请谨慎操作
              </span>
            }
            onConfirm={(e) => onDeleteClick()}
            okText="是"
            okButtonProps={{ loading: false }}
            cancelText="否"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <Space>
              {isHovered ? <IconDeleteRed /> : <IconDelete />}
              <div
                style={{
                  color: isHovered ? '#f15858' : '#374151'
                }}
              >
                删除
              </div>
            </Space>
          </Popconfirm>
        </Space>
      ),
      style: { backgroundColor: isHovered ? '#fee2e2' : '#fff' }
    }
  ]
  const onDropItemsClick = (e, l) => {
    console.log('e', e, l)
    const key = e.key
    e?.domEvent?.stopPropagation()
    switch (key) {
      case 'edit':
        navigate(`/repo/${l.id}/baseInfo`)
        break
      case 'copy':
        onCopyClick(l)
        break
      case 'delete':
        currentSelected.current = l

        break
      //考虑删除操作需要loading, modal中按钮需要防抖 因此改为这种处理
      // setModalConfig({
      //   titleName: l.name,
      //   content: (
      //     <span>
      //       将删除该知识库下的所有数据，<strong>删除后不可恢复</strong>，请谨慎操作
      //     </span>
      //   ),
      //   delAction: async () => {
      //     let data = await deleteRepo({ repoId: l.id })
      //     if (data.data.code == 0) {
      //       message.success('操作成功')
      //       setDelVisible(false)
      //       getCardList()
      //     }
      //   }
      // })
      // setDelVisible(true)
      // modal.confirm({
      //   title: `确认是否删除 ${l.name}`,
      //   icon: <ExclamationCircleFilled />,
      //   content: '将删除该知识库下的所有数据，删除后不可恢复，请谨慎操作',
      //   footer: <div style={{marginTop:'12px',textAlign:'end'}}>
      //     <Button
      //       onClick={()=> {
      //         Modal.destroyAll()
      //       }}
      //     >取消</Button>
      //     <Button
      //       type="primary"
      //       style={{marginLeft:'8px'}}
      //       loading={true}
      //       onClick={() => {
      //         setDelLoading(true)
      //         try {
      //           deleteRepo({ repoId: l.id }).then((data) => {
      //             let res = data.data
      //             if (res.code == 0) {
      //               message.success('操作成功')
      //               close()
      //               getCardList()
      //             }
      //             setDelLoading(false)
      //           })
      //         } catch (e) {
      //           setDelLoading(false)
      //           return
      //         }
      //       }}
      //     >确认</Button>
      //   </div>,
      // })
    }
  }

  useEffect(() => {
    useUserStore.setState({ selectedMenus: ['workspace'] })
  }, [])

  useEffect(() => {
    const unsubscribe = modalEvents.listen('OPEN_REPO', () => {
      setVisble(true)
    })
    return () => {
      unsubscribe() // 使用返回的清理函数
      // 或者直接使用：modalEvents.unlisten('OPEN_B_MODAL', handler);
    }
  }, [])

  // useEffect(() => {
  //   if (pageData.pageIndex > 1) getList(true)
  // }, [pageData.pageIndex])

  // useEffect(() => {
  //   if (!observerRef.current) return
  //   const observer = new IntersectionObserver(
  //     (entries) => {
  //       if (entries[0].isIntersecting && list.length < total) {
  //         console.log('触发下拉加载')
  //         setPageData((prev) => ({
  //           ...prev,
  //           pageIndex: prev.pageIndex + 1
  //         }))
  //       }
  //     },
  //     { threshold: 1.0 }
  //   )
  //   observer.observe(observerRef.current)
  //   return () => observer.disconnect()
  // }, [list, total])

  return (
    <App>
      {/* <div className={styles.cardContainer}>
        {list.length > 0 &&
          list.map((repoItem) => {
            return (
              <Card
                cardData={repoItem}
                key={repoItem.id}
                refreshList={getList}
                onCardClick={lookRepo}
              />
            )
          })}
      </div>
      <div ref={observerRef} style={{ height: 60, textAlign: 'center' }}>
        {loading && <Spin />}
      </div> */}

      <CardList
        title={'知识库'}
        ref={cardListRef}
        searchConfig={{
          url: `/aiuiKnowledge/knowledge/repo/${APP_ENV === 'auto' ? 'autocar' : 'iflycloud'}/getList`,
          method: 'post',
          httpConfig: { useFormData: true },
          searchKey: 'name',
          pagination: {
            page: 'pageIndex',
            pageSize: 'pageSize'
          },
          extraParams: {
            orderByField: 'update_time'
          },
          dataFormatter: (data) => {
            return {
              list: data?.data?.repoPage?.records || [],
              total: data?.data?.repoPage?.total
            }
          }
        }}
        cardConfig={{
          title: 'name',
          description: 'description',
          updateTime: 'updateTime'
        }}
        dropdown={{
          optItems,
          onDropItemsClick
        }}
        renderFooterTag={renderFooterTag}
        events={{
          onAddClick: () => setVisble(true),
          onCardClick: lookRepo
        }}
      />
      {visible && (
        <CreateKnowledgeModal visible={visible} setVisble={setVisble} getList={getCardList} />
      )}
      {delVisible && (
        <DeleteModal visible={delVisible} setVisible={setDelVisible} modalConfig={modalConfig} />
      )}
    </App>
  )
}

export default repoList
