.civi-config-right {
  color: #333;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .civi-config-right__title {
      line-height: 0.5rem;
      position: relative;
      padding: 0 0.1rem;
      font-weight: bold;
      font-size: 0.16rem;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 51%;
        transform: translateY(-50%);
        width: 0.04rem;
        height: 0.15rem;
        background: #3271fa;
      }
    }
  }

  :global {
    .ant-select {
      width: 160px;
      margin: 10px 0;
    }

    .ant-btn-icon {
      height: auto !important;
    }

    .ant-collapse {
      border-radius: 0;

      &-header {
        background: #fff;
        position: relative;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
        height: 0.5rem;
        display: flex;
        align-items: center !important;

        .ant-collapse-expand-icon {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          color: #999;
          font-size: 18px;
        }

        .ant-collapse-extra {
          position: relative;
        }
      }
    }
  }
}
