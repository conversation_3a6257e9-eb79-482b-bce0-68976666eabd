.repoHeader {
  display: flex;
  align-items: center;
  font-size: 16px;
  .return {
    cursor: pointer;
    width: 32px;
    height: 32px;
    border: 1px solid #e6e7e9;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }
  .division {
    color: #afb2c0;
    margin: 0 10px;
  }
  .repoName {
    font-size: 20px;
    color: #111827;
    font-weight: 600;
  }
}

.docManage {
  display: flex;
  height: 100%;
  // padding-top: 12px;
  .main {
    flex: 1;
    padding-right: 8px;
    .header {
      display: flex;
      justify-content: space-between;
      margin: 8px 0;
    }
    .rightMain {
      display: flex;
      height: 100%;
      gap: 12px;
      .tagLeft {
        width: 260px;
        height: 100%;
        overflow: auto;
        background: #fcfcfc;
        border: 1px solid #e5e5e5;
        border-radius: 16px;
        padding: 20px 18px;
        :global {
          .ant-tree {
            background: none;
          }
          .ant-tree-node-content-wrapper {
            padding-inline: 0;
          }
        }
        .labelNode {
          display: flex;
          justify-content: space-between;
          border-radius: 6px;
          padding: 8px 12px;
        }
        .labelHover {
          background-color: #f5f5f5;
        }
      }
      .docRight {
        flex: 1;
        width: 0;
        overflow: auto;
        background: #fcfcfc;
        border: 1px solid #e5e5e5;
        border-radius: 16px;
        padding: 20px 18px;
        position: relative;
        :global {
          .ant-table-thead {
            .ant-table-cell {
              background: #fcfcfc;
            }
          }
        }
        .advance-filter {
          // position: absolute;
          // left: 136px;
          // top: 52px;
          width: 400px;
          background: #ffffff;
          border-radius: 4px;
          box-shadow: 0px 10px 18px 2px #e5eaf8;
          z-index: 5;
          padding: 20px;
          label {
            border-right: 1px solid #e7e9ed;
            padding-right: 8px;
          }
        }
      }
    }
  }

  .chat {
    width: 64px;
    writing-mode: vertical-rl;
    font-size: 16px;
    border-left: 1px solid #232323;
  }

  .docName {
    color: var(--sparkos-second-color);
    word-break: break-all;
    &:hover {
      .icon {
        display: inline-flex;
      }
    }
    .icon {
      margin-left: 4px;
      display: none;
    }
  }
}

.pointManage {
  height: 100%;
  padding-top: 12px;
  position: relative;
  .docHeader {
    margin-bottom: 8px;
  }
  .ragLoading {
    height: calc(100% - 32px);
    overflow: hidden;
    text-align: center;
    padding-top: 32px;
  }
  .ragMicro {
    height: calc(100% - 40px); //减去顶部操作区域距离
    overflow: hidden;
    position: relative;
    micro-app-rag {
      height: 100%;
    }
  }
}

.chatContent {
  overflow: auto;
  .chat {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    .qsText {
      border-radius: 12px;
      padding: 10px 15px;
    }
  }
  .qsText-answer {
    border: 1px solid #e5e7eb;
    color: #374151;
  }
  .qsText-question {
    color: #ffffff;
    background: var(--sparkos-primary-color);
  }
}

.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .debugThreshold {
    font-size: 14px;
    > span {
      cursor: pointer;
    }
  }
}

.chatFooter {
  .msgSend {
    display: flex;
    align-items: center;
    .clear {
      min-width: 38px;
      width: 38px;
      height: 38px;
      border: 1px solid #e8e9eb;
      border-radius: 18px;
      color: #afb2c0;
      cursor: pointer;
      text-align: center;
      line-height: 38px;
      margin-right: 8px;
    }
    :global {
      .ant-input {
        padding: 7px 11px;
      }
    }
  }
  .tip {
    font-size: 12px;
    font-family:
      PingFang SC,
      PingFang SC-400;
    font-weight: 400;
    text-align: center;
    color: #afb2c0;
    line-height: 35px;
  }
}
