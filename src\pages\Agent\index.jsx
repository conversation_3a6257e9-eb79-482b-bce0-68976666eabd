import { useState, useEffect, useRef } from 'react'
import { LinkSegmented } from '@/components'
import { Outlet, useNavigate } from 'react-router-dom'
import {
  Input,
  Space,
  Button,
  Pagination,
  App,
  Modal,
  message,
  Empty,
  Dropdown,
  Popconfirm
} from 'antd'
import { SearchOutlined, ExclamationCircleOutlined, MoreOutlined } from '@ant-design/icons'
import styles from './style.module.scss'
import ajax from '@/utils/http'
import AgentModal from './Modal'
import CardList from '@/components/CardList'
import { APP_ENV } from '@/utils/constant'
import IconCopy from '@/assets/svgs/icon-copy.svg?react'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'

import { modalEvents } from '@/utils/eventSystem.js'

const AgentList = (props) => {
  const title = '智能体'
  const cardListRef = useRef(null) // 用于获取 CardList 的 ref

  const navigate = useNavigate()

  const [modalOpen, setModalOpen] = useState(false)

  const currentSelected = useRef(null)

  const goCreateAgent = () => {
    setModalOpen(true)
  }

  const handleCancel = () => {
    setModalOpen(false)
  }

  const handleRefresh = () => {
    cardListRef?.current.reload()
  }

  const onDeleteClick = async () => {
    const result = await ajax({
      url: `/bot/delete`,
      method: 'post',
      data: {
        id: currentSelected.current.id
      }
    })
    if (result.data?.code === '0') {
      message.success('删除成功')
      // fetchData(1)
      cardListRef?.current.reload()
    }
  }

  const [isHovered, setIsHovered] = useState(false)

  const optItems = [
    // {
    //   key: 'edit',
    //   label: <span>编辑</span>
    // },

    {
      key: 'copy',
      label: (
        <Space>
          <IconCopy />
          <div>{APP_ENV === 'auto' ? '复制' : '复刻'}</div>
        </Space>
      )
    },
    {
      key: 'delete',
      label: (
        <Space
          className={'delete-menu-item'}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Popconfirm
            title="删除智能体"
            description="确认删除该智能体？"
            onConfirm={(e) => onDeleteClick()}
            okText="是"
            okButtonProps={{ loading: false }}
            cancelText="否"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <Space>
              {isHovered ? <IconDeleteRed /> : <IconDelete />}
              <div
                style={{
                  color: isHovered ? '#f15858' : '#374151'
                }}
              >
                删除
              </div>
            </Space>
          </Popconfirm>
        </Space>
      ),
      style: { backgroundColor: isHovered ? '#fee2e2' : '#fff' }
    }
  ]

  const onEditClick = (item) => {
    // navigate(`/${item.id}`, '_blank')
  }

  const onCopyClick = async (item) => {
    console.log('onCopyClick执行了')
    const result = await ajax({
      url: `/bot/copy`,
      method: 'post',
      data: {
        id: item.id
      }
    })
    if (result.data?.code === '0') {
      message.success('复制成功')
      // fetchData(1)
      cardListRef?.current.reload()
    }
  }

  const onDropItemsClick = (e, l) => {
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case 'edit':
        onEditClick(l)
        break
      case 'copy':
        onCopyClick(l)
        break
      case 'delete':
        currentSelected.current = l
        break
    }
  }

  const onAgentClick = (item) => {
    navigate(`/workspace/agent/${item.id}`)
  }

  useEffect(() => {
    const unsubscribe = modalEvents.listen('OPEN_AGENT', () => {
      setModalOpen(true)
    })
    return () => {
      unsubscribe() // 使用返回的清理函数
      // 或者直接使用：modalEvents.unlisten('OPEN_B_MODAL', handler);
    }
  }, [])

  return (
    <App>
      <CardList
        title={title}
        ref={cardListRef}
        searchConfig={{
          url: '/bot/list',
          method: 'get',
          searchKey: 'name',
          pagination: {
            page: 'pageIndex',
            pageSize: 'pageSize'
          },
          dataFormatter: (data) => {
            return {
              list: data?.data?.records || [],
              total: data?.data?.total
            }
          }
        }}
        cardConfig={{
          title: 'name',
          description: 'description',
          // updateTime: 'updateTime',
          id: 'botId'
        }}
        dropdown={{
          optItems,
          onDropItemsClick
        }}
        // renderFooterTag={renderFooterTag}
        events={{
          onAddClick: goCreateAgent,
          onCardClick: onAgentClick
        }}
      />

      <AgentModal open={modalOpen} handleCancel={handleCancel} handleRefresh={handleRefresh} />
    </App>
  )
}

export default AgentList
