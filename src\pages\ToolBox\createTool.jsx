import React, { useState, useEffect, useRef } from 'react'
import { Input, Button, Spin, Row, Col, Card, Form, Steps, theme } from 'antd'
import {
  ProFormDateRangePicker,
  ProFormSelect,
  ProFormText,
  StepsForm
} from '@ant-design/pro-components'
import styles from './style.module.scss'
import { useSearchParams } from 'react-router-dom'
import ajax from '@/utils/http'

import useFormStore from './formData.js'

import StepForm1 from './stepForm1'
import StepForm2 from './stepForm2'
import StepForm3 from './stepForm3'

const Tools = () => {
  const [current, setCurrent] = useState(0) //步骤
  const [searchParams, setSearchParams] = useSearchParams()
  const { clearFormData, formData, setFormData } = useFormStore()

  const steps = [
    {
      title: '填写基本信息'
    },
    {
      title: '添加工具',
      content: '112'
    },
    {
      title: '测试与校验',
      content: '112'
    }
  ]

  const handleStepForm1 = () => {
    setCurrent(1)
  }

  const handleStepForm2 = () => {
    setCurrent(2)
  }

  const gotoPrevStep = () => {
    console.log('gotoPrevStep')
    setCurrent(0)
    console.log(formData, 'formData')
  }

  const handleStepChange = (page) => {
    setCurrent(page)
  }

  const items = steps.map((item) => ({
    key: item.title,
    title: item.title
  }))

  useEffect(() => {
    // clearFormData()
    console.log('location', searchParams.get('tool'))
    let toolId = searchParams.get('tool')
    if (toolId) {
      ajax({
        url: '/aiui-agent/aiui-tool/tool/toolPage',
        data: {
          toolId,
          pageIndex: 1,
          toolSquare: false
        },
        method: 'post'
      }).then((res) => {
        if (res.data.code == 0) {
          console.log(res.data.data.result)
          let toolData = res.data.data.result[0]
          let webSchema = JSON.parse(toolData.webSchema)
          delete toolData['createTime']
          delete toolData['updateTime']
          delete toolData['updateUser']
          delete toolData['schemaFormat']
          setFormData({
            ...toolData,
            toolRequestInput: webSchema.toolRequestInput,
            toolRequestOutput: webSchema.toolRequestOutput
          })
        }
      })
    }
  }, [])

  return (
    <div className="main-container">
      {/* <Steps current={current} items={items} /> */}
      <div className={styles.content}>
        {/* {current === 0 && <StepForm1 handleStepChange={handleStepChange}></StepForm1>}
        {current === 1 && <StepForm2 handleStepChange={handleStepChange}></StepForm2>} */}

        {/* <StepForm1></StepForm1> */}
        <StepForm2></StepForm2>

        {current === 2 && <StepForm3 handleStepChange={handleStepChange} />}
      </div>
    </div>
  )
}

export default Tools
