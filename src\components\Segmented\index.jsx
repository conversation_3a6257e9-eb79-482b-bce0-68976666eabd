import { Segmented, ConfigProvider } from 'antd'
import styles from './style.module.scss'

const LinkSegmented = (props) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Segmented: {
            borderRadiusSM: 8,
            itemSelectedBg: '#1D1F25',
            itemSelectedColor: '#FFFFFF',
            itemColor: '#1D1F25',
            itemHoverBg: '#ECECEC',
            trackPadding: '6px'
          }
        }
      }}
    >
      <Segmented className={styles.segmented} {...props} />
    </ConfigProvider>
  )
}

export default LinkSegmented
