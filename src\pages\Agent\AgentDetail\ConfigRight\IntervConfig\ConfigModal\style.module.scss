/* 容器样式 */
.container {
  padding: 24px;
  max-height: 600px;
  overflow-y: auto;
}

/* 头部样式 */
.header {
  margin-bottom: 16px;
}

/* 链路选择器样式 */
.chainSelect {
  width: 200px;
}

/* 能力项卡片样式 */
.itemCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 160px;
    height: 160px;
    background-image: url('/src/assets/images/rejection.png');
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 0.1;
    pointer-events: none;
  }

  &:hover {
    border-color: #d1d5db;
  }

  &.selected {
    border-color: #1677ff;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

/* 内容区域样式 */
.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.leftContent {
  .title {
    font-size: 14px;
    color: #1f2329;
    margin-bottom: 4px;
  }

  .code {
    font-size: 12px;
    color: #6b7280;
  }
}

.rightContent {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f3f4f6;
}

.packageCount {
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background: #f3f4f6;
    color: #1677ff;
  }
}

.dropdownContent {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  min-width: 600px;
  max-height: 400px;
  overflow-y: auto;

  :global {
    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 500;
        padding: 8px;
        font-size: 14px;
      }

      .ant-table-tbody > tr > td {
        padding: 8px;
        font-size: 14px;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }

    .ant-radio-wrapper {
      margin-right: 0;
    }
  }
}

/* 编辑按钮样式 */
.editButton {
  padding: 0;
  height: auto;
  color: #6b7280;
  margin-left: 12px;

  &:hover {
    color: #1677ff;
  }

  /* 编辑图标样式 */
  svg {
    width: 16px;
    height: 16px;
  }
}

/* 编辑弹窗容器样式 */
.editContainer {
  padding: 20px;
}

.selectHeader {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.selectItem {
  padding: 6px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #d1d5db;
    color: #1f2329;
  }

  &.selected {
    background: #1677ff;
    border-color: #1677ff;
    color: #ffffff;
  }
}

.packageList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 500px;
  overflow-y: auto;
  padding-right: 4px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 3px;
  }
}

.packageCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  &.selected {
    .checkbox {
      border-color: #1677ff;
      background: #1677ff;
    }
  }
}

.checkbox {
  width: 18px;
  height: 18px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  flex-shrink: 0;
}

.packageInfo {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
  gap: 16px;
}

.mainInfo {
  flex: 1;
  min-width: 0;
}

.titleRow {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.title {
  font-size: 15px;
  font-weight: 500;
  color: #1f2329;
}

.number {
  font-size: 13px;
  color: #6b7280;
}

.description {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
}

.version {
  font-size: 12px;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  flex-shrink: 0;
  align-self: center;
}

.intervItem {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #d1d5db;
  }

  &:last-child {
    margin-bottom: 0;
  }

  .title {
    font-size: 14px;
    color: #1f2329;
    margin-bottom: 4px;
  }

  .code {
    font-size: 12px;
    color: #6b7280;
  }

  .packageCount {
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;
  }
}

.footer {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.popoverContent {
  min-width: 600px;
  max-height: 400px;
  overflow-y: auto;

  :global {
    .ant-table {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 500;
        padding: 8px;
        font-size: 14px;
      }

      .ant-table-tbody > tr > td {
        padding: 8px;
        font-size: 14px;
      }

      .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
      }
    }

    .ant-checkbox-wrapper {
      margin-right: 0;
    }
  }
}

.checkboxWrapper {
  :global {
    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #1677ff;
      border-color: #1677ff;
    }
  }
}

.packagePopover {
  :global {
    .ant-popover-inner {
      padding: 12px;
      border-radius: 8px;
    }
  }
}
