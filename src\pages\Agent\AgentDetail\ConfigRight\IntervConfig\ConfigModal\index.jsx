import { useState, useEffect, useCallback } from 'react'
import { message, Popover, Checkbox, Table } from 'antd'
import PropTypes from 'prop-types'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import { APP_ENV, cssVariables } from '@/utils/constant'
import ajax from '@/utils/http'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { useConfigRightChina } from '../../hooks/useConfigRight'

const IntervConfigModal = ({ visible, onClose, onRefresh }) => {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [configItems, setConfigItems] = useState([])
  const [packageLists, setPackageLists] = useState({})
  const [loading, setLoading] = useState(false)

  const { currentChain } = useConfigRightChina()

  const fetchInterventionAbility = useCallback(async (boxBotId, currentChain) => {
    try {
      setLoading(true)
      const res = await ajax({
        url: '/bot/config/getInterventionAbility',
        data: {
          botId: boxBotId,
          routerId: currentChain,
          pageIndex: 1,
          pageSize: 1000
        },
        method: 'get'
      })

      if (res.data.code === '0') {
        const items = res.data.data.ability.map((item) => ({
          ...item,
          packageCount: item.interventions?.length || 0,
          currentPackage: null
        }))
        setConfigItems(items)
      } else {
        message.error('获取干预能力列表失败')
      }
    } catch {
      message.error('获取干预能力列表失败')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    if (visible && agentDetail?.boxBot?.botId) {
      fetchInterventionAbility(agentDetail?.boxBot?.botId, currentChain)
    }
  }, [visible, agentDetail, currentChain, fetchInterventionAbility])

  const fetchPackageList = async (boxBotId, item) => {
    try {
      setLoading(true)
      const res = await ajax({
        url: '/bot/config/getInterventionList',
        data: {
          botId: boxBotId,
          abilityId: item.abilityId,
          category: item.category,
          pageIndex: 1,
          pageSize: 1000
        },
        method: 'get'
      })

      if (res.data.code === '0') {
        const packages = res.data.data.packages || []

        const interventionIds = new Set(
          (item.interventions || [])
            .filter((intervention) => {
              const isValid = intervention && intervention.id != null
              return isValid
            })
            .map((intervention) => {
              const strId = String(intervention.id)
              return strId
            })
        )

        const packagesWithSelection = packages.map((pkg) => {
          const pkgId = String(pkg.id)
          const isSelected = interventionIds.has(pkgId)

          return {
            ...pkg,
            isSelected
          }
        })

        setPackageLists((prev) => ({
          ...prev,
          [item.abilityId]: packagesWithSelection
        }))

        setConfigItems((prev) =>
          prev.map((configItem) => {
            if (configItem.abilityId === item.abilityId) {
              const selectedPackages = packagesWithSelection.filter((pkg) => pkg.isSelected)
              return {
                ...configItem,
                currentPackage: selectedPackages[0] || null,
                packageCount: selectedPackages.length
              }
            }
            return configItem
          })
        )
      } else {
        message.error('获取干预包列表失败')
      }
    } catch {
      message.error('获取干预包列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handlePackageSelect = (item, selectedIds) => {
    setPackageLists((prev) => ({
      ...prev,
      [item.abilityId]: prev[item.abilityId].map((pkg) => ({
        ...pkg,
        isSelected: selectedIds.includes(pkg.id)
      }))
    }))

    setConfigItems((prev) =>
      prev.map((configItem) => {
        if (configItem.abilityId === item.abilityId) {
          const selectedPackages =
            packageLists[item.abilityId]?.filter((pkg) => selectedIds.includes(pkg.id)) || []
          return {
            ...configItem,
            currentPackage: selectedPackages[0],
            packageCount: selectedPackages.length
          }
        }
        return configItem
      })
    )
  }

  const renderPackageContent = (item) => {
    const packages = packageLists[item.abilityId] || []

    const columns = [
      {
        title: '干预包编号',
        dataIndex: 'number',
        key: 'number'
      },
      {
        title: '干预包名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '版本',
        dataIndex: 'version',
        key: 'version',
        width: 80
      },
      {
        title: '说明',
        dataIndex: 'description',
        key: 'description'
      },
      {
        title: '勾选',
        key: 'select',
        width: 60,
        render: (_, record) => (
          <div className={styles.checkboxWrapper}>
            <Checkbox
              checked={record.isSelected}
              onChange={(e) => {
                const selectedPackages = packages
                  .filter((pkg) => (pkg.id === record.id ? e.target.checked : pkg.isSelected))
                  .map((pkg) => pkg.id)
                handlePackageSelect(item, selectedPackages)
              }}
            />
          </div>
        )
      }
    ]

    return (
      <div className={styles.popoverContent}>
        <Table
          columns={columns}
          dataSource={packages}
          pagination={false}
          size="small"
          rowKey="id"
          rowClassName={(record) => (record.isSelected ? styles.selectedRow : '')}
        />
      </div>
    )
  }

  const handleMainModalOk = async () => {
    if (!agentDetail?.boxBot?.botId) {
      message.error('botId不能为空')
      return
    }

    try {
      setLoading(true)
      const interventionConfigs = {}
      configItems.forEach((item) => {
        const allPackages = packageLists[item.abilityId] || []
        if (allPackages.length > 0) {
          interventionConfigs[item.abilityCode] = allPackages.map((pkg) => ({
            iId: String(pkg.id),
            operation: pkg.isSelected ? 'open' : 'close',
            version: pkg.version,
            type: 2
          }))
        }
      })

      const requestData = {
        interventionConfigs,
        botId: agentDetail.boxBot.botId
      }

      const res = await ajax({
        url: '/bot/config/saveInterventionConfig',
        method: 'post',
        data: requestData
      })

      if (res.data.code === '0') {
        message.success('保存成功')
        await fetchInterventionAbility(agentDetail?.boxBot?.botId, currentChain)
        onClose()
        onRefresh?.()
      } else {
        message.error(res.data.msg || '保存失败')
      }
    } catch {
      message.error('保存失败')
    } finally {
      setLoading(false)
    }
  }

  return (
    <MyModal
      title="干预配置"
      open={visible}
      onCancel={onClose}
      onOk={handleMainModalOk}
      width={600}
      destroyOnHidden
      confirmLoading={loading}
    >
      <div className={styles.container}>
        {configItems.map((item) => (
          <div
            key={item.abilityId}
            className={`${styles.itemCard} ${item.currentPackage ? styles.selected : ''}`}
          >
            <div className={styles.content}>
              <div className={styles.leftContent}>
                <div className={styles.title}>{item.abilityName}</div>
                <div className={styles.code}>{item.abilityCode}</div>
              </div>
              <div className={styles.rightContent}>
                <Popover
                  content={renderPackageContent(item)}
                  trigger="click"
                  placement="rightTop"
                  overlayInnerClassName={styles.packagePopover}
                  onOpenChange={(open) => {
                    if (open && !packageLists[item.abilityId]) {
                      fetchPackageList(agentDetail?.boxBot?.botId, item)
                    }
                  }}
                >
                  <span className={styles.packageCount}>{item.packageCount} 干预包</span>
                </Popover>
              </div>
            </div>
          </div>
        ))}
      </div>
    </MyModal>
  )
}

IntervConfigModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onRefresh: PropTypes.func
}

export default IntervConfigModal
