.header {
  background: #f5f5f5;
  display: flex;
  align-items: center; /* 垂直居中 */
  padding: 8px 24px;
  background: #f8f8f8;
  border-bottom: 1px solid #eaeaea;

  .backIcon {
    font-size: 16px;
    cursor: pointer;
  }

  .title {
    font-size: 20px;
    font-weight: 700;
    margin: 0px 30px;
  }

  .menu {
    border-right: none;
    border-inline-end: none !important;
    background: #f5f5f5;
    border-radius: 10px;
    :global(.ant-menu-item) {
      padding-left: 12px !important;
      font-weight: 500;
      font-size: 15px;
    }
    :global(.ant-menu-item-selected) {
      font-weight: 600;
    }
    :global(.ant-menu-item-group-title) {
      padding: 0 12px;
      margin-top: 16px;
    }
  }

  .menuItem {
    display: flex;
    align-items: center;
    justify-items: center;
  }

  .menuItem span {
    margin-left: 8px;
  }

  .btns {
    margin-left: auto;
  }
}

.pluginHeader {
  display: flex;
  align-items: center;
  font-size: 16px;
  .return {
    cursor: pointer;
    width: 32px;
    height: 32px;
    border: 1px solid #e6e7e9;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }
  .division {
    color: #afb2c0;
    margin: 0 10px;
  }
  .pluginName {
    font-size: 20px;
    color: #111827;
    font-weight: 600;
  }
}
