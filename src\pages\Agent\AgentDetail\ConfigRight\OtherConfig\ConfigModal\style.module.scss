.container {
  padding: 24px;
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.sectionTitle {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #1f2329;
  margin-bottom: 16px;

  :global {
    .anticon {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.globeIcon {
  margin-right: 8px;
  font-size: 16px;
}

.ruleList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ruleItem {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 160px;
    height: 160px;
    background-image: url('/src/assets/images/rejection.png');
    background-repeat: no-repeat;
    background-position: right center;
    background-size: contain;
    opacity: 0.5;
    pointer-events: none;
  }
}

.ruleContent {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.ruleName {
  font-size: 14px;
  font-weight: 500;
  color: #1f2329;
}

.ruleCode {
  font-size: 12px;
  color: #6b7280;
}

.rejectButton {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 12px;
  border-color: #e5e7eb;
  position: relative;
  z-index: 1;

  :global {
    .anticon {
      font-size: 14px;
    }
  }

  &:hover {
    color: #1677ff;
    border-color: #1677ff;
  }
}

.dropdownContent {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;

  :global {
    .ant-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .ant-checkbox-wrapper {
        margin-right: 0;
        color: #1f2329;
        font-size: 14px;

        &:hover {
          color: #1677ff;
        }
      }
    }
  }
}

.recognitionTypes {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 12px;
  width: 100%;

  :global {
    .ant-checkbox-wrapper {
      margin-right: 0;
      color: #6b7280;
      font-size: 12px;
    }
  }
}

.searchConfig {
  display: flex;
  gap: 16px;

  :global {
    .ant-select {
      .ant-select-selector {
        border-radius: 8px;
        border-color: #e5e7eb;
      }
    }
  }
}
.note {
  margin-left: 0px;
  font-size: 14px;
  color: #999;
  line-height: 1.5;
}
