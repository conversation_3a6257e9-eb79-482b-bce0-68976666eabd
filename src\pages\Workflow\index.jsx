import { useState, useEffect, useRef, lazy, Suspense } from 'react'
import { LinkSegmented } from '@/components'
import { Outlet, useNavigate } from 'react-router-dom'
import {
  Input,
  Space,
  Button,
  Pagination,
  App,
  Modal,
  message,
  Empty,
  Dropdown,
  Popconfirm,
  Spin
} from 'antd'
import { SearchOutlined, ExclamationCircleOutlined, MoreOutlined } from '@ant-design/icons'
// import styles from './style.module.scss'
import ajax from '@/utils/http'

const Remote = lazy(() => import('remote/remote-app'))

const Workfow = (props) => {
  return (
    <App>
      <Suspense fallback={<Spin />}>
        <Remote />
      </Suspense>
    </App>
  )
}

export default Workfow
