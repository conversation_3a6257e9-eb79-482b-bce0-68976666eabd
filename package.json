{"name": "sparklink-base-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --mode base", "start:base": "vite --mode base", "start:auto": "vite --mode auto", "start:aiui": "vite --mode aiui", "dev": "vite --mode base", "build": "vite build --mode base", "build:base": "vite build --mode base", "build:auto": "vite build --mode auto", "build:aiui": "vite build --mode aiui", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@ant-design/icons": "^5.5.1", "@ant-design/pro-components": "^2.8.6", "@micro-zoe/micro-app": "1.0.0-rc.18", "@microsoft/fetch-event-source": "^2.0.1", "ahooks": "^3.8.1", "antd": "^5.24.4", "antd-style": "^3.7.1", "axios": "^1.7.4", "blueimp-md5": "^2.19.0", "classnames": "^2.5.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "md5-js": "^0.0.3", "prop-types": "^15.8.1", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-router-dom": "^6.26.1", "react-transition-group": "^4.4.5", "reset-css": "^5.0.2", "uuid": "^10.0.0", "zustand": "^4.5.5"}, "devDependencies": {"@eslint/js": "^9.9.0", "@module-federation/vite": "^1.2.6", "@tailwindcss/line-clamp": "^0.4.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "postcss": "^8.4.45", "prettier": "^3.5.3", "sass": "^1.77.8", "sass-loader": "^16.0.1", "tailwindcss": "^3.4.11", "vite": "^5.4.1", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-svgr": "^4.2.0", "vite-plugin-top-level-await": "^1.5.0"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}