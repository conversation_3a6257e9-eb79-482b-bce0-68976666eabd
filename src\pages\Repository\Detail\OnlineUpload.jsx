import { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Select, Button, Form, message, Input, Popover, Typography } from 'antd'
import { getRepoInfo, saveDoc } from '../service'
import SplitConfig from '../SplitConfig'

const { TextArea } = Input
const { Option } = Select

const OnlineUpload = (props, ref) => {
  const { addLoading, setAddLoading, repoId, fileCurrent, finish } = props
  const [form] = Form.useForm()
  const [parseType, setParseType] = useState(1)

  const getDetail = () => {
    getRepoInfo({ repoId }).then((data) => {
      let res = data.data
      fillRepoForm(res.data)
    })
  }
  const fillRepoForm = (repoData) => {
    form.setFieldsValue({ urlString: '', updateFrequency: '' })
    let repoConfig = JSON.parse(repoData.repoConfig)
    if (repoConfig.parseSplitModel) {
      let parseModelInfo = JSON.parse(repoConfig.parseSplitModel.modelInfo)
      // console.log('分段类型',parseModelInfo);
      setParseType(parseModelInfo.parseType || 1)
      let parseConfig = parseModelInfo.parseConfig
      if (parseConfig) {
        form.setFieldsValue({
          strategy: parseConfig.chunkType.split(','),
          identifier: JSON.parse(parseConfig.cutOff || parseConfig.separator || '[]'),
          maxLength: JSON.parse(parseConfig.lengthRange)[1],
          symbolType: parseConfig.separator ? 'separator' : 'cutOff'
        })
      }
    }
  }

  const confirm = () => {
    form.validateFields().then(async (values) => {
      // console.log('在线表单values', values);
      let params = {
        repoId,
        updateFrequency: values.updateFrequency,
        upType: 3,
        parseType
      }
      if (parseType === 2) {
        let parseConfig = {
          chunkType: values.strategy.join(','),
          lengthRange: JSON.stringify([16, values.maxLength])
        }
        if (values.strategy.includes('separator')) {
          parseConfig[values.symbolType] = JSON.stringify(values.identifier)
        }
        params.parseConfig = JSON.stringify(parseConfig)
      }
      if (fileCurrent?.id) {
        params['docId'] = fileCurrent.id
        if(parseType == 1 && fileCurrent.parseType == 1) return
        if(fileCurrent.parseConfig == params.parseConfig) return
      } else {
        let urlArray = values.urlString.split('\n')
        urlArray = urlArray.filter(Boolean)
        params['parseUrl'] = urlArray.join(',')
      }

      setAddLoading(true)
      try {
        let data = await saveDoc(params)
        if (data.data.code == 0) {
          setAddLoading(false)
          message.success('保存成功，开始为您解析文档!')
          finish()
        }
      } catch (e) {
        setAddLoading(false)
      }
    })
  }
  useImperativeHandle(ref, () => ({
    confirm
    // reset: getDetail
  }))

  useEffect(() => {
    if (fileCurrent?.id) {
      form.setFieldsValue({
        urlString: fileCurrent.filePath,
        updateFrequency: fileCurrent.updateFrequency
      })
      setParseType(fileCurrent.parseType)
      if (fileCurrent.parseConfig) {
        let parseConfig = JSON.parse(fileCurrent.parseConfig)
        form.setFieldsValue({
          strategy: parseConfig.chunkType.split(','),
          identifier: JSON.parse(parseConfig.cutOff || parseConfig.separator || '[]'),
          maxLength: JSON.parse(parseConfig.lengthRange)[1],
          symbolType: parseConfig.separator ? 'separator' : 'cutOff'
        })
      }
    } else {
      form.setFieldsValue({ urlString: '' })
      getDetail()
    }
  }, [fileCurrent])

  const tipContent = (
    <div>
      <p style={{ color: '#262626' }}>为了保障网页解析效果，建议上传的URL</p>
      <ul style={{ color: '#6b6b6b', marginBottom: '12px' }}>
        <li>(1)在国内有备案</li>
        <li>(2)网页内容以文本为主</li>
        <li>(3)无需三方登录</li>
      </ul>
    </div>
  )
  return (
    <Form form={form} labelCol={{ span: 4 }}>
      <Form.Item label="网页地址" required>
        <Form.Item
          name="urlString"
          noStyle
          rules={[
            { required: true, message: '请输入网页URL' },
            {
              validator: (rule, value) => {
                let urlArray = value.split('\n')
                urlArray = urlArray.filter(Boolean)
                if (urlArray.length > 10) {
                  return Promise.reject(new Error('单次上传不超过10个URL'))
                } else {
                  return Promise.resolve()
                }
              },
              trigger: 'change'
            }
          ]}
        >
          <TextArea
            rows={4}
            disabled={fileCurrent?.id}
            placeholder="请输入网页URL，多个地址请换行分隔，单次上传不超过10个URL。"
          />
        </Form.Item>
        <Popover placement="topLeft" content={tipContent}>
          <Typography.Link href="#API">上传建议</Typography.Link>
        </Popover>
      </Form.Item>
      <Form.Item
        label="更新频率"
        name="updateFrequency"
        rules={[{ required: true, message: '请选择更新频率' }]}
      >
        <Select style={{ width: 290 }} placeholder="请选择网页更新频率">
          <Option value="NONE">不更新</Option>
          <Option value="DAY">每日</Option>
          <Option value="WEEK">每周</Option>
          <Option value="MONTH">每月</Option>
        </Select>
      </Form.Item>
      <Form.Item label="拆分方法">
        <SplitConfig form={form} parseType={parseType} setParseType={setParseType} />
      </Form.Item>
    </Form>
  )
}

export default forwardRef(OnlineUpload)
