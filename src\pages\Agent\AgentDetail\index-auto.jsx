import ConfigRight from './ConfigRight'
import ConfigRightCI<PERSON> from './ConfigRight/civi'
import styles from './style.module.scss'
import { useEffect } from 'react'
import ajax from '@/utils/http'
import { useParams, useNavigate } from 'react-router-dom'
import { useAgentDetailStore } from './store'

function AgentDetail() {
  const { agentId } = useParams()
  const setAgentDetail = useAgentDetailStore((state) => state.setAgentDetail)
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  // 智能体详情页获取智能体详情

  useEffect(() => {
    getAgentDetail()
  }, [])

  const getAgentDetail = async () => {
    // /bot/getBotInfo?uid=20889&id=8
    let data = window.microApp?.getData() || {}
    const { appid, scene } = data

    const res = await ajax({
      url: '/bot/getBotInfoByApp',
      data: {
        appId: appid,
        sceneName: scene
      },
      method: 'post'
    })
    if (res.data.code === '0') {
      console.log('agent详情页获取的agent详情信息：', res.data?.data)
      // const boxBotId = res.data?.data?.boxBot?.botId
      setAgentDetail(res.data?.data)
    }
  }

  const isMicroEnv = window.__MICRO_APP_ENVIRONMENT__
  // 微前端环境下使用civi定制样式
  return isMicroEnv ? <ConfigRightCIVI /> : <ConfigRight />
}
export default AgentDetail
