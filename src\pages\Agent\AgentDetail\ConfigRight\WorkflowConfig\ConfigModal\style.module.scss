.search {
  width: '100%';
  margin-bottom: 16px;
}

// 卡片样式
.itemCard {
  display: flex;
  align-items: center;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: linear-gradient(180deg, #ffffff, #fafafa 100%);
  border: 1px solid #d1d5db;
}

// 复选框
.checkbox {
  margin-right: 20px;
}

// 内容部分
.content {
  flex: 1;
}

// 标题
.title {
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  line-height: 18px;
}

// 副标题
.subtitle {
  color: #aaa;
  margin-left: 6px;
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  line-height: 14px;
}

// 描述文本
.description {
  margin-top: 4px;
  font-size: 13px;
  font-weight: 400;
  color: #6b7280;
  line-height: 15px;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  width: 350px;
}

// 版本选择框
.select {
  width: 80px;
  margin-right: 12px;
}

// 按钮
.button {
  white-space: nowrap;
  margin: 0 12px;
}
