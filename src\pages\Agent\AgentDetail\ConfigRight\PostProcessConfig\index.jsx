import { Collapse } from 'antd'
import styles from './style.module.scss'
import IntervConfigIcon from 'assets/svgs/IntervConfigIcon.svg?react'
import { useState, useEffect } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import ModelConfigModal from './ConfigModal'
import { RightOutlined } from '@ant-design/icons'

function IntervConfig() {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [modelConfigModalOpen, setModelConfigModalOpen] = useState(false)
  const [activeKey, setActiveKey] = useState(['interv'])

  const onModelConfigClick = () => {
    setModelConfigModalOpen(true)
  }

  const items = [
    {
      key: 'model',
      label: <div>模型前后配置</div>,
      extra: (
        <div
          className={styles.configWrapper}
          onClick={(e) => {
            e.stopPropagation()
            onModelConfigClick()
          }}
        >
          <span className={styles.configButton}>
            <IntervConfigIcon />
          </span>
          <span>配置</span>
        </div>
      )
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        ghost
        onChange={onChange}
        expandIcon={() => null}
      />

      <ModelConfigModal
        visible={modelConfigModalOpen}
        onClose={() => setModelConfigModalOpen(false)}
      />
    </>
  )
}

export default IntervConfig
