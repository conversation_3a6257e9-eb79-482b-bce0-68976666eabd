import React, { useEffect, useState, useRef, useImperativeHandle, forwardRef, useMemo } from 'react'
import { Collapse, Form, Input, Select, Switch, message, Button, Space, Row, Col } from 'antd'
import md5 from 'blueimp-md5'
import ajax from '@/utils/http' // ← 你的 axios 封装
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import styles from './style.module.scss'

const { Option } = Select

const LlmPostProcess = forwardRef((props, ref) => {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  const {
    type = 1,
    text = {
      name: '后处理',
      desc: '对语音识别和语义理解的结果进行自定义处理。'
    },
    visible
  } = props

  /** ----------------------- 表单与本地状态 ----------------------- */
  const [form] = Form.useForm()
  const [swtichIsOn, setSwtichIsOn] = useState(true)
  const [isBackupUrlValid, setIsBackupUrlValid] = useState(true)

  /** -------------- 生成随机 token / aesKey，与原逻辑一致 -------------- */
  const getToken = () => {
    const rand = parseInt(Math.random() * 15, 10)
    const res = md5(`${Date.now()}${rand}`)
    return res.substring(rand, rand + 16)
  }

  /** ------------------------- 初始化表单 ------------------------- */
  useEffect(() => {
    if (visible && agentDetail?.boxBot?.botId) {
      getLLMConf()
    }
  }, [visible, agentDetail])

  /** --------------------- timeout 上限计算 ---------------------- */
  const timeOutLimit = () => {
    const retries = parseInt(form.getFieldValue('retries') || 1, 10)
    const backupReties = parseInt(form.getFieldValue('backupReties') || 1, 10)
    if (isBackupUrlValid && form.getFieldValue('backupUrl')) {
      return 9000 / (retries + backupReties)
    }
    return retries === 3 ? 3000 : retries === 2 ? 4500 : 9000
  }

  /** -------------------- 校验函数（完全翻译） -------------------- */
  /** URL 检查（含后端校验） */
  const checkUrl = async (_, value) => {
    if (!value) return Promise.resolve()

    // 局域网 IP 不允许
    if (
      type === 1 &&
      /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|[wW][sS]{1,2}:\/\/)((192\.168|172\.([1][6-9]|[2]\d|3[01]))(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){2}|10(\.([2][0-4]\d|[2][5][0-5]|[01]?\d?\d)){3})/.test(
        value
      )
    ) {
      return Promise.reject(new Error('不支持内网IP'))
    }

    // if (type === 1) {
    //     try {
    //       const res = await ajax({
    //         url: '/app/callback/checkUrl', // 后处理url校验
    //         method: 'post',
    //         data: {
    //           botId: agentDetail?.boxBot?.botId,
    //           url: value,
    //           h_token: form.getFieldValue('token')
    //         }
    //       })
    //       if (res.data.code !== 0) {
    //         return Promise.reject(new Error(res.data.message))
    //       }
    //     } catch (err) {
    //       return Promise.reject(new Error(err.message || '校验失败'))
    //     }
    // }
    return Promise.resolve()
  }

  /** timeout 范围校验 */
  const checkTimeout = (_, value) => {
    if (value < 100 || value > timeOutLimit()) {
      const retries = parseInt(form.getFieldValue('retries'), 10)
      const backupReties = parseInt(form.getFieldValue('backupReties'), 10)
      const msg =
        isBackupUrlValid && form.getFieldValue('backupUrl')
          ? `尝试次数为${retries + backupReties}次时，超时时间可设范围为100-${timeOutLimit()}ms`
          : `尝试次数为${retries}次时，超时时间可设范围为100-${timeOutLimit()}ms`
      return Promise.reject(new Error(msg))
    }
    return Promise.resolve()
  }

  /** url 与 backupUrl 重复性校验 */
  const isUrlRepeat = (_, value) => {
    if (value && value === form.getFieldValue('backupUrl')) {
      return Promise.reject(new Error('链接不能重复'))
    }
    return Promise.resolve()
  }
  const checkUrlRepeat = (_, value) => {
    if (value && value === form.getFieldValue('url')) {
      return Promise.reject(new Error('链接不能重复'))
    }
    return Promise.resolve()
  }

  // 对应 Vue configSwitch
  const configSwitch = (checked) => {
    setSwtichIsOn(checked)
  }

  // 复制
  const handleCopy = (field) => {
    navigator.clipboard.writeText(form.getFieldValue(field))
    message.success('已复制到剪切板')
  }

  // 重新生成 token/aesKey
  const regenToken = (field) => {
    form.setFieldValue(field, getToken())
  }

  /** ---------------------- 请求接口 ---------------------- */
  // 拉取配置
  const getLLMConf = async () => {
    try {
      const res = await ajax({
        url: '/bot/config/handle/get',
        method: 'get',
        data: {
          botId: agentDetail?.boxBot?.botId,
          type
        }
      })
      if (res.data.code === '0' && res.data.data) {
        const d = res.data.data
        const base = {
          ...d,
          retries: parseInt(d.retries, 10) || 1,
          isEncrypt: Number(d.isEncrypt) === 1,

          token: d.token || getToken(),
          aesKey: d.aesKey || getToken(),
          backupUrl: '',
          backupReties: 1
        }
        if (d.moreConfig) {
          try {
            const arr = JSON.parse(d.moreConfig)
            if (Array.isArray(arr) && arr[0]) {
              base.backupUrl = arr[0].url || ''
              base.backupReties = arr[0].retries || 1
            }
          } catch {
            /* ignore */
          }
        }
        form.setFieldsValue(base)
        setSwtichIsOn(base.swtich === 1)
      } else {
        message.error(res.data.message)
      }
    } catch (err) {
      console.error(err)
    }
  }

  /** ------------------- 暴露方法给父组件 ------------------- */
  useImperativeHandle(ref, () => ({
    validate: async () => {
      try {
        await form.validateFields()
        return true
      } catch {
        // message.error(type === 2 ? '前处理参数配置校验未通过' : '后处理参数配置校验未通过')
        return false
      }
    },
    save: async () => {
      const values = form.getFieldsValue()
      const payload = {
        botId: agentDetail?.boxBot?.botId,
        url: values.url,
        token: values.token || getToken(),
        aesKey: values.aesKey || getToken(),
        isEncrypt: values.isEncrypt ? 1 : 0,
        timeout: values.timeout,
        retries: values.retries,
        swtich: swtichIsOn ? 1 : 0,
        type
      }
      if (values.backupUrl) {
        payload.moreConfig = JSON.stringify([
          { url: values.backupUrl, retries: values.backupReties }
        ])
      } else {
        payload.moreConfig = ''
        payload.backupReties = 1
      }

      try {
        const res = await ajax({
          url: '/bot/config/handle/save', //
          method: 'post',
          data: payload
        })
        if (res.data.code === '0') {
          return true
        } else {
          message.error(res.data.message)
          return false
        }
      } catch (err) {
        console.error(err)
        return false
      }
    }
  }))

  /** ------------------ backupUrl 检验状态 ------------------ */
  const backUrlChange = () => {
    // 每次 backupUrl 变化立即校验
    const validate = async () => {
      try {
        await form.validateFields(['backupUrl'])
        setIsBackupUrlValid(true)
      } catch {
        setIsBackupUrlValid(false)
      }
    }
    validate()
    // () => form.validateFields(['timeout', 'url'])
  }

  /** ----------------------------- 渲染 ----------------------------- */
  const nullMessage = type === 2 ? '前处理URL不能为空' : '后处理URL不能为空'

  const repeatTimeList = [
    { label: '1次', value: 1 },
    { label: '2次', value: 2 },
    { label: '3次', value: 3 }
  ]

  /** Collapse header */
  const header = (
    <div className="config-header">
      <p className={styles['config-title']} id="anchor">
        <Switch checked={swtichIsOn} className="mgr16" onChange={configSwitch} />
        &nbsp;{text.name}
      </p>
      <p className={styles['config-desc']}>{text.desc}</p>
    </div>
  )

  const [formChanged, setFormChanged] = useState(0)

  const urlReg =
    /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/|[wW][sS]{1,2}:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~/])+$/

  const items = [
    {
      key: '1',
      label: header,
      children: (
        <Form
          form={form}
          labelCol={{ flex: '100px' }}
          labelAlign="left"
          colon={false}
          onValuesChange={() => setFormChanged((v) => v + 1)}
        >
          <Row>
            <Col span={16}>
              {/* 主 URL */}
              <Form.Item
                label={type === 2 ? '前处理链接' : '后处理链接'}
                name="url"
                rules={[
                  { required: true, message: nullMessage },
                  {
                    pattern: urlReg,
                    message: 'URL格式不正确'
                  },
                  { validator: checkUrl },
                  { validator: isUrlRepeat }
                ]}
              >
                <Input className="config-input" placeholder="请填写" allowClear />
              </Form.Item>
            </Col>
            <Col span={7} style={{ marginLeft: 20 }}>
              {/* 主 URL 重试次数 */}
              <Form.Item label="尝试次数" name="retries">
                <Select>
                  {repeatTimeList.map((item) => (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 备用 URL */}
          <Row>
            <Col span={16}>
              <Form.Item
                label="备用链接"
                name="backupUrl"
                rules={[
                  {
                    pattern: urlReg,
                    message: 'URL格式不正确1'
                  },
                  { validator: checkUrlRepeat },
                  { validator: checkUrl }
                ]}
              >
                <Input placeholder="请填写" allowClear onChange={backUrlChange} />
              </Form.Item>
            </Col>
            <Col span={7} style={{ marginLeft: 20 }}>
              {/* 备用 URL 重试次数 */}
              <Form.Item label="尝试次数" name="backupReties">
                <Select>
                  {repeatTimeList.map((item) => (
                    <Option key={item.value} value={item.value}>
                      {item.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* timeout */}
          <Form.Item
            label="超时时间"
            name="timeout"
            dependencies={['backupReties', 'retries']}
            rules={[
              { required: true, message: '超时时间不能为空' },
              { pattern: /^[0-9]+$/, message: '仅支持数字' },
              { validator: checkTimeout }
            ]}
          >
            <Input
              style={{ width: 400 }}
              className="config-input"
              placeholder={`尝试次数为${
                form.getFieldValue('retries') +
                (isBackupUrlValid && form.getFieldValue('backupUrl')
                  ? (form.getFieldValue('backupReties') ?? 0)
                  : 0)
              }次时，超时时间可设范围为100-${timeOutLimit()}ms`}
              suffix="ms"
              allowClear
            />
          </Form.Item>
          {/* token */}
          <Space size={4} align="center">
            <Form.Item label="校验token" name="token">
              <Input disabled style={{ width: 200 }} />
            </Form.Item>
            <Form.Item>
              <Button className="mgr24" onClick={() => handleCopy('token')} type="link">
                复制
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => regenToken('token')} type="link">
                重新生成
              </Button>
            </Form.Item>
          </Space>

          {/* isEncrypt */}
          <Form.Item label="消息是否加密" name="isEncrypt" valuePropName="checked">
            <Switch />
          </Form.Item>
          {/* aesKey */}
          <Space size={4} align="center">
            <Form.Item label="加密AES KEY" name="aesKey">
              <Input disabled />
            </Form.Item>
            <Form.Item>
              <Button className="mgr24" onClick={() => handleCopy('aesKey')} type="link">
                复制
              </Button>
            </Form.Item>
            <Form.Item>
              <Button onClick={() => regenToken('aesKey')} type="link">
                重新生成
              </Button>
            </Form.Item>
          </Space>
        </Form>
      )
    }
  ]

  return (
    <Collapse
      bordered={false}
      ghost
      items={items}
      activeKey={swtichIsOn ? ['1'] : []}
      expandIcon={() => null}
    ></Collapse>
  )
})

export default LlmPostProcess
