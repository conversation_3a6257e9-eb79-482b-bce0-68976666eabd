import React, { useState, useEffect } from 'react'
import styles from './style.module.scss'

const Inputing = () => {
  const [index, setIndex] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setIndex((prevIndex) => (prevIndex > 2 ? 0 : prevIndex + 1))
    }, 200)

    return () => clearInterval(timer)
  }, [])

  return (
    <div className={styles.inputing}>
      <span className={`${styles.pot} ${index === 0 ? styles.active : ''}`}></span>
      <span className={`${styles.pot} ${index === 1 ? styles.active : ''}`}></span>
      <span className={`${styles.pot} ${index === 2 ? styles.active : ''}`}></span>
    </div>
  )
}

export default Inputing
