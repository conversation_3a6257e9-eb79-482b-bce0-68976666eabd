export const SEARCH_TYPE__OPTIONS = [
  { label: '聚合搜索摘要版本', value: 'SearchAggSummary' },
  { label: '聚合搜索全文版本', value: 'SearchAggContent' }
]

export const STRATEGY_OPTIONS = [
  { label: '知识库优先', value: 'serial' },
  { label: '知识库和搜索并行', value: 'parallel' }
]

// 单人拒识
export const REJECT_CONFIG_OPTION = [
  { label: '人设拒识', value: 'personaAble' },
  { label: '共情拒识', value: 'empathyAble' },
  { label: '实体单说拒识', value: 'arcAble' },
  { label: '端状态指代拒识', value: 'referenceAble' },
  { label: '完整且通顺拒识', value: 'completeFluencyAble' }
]

// 多人拒识
export const MULTI_REJECT_CONFIG_OPTION = [
  { label: '人设拒识', value: 'multiPersonaAble' },
  { label: '共情拒识', value: 'multiEmpathyAble' },
  { label: '实体单说拒识', value: 'multiArcAble' },
  { label: '端状态指代拒识', value: 'multiReferenceAble' },
  { label: '完整且通顺拒识', value: 'multiCompleteFluencyAble' }
]

export const REJECT_CONFIG_KEYS = REJECT_CONFIG_OPTION.map((x) => x.value)

export const MULTI_REJECT_CONFIG_KEYS = MULTI_REJECT_CONFIG_OPTION.map((x) => x.value)
