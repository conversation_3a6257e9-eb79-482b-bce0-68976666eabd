import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import svgr from 'vite-plugin-svgr'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'
import { resolve } from 'path'
import { federation } from '@module-federation/vite'
import topLevelAwait from 'vite-plugin-top-level-await'
import { dependencies } from './package.json'

// async function getRemoteEntryUrl(env) {
//   const manifestUrl = `${env.VITE_ROUTER_BASE_URL}/flow/assets/manifest.json`
//   try {
//     const res = await fetch(manifestUrl)
//     const { remoteEntry } = await res.json()
//     return `${env.VITE_ROUTER_BASE_URL}/flow/assets/${remoteEntry}`
//   } catch (error) {
//     console.error('Failed to fetch remote manifest:', error)
//     return `${env.VITE_ROUTER_BASE_URL}/flow/assets/remoteEntry.js`
//   }
// }

const proxys = {
  base: {
    '/iflycloud/api': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'http://aitest.iflyaicloud.com:8000',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    },
    '/api/v1': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'http://aitest.iflyaicloud.com:8000',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    }
  },
  auto: {
    '/auto/api': {
      target: 'https://dev.iflyaicloud.com',
      // target: 'http://aitest.iflyaicloud.com:8000',
      // target: 'https://pre.iflyaicloud.com',
      changeOrigin: true
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const proxy = proxys[env.VITE_APP_ENV]
  return {
    base: `${env.VITE_ROUTER_BASE_URL}`,
    css: {
      // preprocessorOptions: {
      //   scss: {
      //     // 全局引入的 SCSS 文件路径
      //     additionalData: `@import "assets/css/variables.scss";`
      //   }
      // }
    },
    plugins: [
      svgr(),
      federation({
        name: 'host',
        // remotes: {
        //   remote: `${env.VITE_ROUTER_BASE_URL}/flow/remoteEntry.js`
        // },
        remotes: {
          remote: {
            type: 'module',
            name: 'remote',
            entry: `${env.VITE_ROUTER_BASE_URL}/flow/remoteEntry.js`, // 动态获取 remoteEntry.js,
            entryGlobalName: 'remote',
            shareScope: 'default'
          }
        },
        shared: ['react', 'react-dom', 'react-router-dom']
      }),
      react(),
      topLevelAwait(),
      cssInjectedByJsPlugin()
    ],
    resolve: {
      alias: {
        '@': resolve('./src'),
        assets: resolve('./src/assets')
      }
    },
    server: {
      port: 9999,
      cors: true, // 开启跨域支持
      proxy
    }
  }
})
