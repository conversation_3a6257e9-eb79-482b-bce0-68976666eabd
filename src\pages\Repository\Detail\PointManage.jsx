/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event'
import React, { useState, useEffect, useRef } from 'react'
import { useUpdateEffect } from '@/hooks/useUpdateEffect'
import { useParams, useNavigate, useLocation, useSearchParams } from 'react-router-dom'
import { Select, Button, message, Spin } from 'antd'
import { getRepoFolders, getDocList, buildRepo } from '../service'
import { useRepoStore } from '../repoStore'
// import microApp from '@micro-zoe/micro-app'
import styles from './style.module.scss'

const PointManage = () => {
  const { repoId } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const [searchParams, setSearchParams] = useSearchParams()

  const { setCheckMethod, setEditState } = useRepoStore()
  const [folderSelected, setFolderSelected] = useState()
  const [docSelected, setDocSelected] = useState(null)
  const [fileInfo, setFileInfo] = useState()
  const [folderOptions, setFolderOptions] = useState([])
  const [fileOptions, setFileOptions] = useState([])
  const [trackRefId, setTrackRefId] = useState('')
  const [microLoading, setMicroLoading] = useState(true)
  // const [isEditing, setIsEditing] = useState

  const iframeRef = useRef()

  const microUrl =
    process.env.NODE_ENV === 'development'
      ? 'http://localhost:5001/console/aiuikb/fileCollect'
      : `${new URL(document.baseURI).origin}/console/aiuikb/fileCollect?token=${window?.__DATA_FROM_PARENT__?.cookie || ''}`

  const checkQuery = () => {
    const query = new URLSearchParams(location.search)
    const docId = query.get('docId')
    const chunkId = query.get('chunkId')
    console.log('location参数', docId, chunkId)

    if (docId) {
      setDocSelected(Number(docId))
    }
    if (chunkId) {
      setTrackRefId(chunkId)
    }
    navigate(location.pathname, { replace: true })
  }

  useEffect(() => {
    console.log(
      '微前端地址',
      microUrl,
      window.location,
      window?.rawWindow?.location,
      new URL(document.baseURI).origin
    )

    const onIframeLoad = () => {
      console.log('iframe 加载完毕（通过原生事件）')
      getFolderOptions()
      setMicroLoading(false)
    }
    const handleIframeMessage = (event) => {
      if (event.data?.type) {
        let { type } = event.data
        if (type === 'check-edit-result') {
          setEditState(event.data.result || false)
        }
      }
      // if (event.data?.type === 'get-token') {
      //   console.log('重发token')
      //   iframeRef.current.contentWindow.postMessage(
      //     {
      //       docInfo: fileInfo,
      //       'X-Auto-Token': window?.__DATA_FROM_PARENT__?.cookie
      //     },
      //     '*'
      //   )
      // }
    }

    window.addEventListener('message', handleIframeMessage)

    const iframe = iframeRef.current
    iframe.addEventListener('load', onIframeLoad)

    return () => {
      setEditState(false)
      iframe.removeEventListener('load', onIframeLoad)
      // window.removeEventListener('message', handleIframeMessage)
    }
  }, [])

  useEffect(() => {
    let docInfo = fileOptions.find((item) => item.id == docSelected)
    // console.log('docInfo', docInfo)
    setFileInfo(docInfo)
  }, [docSelected])

  useEffect(() => {
    console.log('父页面发送postMessage', fileInfo)
    iframeRef.current.contentWindow.postMessage(
      {
        docInfo: fileInfo,
        'X-Auto-Token': window?.__DATA_FROM_PARENT__?.cookie
      },
      '*'
    )
  }, [fileInfo])

  useUpdateEffect(() => {
    getDocOptions()
  }, [folderSelected])

  useUpdateEffect(() => {
    const query = new URLSearchParams(location.search)
    const docId = query.get('docId')
    console.log('page query', query, docId)
    if (docId) {
      checkQuery()
    } else if (fileOptions.length > 0) {
      setDocSelected(fileOptions[0]['id'])
    }
  }, [fileOptions])

  useUpdateEffect(() => {
    // Handle route query changes
    // console.log('query更新');
    checkQuery()
  }, [location.search])

  const getFolderOptions = () => {
    getRepoFolders({ repoId }).then((data) => {
      let res = data.data
      let labelData = res.data.folderList
      let defaultData = {
        folderName: '全部',
        key: '全部',
        id: '全部'
      }
      setFolderOptions([defaultData, ...res.data.folderList])
      setFolderSelected('全部')
    })
  }

  const getDocOptions = async () => {
    let params = {
      repoId,
      folderId: folderSelected === '全部' ? '' : folderSelected,
      pageSize: 9999
    }
    let data = await getDocList(params)
    let res = data.data
    setFileOptions(res.data.docPage.records)
  }

  const handleFolderChange = (value) => {
    setFolderSelected(value)
  }
  const handleFileChange = (value) => {
    console.log('文件选择', value)
    setDocSelected(value)
    if (trackRefId) {
      setTrackRefId('')
    }
    navigate(location.pathname, { replace: true })
  }

  const microMounted = () => {
    console.log('微前端组件挂载')
    setMicroLoading(false)
    // microApp.setData('rag-app', { type: '' })
  }

  const microError = () => {
    setMicroLoading(false)
    console.log('微前端组件挂载出错')
    // microApp.setData('rag-app', { type: 'loadError' })
  }

  return (
    <div className={styles.pointManage}>
      <div className={styles.docHeader}>
        <Select
          value={folderSelected}
          placeholder="请选择标签"
          style={{ width: 220, marginRight: '12px' }}
          onChange={handleFolderChange}
        >
          {folderOptions.map((item) => (
            <Select.Option key={item.id} value={item.id} label={item.folderName}>
              {item.folderName}
            </Select.Option>
          ))}
        </Select>

        <Select
          value={docSelected}
          placeholder="请选择文件"
          style={{ width: 400 }}
          onChange={handleFileChange}
          allowClear
        >
          {fileOptions.map((item) => (
            <Select.Option
              key={item.id}
              value={item.id}
              label={item.docName}
              disabled={item.extractStatus !== 1}
            >
              {item.docName}
            </Select.Option>
          ))}
        </Select>
      </div>

      {/* <div className={microLoading ? styles.ragLoading : styles.ragMicro}>
        <micro-app-rag
          name="rag-app"
          url={microUrl}
          data={{
            docSelected: fileInfo,
            trackRefId,
            'X-Auto-Token': window?.__DATA_FROM_PARENT__?.cookie
          }}
          default-page="fileCollect/"
          router-mode="pure"
          // router-mode='native'
          iframe
          onMounted={microMounted}
          onError={microError}
        />
      </div> */}
      <div className={`${styles.ragMicro} ${microLoading ? styles.ragLoading : ''}`}>
        {microLoading && <Spin size="large" />}
        <iframe
          ref={iframeRef}
          src={microUrl}
          title="rag采编"
          width="100%"
          height="100%"
          allow="fullscreen"
        ></iframe>
      </div>
    </div>
  )
}

export default PointManage
