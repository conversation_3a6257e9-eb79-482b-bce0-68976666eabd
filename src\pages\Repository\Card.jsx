import { Dropdown, Popconfirm, Modal, message, App } from 'antd'
import { useNavigate } from 'react-router-dom'
import { useState } from 'react'
import { MoreOutlined, ExclamationCircleFilled } from '@ant-design/icons'
import styles from './style.module.scss'
import { deleteRepo } from './service'

const Card = (props) => {
  const { cardData, refreshList, onCardClick } = props
  const [modal, contextHolder] = Modal.useModal()
  const navigate = useNavigate()
  const [delLoading, setDelLoading] = useState(false)

  const optItems = [
    {
      key: 'edit',
      label: <span>编辑</span>
    },
    {
      key: 'delete',
      label: (
        <span
          onClick={(e) => {
            e.stopPropagation()
            onDropItemsClick({ key: 'delete' }, cardData)
          }}
        >
          删除
        </span>
      )
    }
  ]
  const onDropItemsClick = (e, l) => {
    console.log('e', e)
    const key = e.key
    e?.domEvent?.stopPropagation()
    switch (key) {
      case 'edit':
        navigate(`/repo/${l.id}/baseInfo`)
        break
      case 'copy':
        onCopyClick(l)
        break
      case 'delete':
        Modal.confirm({
          title: `确认是否删除 ${l.name}`,
          icon: <ExclamationCircleFilled />,
          content: '将删除该知识库下的所有数据，删除后不可恢复，请谨慎操作',
          okButtonProps: { loading: delLoading },
          onOk(close) {
            try {
              deleteRepo({ repoId: l.id }).then((data) => {
                let res = data.data
                if (res.code == 0) {
                  message.success('操作成功')
                  close()
                  refreshList()
                }
                setDelLoading(false)
              })
            } catch (e) {
              setDelLoading(false)
              return
            }
          },
          onCancel() {
            console.log('Cancel')
          }
        })
        break
    }
  }

  return (
    <div className={styles.itemCard} onClick={() => onCardClick(cardData)}>
      <div className={styles.content}>
        <div className={styles.title}>
          {cardData.name}
          <span className={styles.subtitle}>{cardData.botId}</span>
        </div>
        <div className={styles.description}>{cardData.description || ''}</div>
      </div>
      <Dropdown
        menu={{
          items: optItems,
          onClick: (e) => {
            e.domEvent.stopPropagation()
            onDropItemsClick(e, cardData)
          }
        }}
        placement="bottom"
        onClick={(e) => e.stopPropagation()}
      >
        <MoreOutlined style={{ fontSize: 20 }} />
      </Dropdown>
      {/* {contextHolder} */}
    </div>
  )
}

export default Card
