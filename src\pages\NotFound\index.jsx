import { Button, Result } from 'antd'
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom'

function NotFound() {
  const navigate = useNavigate()
  const onBack = () => {
    navigate('/')
  }
  return (
    <Result
      status="404"
      title="404"
      subTitle="功能敬请期待"
      extra={
        <Button type="primary" onClick={onBack}>
          返回
        </Button>
      }
    />
  )
}
export default NotFound
