import { MyModal } from '@/components'
import { useState, useEffect, useMemo } from 'react'
import PropTypes from 'prop-types'
import { Switch, Select, Space, Spin } from 'antd'
import ajax from '@/utils/http'
import { awaitTo } from '@/utils/util'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { SEARCH_TYPE__OPTIONS, STRATEGY_OPTIONS } from '../constant'
import { useConfigRightResource } from '../hooks/useConfigRight'

const SearchModal = ({ visible, onClose, onRefresh }) => {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [searchSwitch, setSearchSwitch] = useState(false)
  const [searchParams, setSearchParams] = useState({
    searchType: 'SearchAggSummary',
    strategy: 'serial'
  })
  const [confirmLoading, setConfirmLoading] = useState(false)
  const botId = useMemo(() => agentDetail?.boxBot?.botId, [agentDetail])

  const { list, loading, fetchResources } = useConfigRightResource('search', visible)

  useEffect(() => {
    if (visible) {
      fetchResources()
    }
  }, [visible, fetchResources])

  useEffect(() => {
    if (list.length) {
      setSearchSwitch(true)
      setSearchParams((prev) => ({ ...prev, searchType: list[0].id, strategy: list[1].id }))
    } else {
      setSearchSwitch(false)
    }
  }, [list])

  useEffect(() => {
    const params = searchSwitch
      ? { searchType: 'SearchAggSummary', strategy: 'serial' }
      : { searchType: 'Close' }
    setSearchParams(params)
  }, [searchSwitch])

  const onOk = async () => {
    const params = { ...searchParams, botId }
    const api = () =>
      ajax({ url: '/bot/config/saveAbleAndSearchConfig', data: params, method: 'post' })
    const [err] = await awaitTo(api(), setConfirmLoading, { showSuccess: true })
    if (err) return false
    onClose()
    onRefresh()
  }

  const SelectRender = () => {
    return (
      <Space className="mt-[16px] mb-[26px] flex" styles={{ item: { flex: 1 } }}>
        <Select
          value={searchParams.searchType}
          className="w-full"
          placeholder="请选择"
          onChange={(value) => setSearchParams((prev) => ({ ...prev, searchType: value }))}
        >
          {SEARCH_TYPE__OPTIONS.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
        <Select
          value={searchParams.strategy}
          className="w-full"
          placeholder="请选择"
          onChange={(value) => setSearchParams((prev) => ({ ...prev, strategy: value }))}
        >
          {STRATEGY_OPTIONS.map((item) => (
            <Select.Option key={item.value} value={item.value}>
              {item.label}
            </Select.Option>
          ))}
        </Select>
      </Space>
    )
  }

  const titleNode = () => {
    return (
      <Space className="flex items-center">
        <Switch
          value={searchSwitch}
          className="relative -top-[2px]"
          size="small"
          onChange={setSearchSwitch}
        />
        <span>搜索配置</span>
      </Space>
    )
  }

  return (
    <MyModal
      width={440}
      confirmLoading={confirmLoading}
      title={titleNode()}
      open={visible}
      onCancel={onClose}
      onOk={onOk}
    >
      <Spin spinning={loading}>{searchSwitch && SelectRender()}</Spin>
    </MyModal>
  )
}

SearchModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onRefresh: PropTypes.func
}

export default SearchModal
