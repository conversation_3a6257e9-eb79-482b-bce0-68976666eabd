import { <PERSON><PERSON><PERSON>, Col, Row, Space, Tooltip, Button, Tag } from 'antd'
import styles from './style.module.scss'
import { useState } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import IntervConfigModal from '../InterModal'
import { useConfigRightResource } from '../hooks/useConfigRight'
import {
  MULTI_REJECT_CONFIG_KEYS,
  REJECT_CONFIG_KEYS,
  REJECT_CONFIG_OPTION,
  MULTI_REJECT_CONFIG_OPTION
} from '../constant'

import { RightOutlined } from '@ant-design/icons'

function IntervConfig() {
  const [configModalOpen, setConfigModalOpen] = useState(false)

  const [activeKey, setActiveKey] = useState(['interv'])

  const { list, fetchResources } = useConfigRightResource('atomicAbility', true)

  const renderList = (list || []).filter(
    (x) => x.interventions?.length || REJECT_CONFIG_KEYS.some((y) => x[y] == 2)
  )

  const onAddClick = (e) => {
    e.stopPropagation()
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const items = [
    {
      key: 'interv',
      label: (
        <div className={styles.labelWithIcon}>
          <RightOutlined
            className={activeKey.includes('interv') ? styles.arrowExpanded : styles.arrow}
          />
          <span>原子能力配置</span>
        </div>
      ),
      children: (
        <div className={styles.tagWrap}>
          {renderList.map((item) => {
            const { abilityCode, packages, abilityName, interventions } = item
            const { number, name, version } =
              packages?.find((y) => y.id == interventions?.[0]?.id) || {}

            const rejectArr = REJECT_CONFIG_KEYS.filter((y) => item[y] == 2)
            const multiRejectArr = MULTI_REJECT_CONFIG_KEYS.filter((y) => item[y] == 2)

            console.log('rejectArr', rejectArr)
            console.log('multiRejectArr', multiRejectArr)

            const rejectItems = rejectArr.map((key) => {
              return {
                name: REJECT_CONFIG_OPTION.find((y) => y.value == key)?.label,
                multi: false
              }
            })

            const multiRejectItems = multiRejectArr.map((key) => {
              return {
                name: MULTI_REJECT_CONFIG_OPTION.find((y) => y.value == key)?.label,
                multi: true
              }
            })

            const allItems = [...rejectItems, ...multiRejectItems]

            const rejectTextRender = () => {
              if (allItems.length === 0) {
                return null
              } else {
                const simple = (
                  <Tag color={allItems[0].multi ? 'green' : 'blue'}>
                    {allItems[0].multi ? '多人' : '单人'}
                  </Tag>
                )
                if (allItems.length === 1) {
                  return (
                    <div>
                      <span style={{ marginRight: 4 }}>{allItems[0]?.name}</span>
                      {simple}
                    </div>
                  )
                } else {
                  const displayTipText = (
                    <>
                      {rejectItems.length > 0 && (
                        <div>
                          <Tag color={'blue'}>单人拒识</Tag>
                          {rejectItems.map((it) => it.name).join('，')}
                        </div>
                      )}
                      {multiRejectItems.length > 0 && (
                        <div style={{ marginTop: 10 }}>
                          <Tag color={'green'}>多人拒识</Tag>
                          {multiRejectItems.map((it) => it.name).join('，')}
                        </div>
                      )}
                    </>
                  )
                  return (
                    <Tooltip title={displayTipText}>
                      <span style={{ marginRight: 4 }}>{allItems[0]?.name}</span>
                      {simple}
                      {'...'}
                    </Tooltip>
                  )
                }
              }
            }

            return (
              <div key={item.abilityId} className={styles.intervItem}>
                <Row gutter={20} className="text-[12px] flex items-center">
                  <Col span={8}>
                    <p className="truncate" title={abilityName}>
                      {abilityName}
                    </p>
                    <p className="truncate" title={abilityCode}>
                      {abilityCode}
                    </p>
                  </Col>
                  <Col span={8} className="text-center">
                    <Space>
                      <p className="truncate" title={name}>
                        {name}
                      </p>
                      <p title={version}>{version}</p>
                    </Space>
                    <p className="truncate" title={number}>
                      {number}
                    </p>
                  </Col>
                  <Col className="text-right" span={8}>
                    {rejectTextRender()}
                  </Col>
                </Row>
              </div>
            )
          })}
        </div>
      ),
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>
            {/* 已配置&nbsp;<span>{(!!renderList.length && renderList.length) || 0}</span> */}

            {renderList.length > 0 ? (
              <>
                已配置&nbsp;<span>{!!renderList.length && renderList.length}</span>
              </>
            ) : (
              <>未配置</>
            )}
          </div>
          <Button type="dashed" onClick={onAddClick} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        ghost
        onChange={onChange}
        expandIcon={() => null}
      />
      <IntervConfigModal
        visible={configModalOpen}
        onClose={onConfigModalCancel}
        onRefresh={fetchResources}
      />
    </>
  )
}

export default IntervConfig
