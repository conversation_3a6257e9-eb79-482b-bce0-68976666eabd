import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle
} from 'react'
import {
  Form,
  Input,
  Radio,
  Button,
  Space,
  Select,
  Row,
  Col,
  Switch,
  Divider,
  Checkbox,
  message,
  Typography,
  Collapse
} from 'antd'
import {
  MinusCircleOutlined,
  PlusCircleOutlined,
  AppstoreAddOutlined,
  ToolOutlined,
  KeyOutlined,
  LockOutlined,
  RightOutlined,
  DownOutlined
} from '@ant-design/icons'
import { v4 as uuidv4, validate } from 'uuid'
import useFormStore from './formData.js'
import styles from './style.module.scss'
import { webSchema } from './data.js'
import ajax from '@/utils/http'

// import FormItemRenderer from './FormItemRender'
import ArrayModal from './arrayModal'

const { Option } = Select
const { Text } = Typography

const StepForm2 = forwardRef((props, ref) => {
  // const { handleStepChange } = props
  // const { adjustPlugin } = props
  const { formData, setFormData } = useFormStore()
  const { pluginInfo } = props
  const [form] = Form.useForm()
  const [modalOpen, setModalOpen] = useState(false)
  const [arrayData, setArrayData] = useState([])
  const [rowPosition, setRowPosition] = useState([])
  // const [arrayChildren, setArrayChildren] = useState([])

  const authType = Form.useWatch('authType', form)
  const location = Form.useWatch('location', form)

  const requestMethods = [
    {
      value: 'get',
      label: 'get'
    },
    {
      value: 'post',
      label: 'post'
    },
    {
      value: 'put',
      label: 'put'
    },
    {
      value: 'delete',
      label: 'delete'
    },
    {
      value: 'patch',
      label: 'patch'
    }
  ]

  useImperativeHandle(ref, () => ({
    validate: async () => {
      try {
        const values = await form.validateFields()
        setFormData(values)
        // save()
        return values // 返回校验通过的表单值
      } catch (error) {
        console.log('Validation failed:', error)
        return null // 校验失败返回 null
      }
    }
  }))

  const next = async () => {
    // let inputFormValues = await inputForm.validateFields()
    // let outputFormValues = await outputForm.validateFields()
    let values = await form.validateFields()
    let newFormData = {
      ...formData
      // ...inputFormValues,
      // ...outputFormValues
      // webSchema: JSON.stringify(schemaValues)
    }
    console.log('整个整合后的values', values)
    // handleStepChange(2)
    setFormData(values)
    save()
  }

  const save = async () => {
    let inputValueCurrent = await form.validateFields()
    let schemaValue = {
      toolRequestInput: formData.toolRequestInput || [],
      toolRequestOutput: formData.toolRequestOutput || []
    }
    let params = {
      ...formData,
      webSchema: JSON.stringify(schemaValue)
    }
    delete params['toolRequestInput']
    delete params['toolRequestOutput']
    ajax({
      url: '/aiui-agent/aiui-tool/tool/saveTool',
      data: params,
      method: 'post'
    }).then((res) => {
      if (res.data.code == 0) {
        message.success('保存成功')
      }
    })
  }

  const prev = () => {
    handleStepChange(0)
  }

  const handleDataSave = (arrayData) => {
    console.log('赋值', arrayData)
    form.setFieldValue(rowPosition, JSON.parse(JSON.stringify(arrayData)))
    setArrayData([])
    setRowPosition([])
    //
    // setTimeout(()=> {
    //   console.log('set后',form.getFieldValue(rowPosition));
    // })
  }

  useEffect(() => {
    if (formData.toolRequestInput || formData.toolRequestOutput) {
      // inputForm.setFieldsValue(formData)
      // outputForm.setFieldsValue(formData)
      form.setFieldsValue(formData)
    }
    form.setFieldsValue(formData)

    // else{
    //   console.log('默认数据',webSchema)
    //   form.setFieldsValue(webSchema)
    // }
  }, [formData])

  useEffect(() => {}, [])

  const FormRow = ({ name, fieldKey, restField, remove, area, nestingLevel = 0 }) => {
    const form = Form.useFormInstance() // 获取 Form 实例(其实和外面是一个form)

    const [collapsed, setCollapsed] = useState(false) // 新增折叠状态

    const type = Form.useWatch([area, ...name, 'type'], form)
    const children = Form.useWatch([area, ...name, 'children'], form) || []
    // const valueFrom = Form.useWatch([area, ...name, 'from'], form)
    const fatherType = Form.useWatch([area, ...name, 'fatherType'], form)
    const defaultValue = Form.useWatch([area, ...name, 'defaultValue'], form)
    const arraySon = Form.useWatch([area, ...name, 'arraySon'], form) || false

    const nameIndex = name[name.length - 1]
    // if(nestingLevel!=0){
    //   console.log('这是子节点',name, nestingLevel,type);
    // }
    // else{
    //   console.log('nestingLevel',name, nestingLevel,type,children);
    // }

    const handleAddChild = (type) => {
      const currentChildren = form.getFieldValue([area, ...name, 'children']) || []
      // let currentChildren = children
      let newChild = {}
      if (type === 'object') {
        newChild = {
          id: uuidv4(),
          type: 'string', // 默认参数类型
          required: true, // 默认是否必填
          // from: 2, // 默认取值来源
          open: true, // 默认开启,
          fatherType: 'object',
          startDisabled: false,
          nameErrMsg: '',
          descriptionErrMsg: '',
          arraySon
        }
      } else if (type === 'array') {
        newChild = {
          id: uuidv4(),
          name: '[Array Item]',
          fatherType: 'array',
          arraySon: true,
          type: 'string', // 默认参数类型
          required: false, // 默认是否必填
          // from: 2, // 默认取值来源
          open: true, // 默认开启,
          startDisabled: false,
          nameErrMsg: '',
          descriptionErrMsg: ''
        }
        // form.setFieldValue([area, ...name, 'subChild'])
      }
      form.setFieldValue([area, ...name, 'children'], [...currentChildren, newChild])
      // console.log('更新后',children,form.getFieldsValue());
    }

    const handleRemoveChild = (index) => {
      const currentChildren = form.getFieldValue([area, ...name, 'children'])
      const updatedChildren = currentChildren.filter((_, i) => i !== index) // 过滤掉指定项
      form.setFieldValue([area, ...name, 'children'], updatedChildren)
    }

    const handleTypeChange = (val) => {
      form.setFieldValue([area, ...name, 'children'], [])
      checkArray()
      if (val === 'object' || val === 'array') {
        handleAddChild(val)
      }
    }

    const findTopArrayPath = (currentPath) => {
      if (currentPath.length < 2) return null
      // 向上回溯两级（children + index结构）
      const parentPath = currentPath.slice(0, -2)
      const parentType = form.getFieldValue([...parentPath, 'type'])

      if (parentType === 'array') {
        return parentPath // 找到目标路径
      }
      return findTopArrayPath(parentPath) // 递归查找
    }

    const clearDefault = (obj) => {
      obj.defaultValue = ''
      if (obj.children && obj.children.length > 0) {
        for (let child of obj.children) {
          clearDefault(child)
        }
      }
      return obj
    }
    const checkArray = () => {
      if (arraySon) {
        // 清空之前最上层 type=array的父节点children数据，保留一个array item子节点以记录数据结构
        let fullPath = [area, ...name]
        let topArrayPath = findTopArrayPath(fullPath)
        if (topArrayPath) {
          let parentValue = form.getFieldValue(topArrayPath)
          let parenChildrenValue = parentValue.children
          let childItem = JSON.parse(JSON.stringify(parenChildrenValue[0]))
          childItem = clearDefault(childItem)
          form.setFieldValue([...topArrayPath, 'children'], [childItem])
        }
      }
    }

    const handleArrayEdit = (e) => {
      // e.stopPropagation()
      let dataType = children[0][type]
      if (dataType == '') return message.warning('请先定义array元素类型')
      let currentRowValue = form.getFieldValue([area, ...name])
      console.log('currentRowValue', currentRowValue)
      setArrayData(currentRowValue)
      setRowPosition(JSON.parse(JSON.stringify([area, ...name])))
      setModalOpen(true)
    }

    const validateNameUnique = (form, area, currentPath, currentName, currentIndex) => {
      // 获取所有同级字段
      const parentPath = currentPath.slice(0, -1)
      const siblings = form.getFieldValue([area, ...parentPath]) || []

      // 检查同级重复
      const hasDuplicate = siblings.some(
        (item, index) => index !== currentIndex && item?.name === currentName
      )

      if (hasDuplicate) return false

      // 如果是对象/数组，递归检查子级
      if (currentPath.length > 2) {
        return validateNameUnique(
          form,
          area,
          parentPath,
          currentName,
          parentPath[parentPath.length - 1]
        )
      }

      return true
    }

    return (
      <React.Fragment>
        <Row gutter={16} key={fieldKey}>
          <Col span={1}>
            {(type === 'object' || type === 'array') && (
              <Button
                type="text"
                size="small"
                icon={collapsed ? <RightOutlined /> : <DownOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ marginLeft: `${nestingLevel * 12}px` }}
              />
            )}
          </Col>

          <Col span={area === 'toolRequestInput' ? 3 : 7}>
            <Form.Item
              // {...restField}
              name={[nameIndex, 'name']}
              rules={[
                { required: true, message: '请输入参数名称' },
                {
                  max: 32,
                  message: '字符数不能超过32'
                },
                // {
                //   pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
                //   message: '只能包含字母、数字或下划线，并且以字母或下划线开头'
                // },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve()

                    // 1. 获取当前字段的完整路径
                    const fullPath = [area, ...name]

                    // 2. 获取父级路径和同级字段
                    let siblings = []
                    let currentIndex = name[name.length - 1]

                    if (name.length === 1) {
                      // 第一层级字段
                      siblings = form.getFieldValue([area]) || []
                    } else {
                      // 嵌套层级字段
                      // 找到包含children数组的父级路径
                      let parentPath = [area]
                      let childrenPath = []

                      // 构建父级路径（去掉最后的索引和可能的'children'）
                      for (let i = 0; i < name.length - 1; i++) {
                        if (name[i] === 'children' && i === name.length - 2) {
                          // 这是children数组中的项，父级路径是上一级
                          break
                        }
                        parentPath.push(name[i])
                      }

                      // 获取父级对象
                      const parent = form.getFieldValue(parentPath)

                      // 确定从父级的哪个属性获取同级字段
                      if (name[name.length - 2] === 'children') {
                        // 当前是children数组中的项
                        siblings = parent?.children || []
                      } else {
                        // 其他情况（理论上不应该发生）
                        siblings = []
                      }
                    }

                    console.log('验证名称唯一性:', {
                      value,
                      fullPath,
                      currentIndex,
                      siblings: siblings.map((s) => s?.name),
                      siblingPaths: siblings.map((_, i) => [...fullPath.slice(0, -1), i])
                    })

                    // 3. 检查同父级下的重复名称（排除自身）
                    const duplicates = siblings.filter(
                      (item, index) => index !== currentIndex && item?.name === value
                    )

                    if (duplicates.length > 0 && fatherType !== 'array') {
                      return Promise.reject('同层级参数名称不能重复')
                    }

                    if (fatherType == 'array') {
                      return Promise.resolve()
                    }
                    if (/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
                      return Promise.resolve()
                    }
                    return Promise.reject(
                      new Error('只能包含字母、数字或下划线，并且以字母或下划线开头')
                    )
                  }
                }
              ]}
              dependencies={[[area, ...name.slice(0, -1)].join('.')]}
              style={{ paddingLeft: `${nestingLevel * 12}px` }}
            >
              <Input
                placeholder="测试输入参数名称"
                disabled={fatherType === 'array'}
                onChange={(e) => checkArray()}
              />
            </Form.Item>
          </Col>

          <Col span={area === 'toolRequestInput' ? 4 : 8}>
            <Form.Item
              // {...restField}
              name={[nameIndex, 'description']}
              rules={[{ required: true, message: '请输入参数描述' }]}
            >
              <Input placeholder="请输入参数描述" onChange={(e) => checkArray()} />
            </Form.Item>
          </Col>

          <Col span={area === 'toolRequestInput' ? 3 : 4}>
            <Form.Item
              // {...restField}
              name={[nameIndex, 'type']}
              rules={[{ required: true, message: '请选择参数类型' }]}
            >
              <Select placeholder="选择参数类型" onChange={(val) => handleTypeChange(val)}>
                <Option value="string">string</Option>
                <Option value="number">number</Option>
                <Option value="integer">integer</Option>
                <Option value="boolean">boolean</Option>
                {fatherType != 'array' && <Option value="array">array</Option>}
                <Option value="object">object</Option>
              </Select>
            </Form.Item>
          </Col>
          {area === 'toolRequestInput' && (
            <React.Fragment>
              <Col span={3}>
                {nestingLevel === 0 && (
                  <Form.Item
                    // {...restField}
                    name={[nameIndex, 'location']}
                    rules={[{ required: true, message: '请选择传入方法' }]}
                  >
                    <Select placeholder="选择参数类型">
                      <Option value="query">query</Option>
                      <Option value="body">body</Option>
                      <Option value="path">path</Option>
                      <Option value="header">header</Option>
                    </Select>
                  </Form.Item>
                )}
              </Col>

              <Col span={3}>
                {fatherType != 'array' && (
                  <Form.Item name={[nameIndex, 'required']} valuePropName="checked">
                    <Checkbox onChange={(val) => checkArray()} />
                  </Form.Item>
                )}
              </Col>

              {/* <Col span={3}>
                <Form.Item name={[nameIndex, 'from']}>
                  <Select placeholder="选择来源" onChange={(val) => checkArray()}>
                    <Option value={2}>默认值</Option>
                    <Option value={0}>模型识别</Option>
                    <Option value={1}>业务透传</Option>
                  </Select>
                </Form.Item>
              </Col> */}

              <Col span={3}>
                {type != 'object' && type != 'array' && (
                  <Form.Item
                    // {...restField}
                    name={[nameIndex, 'defaultValue']}
                    rules={[
                      ({ getFieldValue }) => ({
                        required: getFieldValue([nameIndex, 'required']),
                        message: '请输入默认值'
                      })
                    ]}
                    style={{ visibility: arraySon ? 'hidden' : 'visible' }}
                  >
                    <Input placeholder="测试输入默认值" />
                  </Form.Item>
                )}

                {type == 'array' && !arraySon && (
                  <Form.Item name={[nameIndex, 'defaultValue']}>
                    <Button color="default" type="dashed" onClick={(e) => handleArrayEdit(e)}>
                      编辑数组参数
                    </Button>
                  </Form.Item>
                )}
              </Col>
            </React.Fragment>
          )}

          <Col span={2}>
            {/* {type != 'object' && (
              <Form.Item name={[nameIndex, 'open']}>
                <Switch />
              </Form.Item>
            )} */}
            <Form.Item name={[nameIndex, 'open']}>
              <Switch />
            </Form.Item>
          </Col>
          <Col span={2}>
            <Form.Item
              // {...restField}
              // name={[nameIndex, 'operation']}
              dependencies={[area, nameIndex, 'type']}
            >
              {() => (
                <div>
                  {type === 'object' && (
                    <AppstoreAddOutlined
                      style={{ cursor: 'pointer', marginRight: '8px' }}
                      onClick={() => {
                        handleAddChild('object')
                      }}
                    />
                  )}
                  {fatherType != 'array' && <MinusCircleOutlined onClick={() => remove(name)} />}
                </div>
              )}
            </Form.Item>
          </Col>
        </Row>
        {(type === 'object' || type === 'array') && children.length > 0 && !collapsed && (
          <Form.List name={[nameIndex, 'children']}>
            {(childFields, { add, remove }) => (
              <>
                {childFields.map((childField, index) => {
                  // console.log('childField', childField)
                  if (type === 'array' && index != 0) {
                    return
                  } else {
                    return (
                      <FormRow
                        key={`${name}-child-${index}`}
                        name={[...name, 'children', childField.name]}
                        fieldKey={`${fieldKey}-child-${index}`}
                        restField={childField}
                        remove={() => handleRemoveChild(index)}
                        nestingLevel={nestingLevel + 1}
                        area={area}
                      />
                    )
                  }
                })}
              </>
            )}
          </Form.List>
        )}
      </React.Fragment>
    )
  }

  const CollapseItems = [
    { key: '1', label: '工具信息', children: '' },
    { key: '2', label: '输入参数', children: '' },
    { key: '3', label: '输出参数', children: '' }
  ]

  return (
    <div>
      <Form
        style={{ marginTop: '10px' }}
        form={form}
        layout="vertical"
        // labelCol={{
        //   span: 4
        // }}
        // wrapperCol={{
        //   span: 20
        // }}
        labelWrap
        autoComplete="off"
        initialValues={{
          location: 'header'
        }}
      >
        <div>
          <Form.Item
            label="工具路径"
            name="endPoint"
            rules={[
              { required: true, message: '请输入工具路径' },
              {
                pattern:
                  /^(https?:\/\/)?(localhost|(\d{1,3}\.){3}\d{1,3}|([\w-]+\.)+[\w-]+)(:\d+)?(\/[\w\-./?%&=]*)?$/,
                message: '请输入有效的URL地址'
              }
            ]}
          >
            <Input placeholder="请输入" allowClear />
          </Form.Item>

          <Form.Item label="授权方式" name="authType" rules={[{ required: true }]}>
            <Radio.Group style={{ display: 'flex', gap: '20px' }}>
              <Radio.Button
                value={-1}
                style={{
                  flex: 1,
                  height: 120,
                  padding: '16px',
                  textAlign: 'left',
                  borderRadius: '8px',
                  border:
                    authType === -1
                      ? '1px solid var(--sparkos-primary-color)'
                      : '1px solid #d9d9d9',
                  boxShadow: authType === -1 ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                  overflow: 'hidden',
                  background: authType === -1 ? '#f0faff' : '#fff'
                }}
              >
                <LockOutlined
                  style={{ fontSize: 18, marginRight: 8, color: 'var(--sparkos-primary-color)' }}
                />
                <strong>不需要授权</strong>
                <div>
                  <Text type="secondary">无需额外授权就可以使用 API</Text>
                </div>
              </Radio.Button>

              <Radio.Button
                value={2}
                style={{
                  flex: 1,
                  height: 120,
                  padding: '16px',
                  textAlign: 'left',
                  borderRadius: '8px',
                  border:
                    authType === 2 ? '1px solid var(--sparkos-primary-color)' : '1px solid #d9d9d9',
                  boxShadow: authType === 2 ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                  overflow: 'hidden',
                  background: authType === 2 ? '#f0faff' : '#fff'
                }}
              >
                <KeyOutlined
                  style={{ fontSize: 18, marginRight: 8, color: 'var(--sparkos-primary-color)' }}
                />
                <strong>Service</strong>
                <div>
                  <Text type="secondary">
                    需要在请求头 (header) 或查询参数 (query) 时携带密钥来获取授权
                  </Text>
                </div>
              </Radio.Button>
            </Radio.Group>
          </Form.Item>

          {authType === 2 && (
            <Form.Item
              label="位置"
              name="location"
              rules={[{ required: true, message: '请选择密钥传递方式' }]}
              extra={
                <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                  Header代表在请求头中传递密钥，Query代表在查询中传递密钥
                </div>
              }
            >
              <Radio.Group optionType="radio" style={{ display: 'flex', gap: '20px' }}>
                <Radio.Button
                  value="header"
                  style={{
                    flex: 1,
                    height: 36,
                    padding: '3px 16px',
                    textAlign: 'left',
                    display: 'flex', // 添加这个
                    alignItems: 'center',
                    borderRadius: '8px',
                    border:
                      location === 'header'
                        ? '1px solid var(--sparkos-primary-color)'
                        : '1px solid #d9d9d9',
                    boxShadow: location === 'header' ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                    overflow: 'hidden',
                    background: location === 'header' ? '#f0faff' : '#fff'
                  }}
                >
                  Header
                </Radio.Button>
                <Radio.Button
                  value="query"
                  style={{
                    flex: 1,
                    height: 36,
                    padding: '3px 16px',
                    display: 'flex', // 添加这个
                    alignItems: 'center',
                    textAlign: 'left',
                    borderRadius: '8px',
                    border:
                      location === 'query'
                        ? '1px solid var(--sparkos-primary-color)'
                        : '1px solid #d9d9d9',
                    boxShadow: location === 'query' ? '0 0 6px rgba(24, 144, 255, 0.5)' : 'none',
                    overflow: 'hidden',
                    background: location === 'query' ? '#f0faff' : '#fff'
                  }}
                >
                  Query
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          )}

          {authType === 2 && (
            <Form.Item
              name="parameterName"
              label="Parameter name"
              rules={[{ required: true, message: '请输入' }]}
              extra={
                <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                  密钥的参数，您需要传递Service
                  Token的参数名。其作用是告诉API服务，您将在哪个参数中提供授权信息
                </div>
              }
            >
              <Input></Input>
            </Form.Item>
          )}

          {authType === 2 && (
            <Form.Item
              name="serviceToken"
              label="Service token / APl key"
              rules={[{ required: true, message: '请输入' }]}
              extra={
                <div style={{ textAlign: 'left', fontSize: '12px', color: '#757575' }}>
                  密钥的参数值，代表您的身份或给定的服务权限。API服务会验证此Token，以确保您有权进行相应的操作
                </div>
              }
            >
              <Input></Input>
            </Form.Item>
          )}

          <Form.Item
            label="请求方法"
            name="method"
            rules={[{ required: true, message: '请选择请求方法' }]}
          >
            <Select options={requestMethods} placeholder="请选择请求方法"></Select>
          </Form.Item>
        </div>
      </Form>

      <div>
        <div className={styles.label}>
          <span>配置输入参数</span>
        </div>

        <Row gutter={16}>
          <Col span={1}></Col>
          <Col span={3}>参数名称</Col>
          <Col span={4}>参数描述</Col>
          <Col span={3}>参数类型</Col>
          <Col span={3}>传入方法</Col>
          <Col span={3}>是否必填</Col>
          {/* <Col span={3}>取值来源</Col> */}
          <Col span={3}>默认值</Col>
          <Col span={2}>开启</Col>
          <Col span={2}>操作</Col>
        </Row>

        <Form layout="vertical" style={{ marginTop: 20 }} form={form} className={styles.step2Form}>
          <Form.List name="toolRequestInput" initialValue={[]}>
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <FormRow
                    key={key}
                    fieldKey={key}
                    name={[name]}
                    restField={restField}
                    remove={remove}
                    area={'toolRequestInput'}
                  />
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    block
                    icon={<PlusCircleOutlined />}
                    onClick={() =>
                      add({
                        id: uuidv4(),
                        type: 'string', // 默认参数类型
                        location: 'query', // 默认传入方法
                        required: true, // 默认是否必填
                        // from: 2, // 默认取值来源
                        open: true, // 默认开启
                        startDisabled: false,
                        nameErrMsg: '',
                        descriptionErrMsg: ''
                      })
                    }
                  >
                    添加
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>

        <Divider></Divider>

        <div className={styles.label}>
          <span style={{ marginBottom: '20px' }}>配置输出参数</span>
        </div>

        <Row gutter={16}>
          <Col span={1}></Col>
          <Col span={7}>参数名称</Col>
          <Col span={8}>参数描述</Col>
          <Col span={4}>参数类型</Col>
          <Col span={2}>开启</Col>
          <Col span={2}>操作</Col>
        </Row>

        <Form layout="vertical" style={{ marginTop: 20 }} form={form}>
          <Form.List name="toolRequestOutput">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <FormRow
                    key={key}
                    fieldKey={key}
                    name={[name]}
                    restField={restField}
                    remove={remove}
                    area={'toolRequestOutput'}
                  />
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    block
                    icon={<PlusCircleOutlined />}
                    onClick={() =>
                      add({
                        id: uuidv4(),
                        type: 'string', // 默认参数类型
                        location: 'query', // 默认传入方法
                        required: true, // 默认是否必填
                        // from: 2, // 默认取值来源
                        open: true, // 默认开启
                        startDisabled: false,
                        nameErrMsg: '',
                        descriptionErrMsg: ''
                      })
                    }
                  >
                    添加
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form>
      </div>

      <ArrayModal
        open={modalOpen}
        arrayData={arrayData}
        handleDataSave={handleDataSave}
        setIsModalOpen={setModalOpen}
      />
    </div>
  )
})

export default StepForm2
