import { useState, useEffect, useRef } from 'react'
import {
  Form,
  Input,
  Button,
  message,
  Space,
  Modal,
  Table,
  App,
  Empty,
  Dropdown,
  Pagination,
  Popconfirm,
  Tag
} from 'antd'
import { useNavigate } from 'react-router-dom'
import { SearchOutlined, ExclamationCircleOutlined, TagsOutlined } from '@ant-design/icons'
import CardList from '@/components/CardList'
import MoreIcon from '@/assets/svgs/more2.svg?react'
import DateIcon from '@/assets/svgs/date.svg?react'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import { APP_ENV, cssVariables } from '@/utils/constant'
import { modalEvents } from '@/utils/eventSystem.js'
import IconIntent from 'assets/svgs/icon-intent.svg?react'
import IconEdit from '@/assets/svgs/icon-edit.svg?react'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'
import { MyModal } from '@/components'

const Intent = () => {
  const [modal, contextHolder] = Modal.useModal()
  const [searchVal, setSearchVal] = useState('')
  const [intentList, setIntentList] = useState([])
  const [current, setCurrent] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [loading, setLoading] = useState(false)
  const [visible, setVisible] = useState(false)
  const [form] = Form.useForm()
  const [intentId, setIntentId] = useState(null)
  const [title, setTitle] = useState('创建意图')

  const cardListRef = useRef(null) // 用于获取 CardList 的 ref
  const currentIntent = useRef(null)

  const navigate = useNavigate()

  const createIntent = () => {
    setVisible(true)
  }

  const editIntent = (record) => {
    setIntentId(record?.intentId)
    setTitle('编辑意图')
    form.setFieldsValue({
      intentName: record.intentName,
      intentNameEn: record.intentNameEn,
      intentDesc: record.intentDesc
    })
    setVisible(true)
  }

  const doDeleteIntent = (intentId) => {
    const url = `/aiui-agent/intent/delete`
    const data = {
      intentId: intentId
    }
    ajax({
      url,
      data,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('操作成功')
          cardListRef?.current.reload()
        }
      })
      .catch((err) => {})
  }

  const deleteIntent = (intentId) => {
    setIntentId(intentId)
    modal.confirm({
      title: '确定要删除该意图吗？',
      icon: <ExclamationCircleOutlined />,
      content: '',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        const url = `/aiui-agent/intent/delete`
        const data = {
          intentId: intentId
        }
        ajax({
          url,
          data,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code === '0') {
              message.success('操作成功')
            }
          })
          .catch((err) => {})
      },
      onCancel() {}
    })
  }

  const goCorpus = (record) => {
    if (APP_ENV === 'base') {
      navigate(`/workspace/intent/${record.intentId}/corpus`)
    } else if (APP_ENV === 'auto') {
      navigate(`/intent/${record.intentId}/corpus`)
    }
  }
  const [isHovered, setIsHovered] = useState(false)

  const dropdownItems = [
    {
      key: '1',
      label: (
        <Space>
          <IconEdit />
          <div>编辑</div>
        </Space>
      )
    },
    {
      key: '2',
      label: (
        <Space
          className={'delete-menu-item'}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Popconfirm
            title={`删除意图`}
            description="确定删除该意图"
            onConfirm={(e) => doDeleteIntent(currentIntent.current?.intentId)}
            okText="是"
            okButtonProps={{ loading: false }}
            cancelText="否"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <Space>
              {isHovered ? <IconDeleteRed /> : <IconDelete />}
              <div
                style={{
                  color: isHovered ? '#f15858' : '#374151'
                }}
              >
                删除
              </div>
            </Space>
          </Popconfirm>
        </Space>
      ),
      style: { backgroundColor: isHovered ? '#fee2e2' : '#fff' }
    }
  ]

  const onDropItemsClick = (e, item) => {
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case '1':
        editIntent(item, e)
        break
      case '2':
        // deleteIntent(item.intentId, e)
        currentIntent.current = item
        break
    }
  }

  const closeForm = () => {
    form.resetFields()
    setTitle('创建意图')
    setVisible(false)
    setIntentId(null)
  }
  const submitForm = () => {
    form
      .validateFields()
      .then((values) => {
        setLoading(false)
        const params = intentId
          ? {
              intentName: values.intentName,
              intentNameEn: values.intentNameEn,
              intentDesc: values.intentDesc,
              intentId: intentId
            }
          : {
              intentName: values.intentName,
              intentNameEn: values.intentNameEn,
              intentDesc: values.intentDesc
            }
        const url = intentId ? '/aiui-agent/intent/edit' : `/aiui-agent/intent/create`
        ajax({
          url,
          data: params,
          method: 'post'
        })
          .then((res) => {
            if (res.data.code === '0') {
              setLoading(false)
              closeForm()
              message.success('操作成功')
              cardListRef?.current.reload()
            }
          })
          .catch((err) => {
            setLoading(false)
          })
      })
      .catch()
  }

  const renderFooterTag = (cardData) => {
    if (APP_ENV === 'base') {
      return null
    } else {
      return (
        <div>
          {cardData?.orgName ? (
            <Tag color="orange" icon={<TagsOutlined />}>
              {cardData?.orgName}
            </Tag>
          ) : null}
        </div>
      )
    }
  }

  useEffect(() => {
    const unsubscribe = modalEvents.listen('OPEN_INTENT', () => {
      setVisible(true)
    })
    return () => {
      unsubscribe() // 使用返回的清理函数
      // 或者直接使用：modalEvents.unlisten('OPEN_B_MODAL', handler);
    }
  }, [])

  return (
    <App>
      {contextHolder}

      <CardList
        title="意图"
        ref={cardListRef}
        searchConfig={{
          url: '/aiui-agent/intent/list',
          method: 'post',
          searchKey: 'searchKey',
          pagination: {
            page: 'pageIndex',
            pageSize: 'pageSize'
          },
          dataFormatter: (data) => {
            return {
              list: data?.data?.data || [],
              total: data?.data?.totalSize
            }
          }
        }}
        cardConfig={{
          title: 'intentName',
          description: 'intentDesc',
          updateTime: 'createTime',
          id: 'intentId'
        }}
        dropdown={{
          optItems: dropdownItems,
          onDropItemsClick
        }}
        renderFooterTag={renderFooterTag}
        events={{
          onAddClick: createIntent,
          onCardClick: goCorpus
        }}
      />

      <MyModal
        title={
          APP_ENV === 'base' ? (
            <Space size={4}>
              <IconIntent /> {title}
            </Space>
          ) : (
            title
          )
        }
        open={visible}
        width={600}
        confirmLoading={loading}
        destroyOnHidden={true}
        onCancel={closeForm}
        onClose={closeForm}
        onOk={submitForm}
        okText={title.includes('编辑') ? '保存' : '创建'}
      >
        <Form form={form} layout="vertical" style={{ marginTop: '20px' }}>
          <Form.Item
            name="intentName"
            label="意图名称"
            rules={[
              { required: true, message: '请填写意图名称', trigger: 'blur' },
              {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
                message:
                  '输入内容只能包含中文、英文字母、数字、小数点、短横线和下划线，且长度不超过32个字符',
                trigger: 'blur'
              }
            ]}
          >
            <Input
              placeholder="支持中英文/数字/小数点/短横线/下划线，不超过32个字符"
              showCount
              maxLength={32}
            ></Input>
          </Form.Item>

          <Form.Item
            name="intentNameEn"
            label="英文标识"
            rules={[
              { required: true, message: '请填写意图标识', trigger: 'blur' },
              {
                pattern: /^[a-zA-Z0-9._-]{0,32}$/,
                message:
                  '输入内容只能包含英文字母、数字、小数点、短横线和下划线,且长度不超过32个字符',
                trigger: 'blur'
              }
            ]}
          >
            <Input
              placeholder="支持中英文/数字/小数点/短横线/下划线，不超过32个字符"
              maxLength={32}
              showCount
            ></Input>
          </Form.Item>

          <Form.Item
            name="intentDesc"
            label="意图描述"
            rules={[
              { required: true, message: '请填写描述', trigger: 'blur' },
              { max: 250, message: '描述不能超过250个字符', trigger: 'blur' }
            ]}
          >
            <Input.TextArea
              placeholder="请输入描述，不超过250个字符"
              showCount
              maxLength={250}
            ></Input.TextArea>
          </Form.Item>
        </Form>
      </MyModal>
    </App>
  )
}

export default Intent
