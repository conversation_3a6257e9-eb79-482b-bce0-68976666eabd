import { Link, Outlet, useNavigate, useLocation } from 'react-router-dom'
import React, { useState, useEffect } from 'react'
import IconApp from 'assets/svgs/app.svg?react'
import IconSkill from 'assets/svgs/skill.svg?react'
import IconArrow from 'assets/svgs/arrow.svg?react'
import IconLogo from 'assets/logo_title.svg?react'

import { Avatar, Button, Dropdown, Layout, Menu, message, Space, theme } from 'antd'
import { UserOutlined, PlusOutlined } from '@ant-design/icons'
import { useUserStore } from '@/store/user'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import { SSOlogoutUrl } from '@/utils/getSSOUrl'

import { modalEvents } from '@/utils/eventSystem'

import IconHome from 'assets/svgs/menu/home.svg?react'
import IconSpace from 'assets/svgs/menu/personal.svg?react'
import IconAgent from 'assets/svgs/menu/agent.svg?react'
import IconBot from 'assets/svgs/menu/bot.svg?react'
import IconTool from 'assets/svgs/menu/tool.svg?react'
import IconCustom from 'assets/svgs/menu/custom.svg?react'
import IconConfig from 'assets/svgs/menu/config.svg?react'

const { Header, Sider, Content } = Layout

const items = [
  {
    key: 'logout',
    label: <span style={{ fontSize: '12px' }}>退出登录</span>
  }
]

const menuItems = [
  // {
  //   key: 'home',
  //   label: '主页',
  //   icon: <IconHome />
  // },
  {
    key: 'space',
    label: '空间',
    type: 'group',
    children: [
      {
        key: 'workspace',
        label: '个人空间',
        icon: <IconSpace />
      }
    ]
  },
  {
    key: 'square',
    label: '广场',
    type: 'group',
    children: [
      {
        key: 'botSquare',
        label: 'Bot广场',
        icon: <IconBot />
      },
      {
        key: 'agentSquare',
        label: '智能体广场',
        icon: <IconAgent />
      },
      {
        key: 'toolSquare',
        label: '工具广场',
        icon: <IconTool />
      }
    ]
  },
  {
    key: 'manage',
    label: '管理',
    type: 'group',
    children: [
      {
        key: 'customManage',
        label: '定制管理',
        icon: <IconCustom />
      }
    ]
  }
]

const Root = () => {
  const [showSider, setShowSider] = useState(true)
  const account = useUserStore((state) => state.account)
  const setAccount = useUserStore((state) => state.setAccount)
  const setLimitCount = useUserStore((state) => state.setLimitCount)
  const selectedMenus = useUserStore((state) => state.selectedMenus)
  const navigate = useNavigate()
  const location = useLocation()

  useEffect(() => {
    if (window.__MICRO_APP_ENVIRONMENT__) {
      setShowSider(false)
    } else {
      ajax({
        url: '/auth/getUserInfo',
        baseURL: '/api/v1',
        data: {},
        method: 'get'
      }).then((res) => {
        setAccount(res.data.data)
      })
    }

    ajax({
      url: '/user/getLimitCount',
      data: {},
      method: 'get'
    }).then((res) => {
      setLimitCount(res.data?.data)
    })
  }, [])

  useEffect(() => {
    if (location.pathname === '/' || location.pathname.includes('home')) {
      useUserStore.setState({ selectedMenus: ['home'] })
    } else if (location.pathname.includes('workspace')) {
      useUserStore.setState({ selectedMenus: ['workspace'] })
    }
    // console.log('当前菜单', selectedMenus)
  }, [location])

  const onLogoutClick = ({ key }) => {
    ajax({
      url: '/auth/userLogout',
      baseURL: '/api/v1',
      data: {},
      method: 'get'
    }).then((res) => {
      console.log('logout res', res)
      message.success('退出成功')
      const isDomain = account.authType === 1
      if (isDomain) {
        window.location.href = SSOlogoutUrl + `${window.origin}/portal`
      } else {
        window.location.href = `${window.location.origin}/login`
      }
    })
  }

  const gotoCreate = () => {
    const path = window.location.pathname
    let eventName = ''

    if (path.includes('/workspace/agent')) eventName = 'OPEN_AGENT'
    else if (path.includes('/workspace/plugin')) eventName = 'OPEN_PLUGIN'
    else if (path.includes('/workspace/flow')) eventName = 'OPEN_FLOW'
    else if (path.includes('/workspace/repo')) eventName = 'OPEN_REPO'
    else if (path.includes('/workspace/intent')) eventName = 'OPEN_INTENT'

    // 工作流单独使用
    if (eventName === 'OPEN_FLOW') {
      const event = new CustomEvent(eventName, { detail: {} })
      window.dispatchEvent(event)
    } else {
      modalEvents.emit(eventName)
    }
  }

  const {
    token: { colorBgContainer, borderRadiusLG }
  } = theme.useToken()
  return (
    <div className={styles.container}>
      {showSider && (
        <Layout className={styles.layout}>
          <Sider width={220} className={styles.sider}>
            {/* <h3 className={styles.platTitle}>SparkOS人机交互平台</h3> */}
            <div className={styles.platTitle}>
              <IconLogo />
            </div>

            <Button
              icon={<PlusOutlined />}
              style={{ width: '100%', height: '38px' }}
              onClick={gotoCreate}
            >
              创建
            </Button>
            <div className={styles.line}></div>
            <Menu
              mode="inline"
              selectedKeys={selectedMenus}
              className={styles.leftMenu}
              items={menuItems}
              onClick={({ key }) => navigate(`/${key}`)}
            />
            <div className={styles.footer}>
              <div className={styles.leftSection}>
                <div className={styles.avatar}>
                  {(account?.accountName || account?.nickname)?.charAt(0) || 'U'}
                </div>
                <span className={styles.username}>{account?.accountName || account?.nickname}</span>
              </div>
              <Dropdown
                menu={{
                  items,
                  onClick: onLogoutClick
                }}
                placement="topLeft"
              >
                <div className={styles.circleButton}>
                  <IconConfig />
                </div>
              </Dropdown>
            </div>
          </Sider>
          <Content
            style={{
              backgroundColor: '#f5f5f5'
            }}
          >
            <Outlet />
          </Content>
        </Layout>
      )}
      {!showSider && <Outlet />}
    </div>
  )
}
export default Root
