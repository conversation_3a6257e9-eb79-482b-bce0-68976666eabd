import { useEffect, useState } from 'react'
import { Table, App, Modal, Button, Spin, message } from 'antd'
import { useParams } from 'react-router-dom'
import ajax from '@/utils/http'
import { MyModal } from '@/components'

const ExpandCorpusModal = ({
  expandModalVisible,
  handleExpandModal,
  expandList,
  expandLoading,
  updateExpandList,
  updateExpandAllList
}) => {
  const { intentId } = useParams()

  const expandColumns = [
    {
      dataIndex: 'number',
      key: 'number',
      width: 50
    },
    {
      dataIndex: 'content',
      key: 'content'
    },
    {
      fixed: 'right',
      width: 100,
      key: '',
      render: (_, record) => (
        <Button
          color="default"
          type="link"
          disabled={record.added}
          onClick={() => addContent(record)}
        >
          {record.added ? '已添加' : '添加'}
        </Button>
      )
    }
  ]

  const addContent = (record) => {
    const url = `/aiui-agent/intent/corpus/add`
    const params = {
      corpusList: [record.content],
      intentId: intentId
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('添加成功')
          updateExpandList(record.key)
        }
      })
      .catch((err) => {})
  }

  const addAll = () => {
    const url = `/aiui-agent/intent/corpus/add`
    const params = {
      intentId: intentId,
      corpusList: expandList.map((item) => item.content)
    }
    ajax({
      url,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('添加成功')
          updateExpandAllList()
        }
      })
      .catch((err) => {})
  }

  return (
    <App>
      <MyModal
        title="语料泛化"
        open={expandModalVisible}
        footer={null}
        width={700}
        onCancel={handleExpandModal}
      >
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '15px' }}>
          <span style={{ color: '#bdbdbd' }}>调用AI帮你扩写说法，泛化语料。全部满意，点击</span>
          <Button type="text" onClick={addAll} style={{ color: '#69b1ff' }}>
            一键添加
          </Button>
        </div>
        <Table
          showHeader={false}
          dataSource={expandList}
          columns={expandColumns}
          loading={{
            spinning: expandLoading,
            indicator: <Spin tip="加载中..." size="large" />
          }}
          pagination={false}
          rowKey="number"
        ></Table>
      </MyModal>
    </App>
  )
}

export default ExpandCorpusModal
