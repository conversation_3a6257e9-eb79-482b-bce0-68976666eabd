.cardList {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.cardItem {
  width: 370px;
  height: 179px;
  background: linear-gradient(180deg, #ffffff, #fafafa 100%);
  border: 1px solid #d1d5db;
  border-radius: 12px;
  padding: 16px 14px;
  display: flex;
  flex-direction: column;

  cursor: pointer;
  &:hover {
    box-shadow:
      0px 2px 4px -1px rgba(0, 0, 0, 0.06),
      0px 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .cardName {
    padding-right: 20px;
    height: 21px;
    color: #111827;
    font-size: 15px;
    font-family:
      PingFang SC,
      PingFang SC-500;
    font-weight: 500;
    text-align: LEFT;
    color: #111827;
    line-height: 18px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cardMid {
    display: flex;
    .cardId {
      font-size: 12px;
      font-family:
        <PERSON>Fang SC,
        PingFang SC-400;
      font-weight: 400;
      text-align: LEFT;
      color: #6b7280;
      line-height: 14px;
    }
    .moreIcon {
      margin-left: auto;
    }
  }

  .desc {
    margin-top: 15px;
    margin-bottom: 5px;
    font-size: 13px;
    font-family:
      PingFang SC,
      PingFang SC-400;
    font-weight: 400;
    text-align: LEFT;
    color: #6b7280;
    line-height: 15px;
    word-wrap: break-word;
  }

  .footer {
    display: flex;
    justify-content: space-between;
    margin-top: auto;

    .updateTime {
      margin-left: auto;
    }
  }
}
