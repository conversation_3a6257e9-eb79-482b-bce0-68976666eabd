import React, { useState, useEffect } from 'react'
import { Form, Input, Button, Checkbox, Radio, Select, Tooltip, InputNumber, message } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import SplitConfig from '../SplitConfig'
import { getRepoInfo, updateRepo } from '../service'
import { useParams, useNavigate } from 'react-router-dom'
import { APP_ENV } from '@/utils/constant.js'

const { TextArea } = Input

const BaseInfo = () => {
  const { repoId } = useParams()
  const navigate = useNavigate()
  const [form] = Form.useForm()
  const [parseType, setParseType] = useState(1)
  const [addLoading, setAddLoading] = useState(false)

  useEffect(() => {
    // console.log('repoId', repoId)
    getDetail()
  }, [])

  const getDetail = () => {
    getRepoInfo({ repoId }).then((data) => {
      let res = data.data
      fillRepoForm(res.data)
    })
  }

  const fillRepoForm = (repoData) => {
    let repoConfig = JSON.parse(repoData.repoConfig)
    if (repoConfig.parseSplitModel) {
      let parseModelInfo = JSON.parse(repoConfig.parseSplitModel.modelInfo)
      // console.log('分段类型',parseModelInfo);
      setParseType(parseModelInfo.parseType || 1)
      let parseConfig = parseModelInfo.parseConfig
      if (parseConfig) {
        // self.ruleForm.strategy = parseConfig.chunkType.split(',')
        // self.ruleForm.identifier = JSON.parse(parseConfig.cutOff || parseConfig.separator)
        // self.ruleForm.maxLength = JSON.parse(parseConfig.lengthRange)[1]
        // self.ruleForm.symbolType = parseConfig.separator? 'separator' : 'cutOff'
        form.setFieldsValue({
          strategy: parseConfig.chunkType.split(','),
          identifier: JSON.parse(parseConfig.cutOff || parseConfig.separator || '[]'),
          maxLength: JSON.parse(parseConfig.lengthRange)[1],
          symbolType: parseConfig.separator ? 'separator' : 'cutOff'
        })
      }
    }
    form.setFieldsValue({
      name: repoData.name,
      description: repoData.description || ''
    })
  }

  const cancel = () => {
    if (APP_ENV === 'auto') {
      navigate('/repo')
    } else {
      navigate('/workspace/repo')
    }
  }

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      console.log('提交表单', values)
      setAddLoading(false)
      let data = {
        repoId,
        repoName: values.name,
        description: values.description,
        fromSource: 'aiCloud'
      }
      // 拼接配置参数
      let parseModelInfo = {
        layout: true,
        parseType: parseType
      }
      if (parseType === 2) {
        parseModelInfo.parseConfig = {
          chunkType: values.strategy.join(','),
          lengthRange: JSON.stringify([16, values.maxLength]),
          [values.symbolType]: JSON.stringify(values.identifier)
        }
      }
      let repoConfig = {
        chunkSize: 1024,
        strategyModel: { id: -1 },
        vectorModel: {
          id: 10,
          modelInfo: JSON.stringify({
            name: 'SPARK',
            domain: 'spark',
            defaultDim: 23
          })
        },
        dimModel: {
          id: 23,
          modelInfo: JSON.stringify({
            name: '1024',
            chunkSize: [16, 1024],
            defaultChunk: 1024,
            version: '2.0.1024'
          })
        },
        parseSplitModel: {
          id: 46,
          modelInfo: JSON.stringify(parseModelInfo)
        }
      }
      data.repoConfig = JSON.stringify(repoConfig)
      updateRepo(data).then((data) => {
        let res = data.data
        if (res.code == 0) {
          setAddLoading(false)
          message.success('保存成功')
          getDetail()
        }
      })
    })
  }

  return (
    <Form form={form} layout="horizontal" labelCol={{ span: 4 }} wrapperCol={{ span: 12 }}>
      <Form.Item
        label="知识库名称："
        name="name"
        rules={[
          { required: true, message: '请输入知识库名称' },
          { max: 40, message: '名称不得超过40个字符', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const regExp = /^[a-zA-Z0-9_\u4e00-\u9fff]+$/ // 正则表达式匹配中英文 数字 下划线
              if (!regExp.test(value)) {
                callback(new Error('名称只能包含中英文/数字/下划线'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ]}
      >
        <Input placeholder="输入自定义库名称，中文、英文不超过40个字符" />
      </Form.Item>
      <Form.Item
        label="描述："
        name="description"
        rules={[{ max: 50, message: '文档描述不得超过50个字符', trigger: 'change' }]}
      >
        <TextArea placeholder="输入文档描述，非必填，50字内" autoSize={{ minRows: 3 }} />
      </Form.Item>
      <Form.Item label="拆分方法：">
        <SplitConfig form={form} parseType={parseType} setParseType={setParseType} />
      </Form.Item>
      <Form.Item label=" " colon={false}>
        <Button onClick={cancel} style={{ marginRight: '12px' }}>
          取消
        </Button>
        <Button type="primary" onClick={() => handleSubmit()}>
          确定
        </Button>
      </Form.Item>
    </Form>
  )
}

export default BaseInfo
