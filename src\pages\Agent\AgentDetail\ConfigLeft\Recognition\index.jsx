import { useState, useEffect } from 'react'
import styles from '../style.module.scss'
import { QuestionCircleOutlined, RightOutlined } from '@ant-design/icons'
import { Tooltip, Space, Select } from 'antd'
import ajax from '@/utils/http'
import { useParams } from 'react-router-dom'
import { useAgentDetailStore } from '../../store'

const Recognition = () => {
  const { agentId } = useParams()
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [relations, setRelations] = useState([])
  const [langList, setLangList] = useState([])
  const [relationSelected, setRelationSelected] = useState()
  const [langSelected, setLangSelected] = useState()

  const getAccentDetail = () => {
    ajax({
      url: '/bot/iat/getConfig',
      data: {
        botId: agentDetail?.boxBot?.botId
      },
      method: 'get'
    }).then((res) => {
      console.log('detail res.data', res.data) //初始化时 res.data 没有data
      if (res.data.data) {
        let data = res.data.data
        if (
          data.language == 'zh-cn' &&
          (data.domain == 'sms' || data.domain == 'tv' || data.domain == 'sos-tv') &&
          data.isFar == '0'
        ) {
          setRelationSelected('sms,zh-cn,0')
        } else {
          setRelationSelected([data.domain, data.language, data.isFar, data.accent].join(','))
        }
        setLangSelected(data.accent)
      } else {
        setRelationSelected(null)
        setLangSelected(null)
      }
    })
  }
  const getAccentList = () => {
    ajax({
      url: '/app/dist/sos/getAccentAndDomain',
      data: {
        appid: agentDetail.appId
      },
      method: 'get'
    }).then((res) => {
      let accentAndDomain = res.data.data
      let audioConfList = [
        {
          domain: 'sms',
          isFar: '0',
          key: 'sms,zh-cn,0',
          language: 'zh-cn',
          label: '通用-中文-近场',
          disabled: false,
          value: 'sms,zh-cn,0'
        },
        ...(accentAndDomain.relations || []).map((item) => {
          return {
            ...item,
            disabled: !item.select,
            label: item.value,
            value: item.key
          }
        })
      ]
      let smsRelations = (accentAndDomain.smsRelations || []).map((item) => {
        return {
          ...item,
          disabled: !item.select,
          label: item.value,
          value: item.accent
        }
      })
      setRelations(audioConfList)
      setLangList(smsRelations)
    })
  }
  const changeOption = (val) => {
    setRelationSelected(val)
    let valArr = val.split(',')
    let langDefault
    if (!langSelected) {
      langDefault = langList.find((item) => item.select === true).accent
      setLangSelected(langDefault)
    }
    // console.log('获取识别拼接结果',val)
    let params = {
      botId: agentDetail?.boxBot?.botId,
      language: valArr[1], //语言
      isFar: valArr[2], //距离
      accent: val == 'sms,zh-cn,0' ? langSelected || langDefault : valArr[3], //方言
      domain:
        val == 'sms,zh-cn,0'
          ? langList.find((item) => item.accent === (langSelected || langDefault)).domain
          : valArr[0], //领域
      ptt: '0',
      nunum: '0',
      dwa: '0'
    }
    ajax({
      url: '/bot/iat/saveConfig',
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
        }
      })
      .catch((err) => {
        getAccentDetail()
      })
  }

  useEffect(() => {
    if (agentDetail?.appId) {
      getAccentList()
    }
    if (agentDetail?.boxBot) {
      getAccentDetail()
    }
  }, [agentDetail])
  return (
    <>
      <div className={styles['base-title']}>
        <span className={styles['header-left']}>
          识别&nbsp;
          <Tooltip title="根据需求选择不同的识别引擎，会提高对应领域内识别的准确率。">
            <QuestionCircleOutlined />
          </Tooltip>
        </span>
        <span className={styles['header-right']}>
          更多设置&nbsp;
          <RightOutlined />
        </span>
      </div>
      <div style={{ display: 'flex', width: '100%', gap: '12px' }}>
        <Select
          options={relations}
          style={{ flex: 6 }}
          placeholder="请选择识别引擎"
          value={relationSelected}
          onChange={changeOption}
        />
        <Select
          options={langList}
          style={{ flex: 4 }}
          placeholder="请选择方言"
          value={langSelected}
        />
      </div>
    </>
  )
}

export default Recognition
