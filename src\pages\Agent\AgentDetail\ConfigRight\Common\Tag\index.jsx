import IconPlugin from 'assets/svgs/plugin.svg?react'
import IconKnowledge from 'assets/svgs/knowledge.svg?react'
import IconWorkflow from 'assets/svgs/agentConf/workflow.svg?react'
import IconSkill from 'assets/svgs/agentConf/skill.svg?react'
import styles from './style.module.scss'
import { message, Tag as ATag } from 'antd'

const Components = {
  plugin: IconPlugin,
  knowledge: IconKnowledge,
  workflow: IconWorkflow,
  skill: IconSkill
}

function Tag({ name, type, info }) {
  const Component = Components[type] || IconPlugin

  /**
   * TODO: 跳转的地址不应该写在里面，最好是在组件外部接收回调
   */

  const gotoDetail = () => {
    switch (type) {
      case 'plugin':
        console.log(info, 'info')
        if (info?.multi) {
          return message.warning('暂无权限')
        }
        if (info?.isAuthor) {
          window.open(`/sparkos/workspace/plugin/${info?.pluginId}/detail`, '_blank')
        } else {
          message.warning('暂无权限')
        }
        break
      case 'knowledge':
        window.open(`/sparkos/repo/${info.id}/docManage`, '_blank')
        break
      case 'flow':
        console.log(info, 'info')
        if (info?.isAuthor) {
          window.open(`/sparkos/flow/${info.id}`, '_blank')
        } else {
          message.warning('暂无权限')
        }
        break
    }
  }

  return (
    <span className={styles.tag} onClick={gotoDetail}>
      <Component />
      <span style={{ marginLeft: 4, marginRight: 4 }}>{name}</span>
      {'plugin' === type && (
        <ATag color={info.multi ? 'green' : 'blue'}>{info.multi ? '多阶' : '单阶'}</ATag>
      )}
    </span>
  )
}
export default Tag
