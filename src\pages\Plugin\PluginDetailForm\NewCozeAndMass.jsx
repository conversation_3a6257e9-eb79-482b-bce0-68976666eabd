import { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import ajax from '@/utils/http'
import { Input, Button, Space, App, Form, Select, Row, Col } from 'antd'
import useFormStore from '@/pages/ToolBox/formData.js'
import FormRow from './FormRow'
import { PlusCircleOutlined } from '@ant-design/icons'
import { v4 as uuidv4, validate } from 'uuid'

const NewCozeAndMass = forwardRef((props, ref) => {
  const [form] = Form.useForm()
  const { Option } = Select

  const { formData, setFormData } = useFormStore()
  const [resourceList, setResourceList] = useState([])

  const { massOrCozeInfo, onFormValuesChange, pluginType } = props

  useImperativeHandle(ref, () => ({
    validate: async () => {
      try {
        const values = await form.validateFields()
        setFormData(values)
        return values
      } catch (error) {
        return null
      }
    }
  }))

  const changeResourceType = (value) => {
    const info = resourceList.find((item) => item.resourceCode === value)
    console.log(info, '===>info')

    if (info.resourceCode === massOrCozeInfo?.extra?.thirdPartyInfo?.resourceType) {
      initFormSchema(massOrCozeInfo?.extra?.thirdPartyInfo?.webSchema)
    } else {
      initFormSchema(info.webSchema)
    }

    form.setFieldsValue({
      httpMethod: info?.httpMethod,
      resourceUrl: info?.resourceUrl
    })
  }

  const initFormSchema = (webSchema) => {
    const webSchemaObj = JSON.parse(webSchema || '{}')

    // 获取用户添加的字段名称列表（如果存在的话）
    const userAddedInputFields = webSchemaObj?.userAddedFields?.input || []
    const userAddedOutputFields = webSchemaObj?.userAddedFields?.output || []

    //  看是否是自定义的字段  需要区分，
    // 为接口返回的数据项标记isUserAdded为false
    const inputData = JSON.parse(JSON.stringify(webSchemaObj?.toolRequestInput) || '{}')
    const outputData = JSON.parse(JSON.stringify(webSchemaObj?.toolRequestOutput) || '{}')

    if (webSchemaObj.toolRequestInput) {
      inputData.forEach((item) => {
        // 如果字段名称在用户添加的列表中，则标记为用户添加
        item.isUserAdded = userAddedInputFields.includes(item.name)
        // 递归处理嵌套的children
        const markChildren = (obj) => {
          if (obj.children && obj.children.length > 0) {
            obj.children.forEach((child) => {
              child.isUserAdded = userAddedInputFields.includes(child.name)
              markChildren(child)
            })
          }
        }
        markChildren(item)
      })
    }

    if (webSchemaObj.toolRequestOutput) {
      outputData.forEach((item) => {
        // 如果字段名称在用户添加的列表中，则标记为用户添加
        item.isUserAdded = userAddedOutputFields.includes(item.name)
        // 递归处理嵌套的children
        const markChildren = (obj) => {
          if (obj.children && obj.children.length > 0) {
            obj.children.forEach((child) => {
              child.isUserAdded = userAddedOutputFields.includes(child.name)
              markChildren(child)
            })
          }
        }
        markChildren(item)
      })
    }

    form.setFieldsValue({
      toolRequestInput: inputData,
      toolRequestOutput: outputData
    })
  }

  const getThirdPartyInfo = () => {
    ajax({
      url: '/aiui-agent/third-party/type/all',
      method: 'GET'
    })
      .then((res) => {
        if (res.data.code === '0') {
          if (pluginType === 3) {
            const myItem = res.data.data.find((item) => item.code === 'coze')
            setResourceList(myItem.resourceList)
          } else if (pluginType === 4) {
            const myItem = res.data.data.find((item) => item.code === 'maas')
            setResourceList(myItem.resourceList)
          }
        }
      })
      .catch((err) => {})
  }

  useEffect(() => {
    getThirdPartyInfo()
  }, [])

  useEffect(() => {
    console.log(massOrCozeInfo, '打印过来的massOrCozeInfo')
    if (massOrCozeInfo) {
      initFormSchema(massOrCozeInfo?.extra?.thirdPartyInfo?.webSchema)
      form.setFieldsValue({
        resourceUrl: massOrCozeInfo?.extra?.thirdPartyInfo?.resourceUrl,
        httpMethod: massOrCozeInfo?.extra?.thirdPartyInfo?.httpMethod,
        resourceType: massOrCozeInfo?.extra?.thirdPartyInfo?.resourceType
      })
    }
  }, [massOrCozeInfo])

  useEffect(() => {
    if (pluginType === 3) {
      form.setFieldValue('platform', 'coze')
    } else if (pluginType === 4) {
      form.setFieldValue('platform', 'maas')
    }
  }, [pluginType])

  return (
    <div>
      <Form form={form} layout="vertical">
        <Row gutter={15}>
          <Col span={4}>
            <Form.Item name="platform" label="平台" rules={[{ required: true, message: '' }]}>
              <Select disabled>
                <Option value="coze">coze</Option>
                <Option value="星辰">maas</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item
              name="resourceType"
              label="插件类型"
              rules={[{ required: true, message: '请选择插件类型' }]}
            >
              <Select onChange={changeResourceType}>
                <Option value="bot">智能体</Option>
                <Option value="workflow">工作流</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item
              name="httpMethod"
              label="请求方式"
              rules={[{ required: true, message: '请选择请求方式' }]}
            >
              <Select disabled>
                <Option value="POST">POST</Option>
                <Option value="GET">GET</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="resourceUrl"
              label="API链接"
              rules={[{ required: true, message: '请输入API链接' }]}
            >
              <Input disabled></Input>
            </Form.Item>
          </Col>
        </Row>

        <div style={{ fontWeight: 400, color: '#897171', marginBottom: '10px' }}>输入参数</div>

        <Row gutter={16} style={{ marginBottom: '15px' }}>
          <Col span={1}></Col>
          <Col span={3}>参数名称</Col>
          <Col span={4}>参数描述</Col>
          <Col span={3}>参数类型</Col>
          <Col span={3}>传入方法</Col>
          <Col span={3}>是否必填</Col>
          {/* <Col span={3}>取值来源</Col> */}
          <Col span={3}>默认值</Col>
          <Col span={2}>开启</Col>
          <Col span={2}>操作</Col>
        </Row>

        <Form.List name="toolRequestInput" initialValue={[]}>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <FormRow
                  key={key}
                  fieldKey={key}
                  name={[name]}
                  restField={restField}
                  remove={remove}
                  area={'toolRequestInput'}
                />
              ))}
              {fields.length <= 2 && (
                <Form.Item>
                  <Button
                    type="dashed"
                    block
                    icon={<PlusCircleOutlined />}
                    onClick={() =>
                      add({
                        id: uuidv4(),
                        type: 'string', // 默认参数类型
                        location: 'query', // 默认传入方法
                        required: true, // 默认是否必填
                        // from: 2, // 默认取值来源
                        open: true, // 默认开启
                        startDisabled: false,
                        nameErrMsg: '',
                        descriptionErrMsg: '',
                        isUserAdded: true
                      })
                    }
                  >
                    添加
                  </Button>
                </Form.Item>
              )}
            </>
          )}
        </Form.List>

        <div style={{ fontWeight: 400, color: '#897171', marginBottom: '10px' }}>输出参数</div>

        <Row gutter={16} style={{ marginBottom: '15px' }}>
          <Col span={1}></Col>
          <Col span={7}>参数名称</Col>
          <Col span={8}>参数描述</Col>
          <Col span={4}>参数类型</Col>
          <Col span={2}>开启</Col>
          <Col span={2}>操作</Col>
        </Row>

        <Form.List name="toolRequestOutput">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <FormRow
                  key={key}
                  fieldKey={key}
                  name={[name]}
                  restField={restField}
                  remove={remove}
                  area={'toolRequestOutput'}
                />
              ))}
              {fields.length <= 2 && (
                <Form.Item>
                  <Button
                    type="dashed"
                    block
                    icon={<PlusCircleOutlined />}
                    onClick={() =>
                      add({
                        id: uuidv4(),
                        type: 'string', // 默认参数类型
                        location: 'query', // 默认传入方法
                        required: true, // 默认是否必填
                        // from: 2, // 默认取值来源
                        open: true, // 默认开启
                        startDisabled: false,
                        nameErrMsg: '',
                        descriptionErrMsg: '',
                        isUserAdded: true
                      })
                    }
                  >
                    添加
                  </Button>
                </Form.Item>
              )}
            </>
          )}
        </Form.List>
      </Form>
    </div>
  )
})

export default NewCozeAndMass
