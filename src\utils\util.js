import { message } from 'antd'

const createAwaitTo =
  (successCode = '0') =>
  async (promise, setLoading, options = {}) => {
    const { showSuccess = false, showError = true, successMessage, errorMessage } = options
    try {
      setLoading?.(true)
      const response = await promise

      if (Array.isArray(response)) {
        const hasError = response.some((res) => res?.data?.code !== successCode)
        if (!hasError) {
          showSuccess && message.success(successMessage || '操作成功')
          return [null, response]
        }
        const errorRes = response.find((res) => res?.data?.code !== successCode)
        showError && message.error(errorMessage || errorRes?.data?.desc || '操作失败')
        return [errorRes, null]
      }

      if (response?.data?.code === successCode) {
        showSuccess && message.success(successMessage || response?.data?.desc || '操作成功')
        return [null, response]
      }
      showError && message.error(errorMessage || response?.data?.desc || '操作失败')
      return [response, null]
    } catch (error) {
      showError && message.error(errorMessage || error?.data?.desc || '操作失败')
      return [error, null]
    } finally {
      setLoading?.(false)
    }
  }

// 简化异步操作
export const awaitTo = createAwaitTo()
