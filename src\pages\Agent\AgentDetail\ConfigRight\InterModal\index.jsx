import { MyModal } from '@/components'
import { useMemo, useEffect } from 'react'
import PropTypes from 'prop-types'
import { axios } from '@/utils/http'
import { transform, isObject, isPlainObject, isEmpty } from 'lodash'
import { awaitTo } from '@/utils/util'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { Table, Select, Checkbox, Col, Row, Tooltip, Popover, Button } from 'antd'
import { useConfigRightResource } from '../hooks/useConfigRight'
import { REJECT_CONFIG_OPTION, MULTI_REJECT_CONFIG_OPTION } from '../constant'

const InterModal = ({ visible, onClose, onRefresh }) => {
  const { list, loading, setList, setLoading, fetchResources } = useConfigRightResource(
    'atomicAbility',
    visible
  )
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const botId = useMemo(() => agentDetail?.boxBot?.botId, [agentDetail])

  const columns = [
    {
      title: '能力项',
      dataIndex: 'abilityName',
      key: 'abilityName',
      align: 'center',
      render: (value, row) => {
        return (
          <div className="h-full flex items-center justify-center flex-col">
            <p className="truncate" title={value}>
              {value}
            </p>
            <p className="mt-[6px] text-[#4d4b4b] text-[12px] truncate" title={row.abilityCode}>
              {row.abilityCode}
            </p>
          </div>
        )
      }
    },
    {
      title: '干预包编号/名称',
      dataIndex: 'number',
      key: 'number',
      align: 'center',
      width: 260,
      render: (value, row) => {
        const options = row.packages

        // const firstValue = row.interventions?.[0]?.id ? Number(row.interventions[0].id) : null
        const tempInterventions = (row.interventions || []).filter((it) => it.operation !== 'close')

        const firstValue = tempInterventions?.[0]?.id ? Number(tempInterventions[0].id) : null

        const defaultValue =
          (row.interventions || []).find((it) => it.operation === 'open')?.id || firstValue

        return (
          <div className="h-full flex items-center justify-center">
            {options?.length ? (
              <Select
                placeholder="请选择干预包"
                value={defaultValue}
                className="w-full"
                allowClear
                onChange={(value) => onSelectChange(value, row)}
                showSearch
                // dropdownStyle={{ minWidth: 500 }}
                onClear={() => onSelectClear(row)}
                filterOption={(input, option) => {
                  const searchText = option.props['data-customtext'].toLowerCase()
                  return searchText.includes(input.toLowerCase())
                }}
              >
                {options?.map((x) => (
                  <Select.Option key={x.id} value={x.id} data-customtext={`${x.number}/${x.name}`}>
                    {/* {x.number}/{x.name} */}
                    <Tooltip
                      title={
                        <>
                          {x.number}/{x.name}
                        </>
                      }
                      placement="right"
                    >
                      <span
                        style={{
                          width: '100%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}
                      >
                        {x.number}/{x.name}
                      </span>
                    </Tooltip>
                  </Select.Option>
                ))}
              </Select>
            ) : (
              '--'
            )}
          </div>
        )
      }
    },
    {
      title: '版本号',
      dataIndex: 'version',
      key: 'version',
      align: 'center',
      width: 80,
      render: (value, row) => {
        const options = row.packages
        const version = options?.find((x) => x.id == row.interventions?.[0]?.id)?.version || '--'
        return <div className="h-full flex items-center justify-center">{version}</div>
      }
    },
    {
      title: '说明',
      dataIndex: 'description',
      key: 'description',
      align: 'center',
      render: (value, row) => {
        const options = row.packages
        const description =
          options?.find((x) => x.id == row.interventions?.[0]?.id)?.description || '--'
        return <div className="h-full flex items-center justify-center">{description}</div>
      }
    },
    {
      title: '拒识配置',
      dataIndex: 'rejectionConfig',
      key: 'rejectionConfig',
      width: 300,
      align: 'center',
      render: (value, row) => {
        const options = REJECT_CONFIG_OPTION.filter((x) => row[x.value])
        const defaultValue = options.filter((x) => row[x.value] == 2).map((x) => x.value)

        // 多人拒识
        const multiOptions = MULTI_REJECT_CONFIG_OPTION.filter((x) => row[x.value])
        const multiDefaultValue = multiOptions.filter((x) => row[x.value] == 2).map((x) => x.value)

        const content = (
          <div style={{ minWidth: 180 }}>
            <div style={{ color: '#999', marginBottom: 10 }}>单人拒识</div>
            <Checkbox.Group
              defaultValue={defaultValue}
              className="text-left w-full"
              onChange={(value) => onRejectionConfigChange(value, row)}
            >
              <Row className="w-full">
                {options.map((x, i) => {
                  return (
                    <Col span={12} key={x.value} className={i >= 2 ? 'mt-[10px]' : ''}>
                      <Checkbox value={x.value}>{x.label}</Checkbox>
                    </Col>
                  )
                })}
                {!options.length && '--'}
              </Row>
            </Checkbox.Group>

            <div style={{ color: '#999', marginBottom: 10, marginTop: 10 }}>多人拒识</div>
            <Checkbox.Group
              defaultValue={multiDefaultValue}
              className="text-left w-full"
              onChange={(value) => onMultiRejectionConfigChange(value, row)}
            >
              <Row className="w-full">
                {multiOptions.map((x, i) => {
                  return (
                    <Col span={12} key={x.value} className={i >= 2 ? 'mt-[10px]' : ''}>
                      <Checkbox value={x.value}>{x.label}</Checkbox>
                    </Col>
                  )
                })}
                {!multiOptions.length && '--'}
              </Row>
            </Checkbox.Group>
          </div>
        )
        return options.length === 0 && multiOptions.length === 0 ? (
          '--'
        ) : (
          <Popover placement="top" title={null} content={content}>
            <Button>拒识配置</Button>
          </Popover>
        )
      }
    }
  ]

  useEffect(() => {
    visible && fetchResources()
  }, [visible, fetchResources])

  // 选择干预包触发
  const onSelectChange = (value, row) => {
    if (!value) {
      // clear清空操作造成的change直接返回
      return
    }

    const oldRow = JSON.parse(JSON.stringify(row))

    const { version, description, id } = row.packages?.find((x) => x.id == value) || {}
    row.interventions ??= [{ version: '', type: '', id: '' }] // 兼容字段没有的情况

    let newInterventions = (row.interventions || []).map((item) => {
      return {
        ...item,
        iId: item.id,
        operation: Number(item.id) === Number(value) ? 'open' : 'close'
      }
    })
    // operation: Number(item.id) === Number(value) ? 'open' : 'close'
    if (newInterventions.findIndex((it) => Number(it.id) === Number(value)) === -1) {
      newInterventions.push({
        id: value,
        iId: value,
        operation: 'open',
        version: (row.packages || []).find((it) => Number(it.id) === Number(value))?.version || ''
      })
    }

    // Object.assign(row.interventions[0], {
    //   version,
    //   id: value,
    //   description,
    //   operation: value ? 'open' : 'close',
    //   iId: id || oldRow.interventions[0]?.id
    // })
    setList((prev) =>
      prev.map((x) => {
        // (x.abilityId === row.abilityId ? row : x)
        return {
          ...x,
          interventions: x.abilityId === row.abilityId ? newInterventions : x.interventions
        }
      })
    )
  }

  const onSelectClear = (row) => {
    let newInterventions = (row.interventions || []).map((item) => {
      return {
        ...item,
        iId: item.id,
        operation: 'close'
      }
    })

    setList((prev) =>
      prev.map((x) => {
        return {
          ...x,
          interventions: x.abilityId === row.abilityId ? newInterventions : x.interventions
        }
      })
    )
  }

  // 拒识配置触发
  const onRejectionConfigChange = (value, row) => {
    // 遍历所有可选项,选中的置为2,未选中的置为1
    const values = REJECT_CONFIG_OPTION.reduce(
      (pre, cur) => ({
        ...pre,
        [cur.value]: row[cur.value] ? (value.includes(cur.value) ? 2 : 1) : undefined
      }),
      {}
    )
    Object.assign(row, values)
    setList((prev) => prev.map((x) => (x.abilityId === row.abilityId ? row : x)))
  }

  const onMultiRejectionConfigChange = (value, row) => {
    // 遍历所有可选项,选中的置为2,未选中的置为1
    const values = MULTI_REJECT_CONFIG_OPTION.reduce(
      (pre, cur) => ({
        ...pre,
        [cur.value]: row[cur.value] ? (value.includes(cur.value) ? 2 : 1) : undefined
      }),
      {}
    )
    Object.assign(row, values)
    setList((prev) => prev.map((x) => (x.abilityId === row.abilityId ? row : x)))
  }

  // 去除对象空值
  const removeEmpty = (obj) =>
    transform(obj, (result, v, k) => {
      if (isObject(v)) v = removeEmpty(v) // 递归处理对象
      if (!(isPlainObject(v) && isEmpty(v))) result[k] = v // 保留非空对象
    })

  // 保存
  const onOk = async () => {
    console.log('list', list)
    const filterList = list.filter(
      (x) => x.interventions?.length || REJECT_CONFIG_OPTION.some((y) => x[y.value])
    )
    console.log(filterList)
    const interventionConfigs = filterList.reduce((pre, cur) => {
      const interventions =
        (cur.interventions || []).map((x) => ({
          iId: String(x.iId || x.id),
          operation: x.operation || 'open',
          version: x.version,
          // type: 2
          type: (cur.packages || []).find((it) => Number(it.id) === Number(x.iId || x.id))?.type
        })) || []
      // const interventionConfig = cur.interventions?.length ? { [cur.abilityId]: interventions } : {}
      const interventionConfig = { [cur.abilityId]: interventions }
      const personaAbles = cur.personaAble ? { [cur.abilityId]: cur.personaAble } : {}
      const arcAbles = cur.arcAble ? { [cur.abilityId]: cur.arcAble } : {}
      const empathyAbles = cur.empathyAble ? { [cur.abilityId]: cur.empathyAble } : {}
      const referenceAbles = cur.referenceAble ? { [cur.abilityId]: cur.referenceAble } : {}

      const completeFluencyAbles = cur.completeFluencyAble
        ? { [cur.abilityId]: cur.completeFluencyAble }
        : {}

      // 多人拒识
      const multiPersonaAbles = cur.multiPersonaAble
        ? { [cur.abilityId]: cur.multiPersonaAble }
        : {}
      const multiArcAbles = cur.multiArcAble ? { [cur.abilityId]: cur.multiArcAble } : {}
      const multiEmpathyAbles = cur.multiEmpathyAble
        ? { [cur.abilityId]: cur.multiEmpathyAble }
        : {}
      const multiReferenceAbles = cur.multiReferenceAble
        ? { [cur.abilityId]: cur.multiReferenceAble }
        : {}

      const multiCompleteFluencyAbles = cur.multiCompleteFluencyAble
        ? { [cur.abilityId]: cur.multiCompleteFluencyAble }
        : {}

      return {
        // 干预包配置
        interventions: { ...(pre.interventions || {}), ...interventionConfig },
        // 拒识配置
        personaAbles: { ...(pre.personaAbles || {}), ...personaAbles },
        arcAbles: { ...(pre.arcAbles || {}), ...arcAbles },
        empathyAbles: { ...(pre.empathyAbles || {}), ...empathyAbles },
        referenceAbles: { ...(pre.referenceAbles || {}), ...referenceAbles },

        completeFluencyAbles: { ...(pre.completeFluencyAbles || {}), ...completeFluencyAbles },

        // 多人拒识配置
        multiPersonaAbles: { ...(pre.multiPersonaAbles || {}), ...multiPersonaAbles },
        multiArcAbles: { ...(pre.multiArcAbles || {}), ...multiArcAbles },
        multiEmpathyAbles: { ...(pre.multiEmpathyAbles || {}), ...multiEmpathyAbles },
        multiReferenceAbles: { ...(pre.multiReferenceAbles || {}), ...multiReferenceAbles },

        multiCompleteFluencyAbles: {
          ...(pre.multiCompleteFluencyAbles || {}),
          ...multiCompleteFluencyAbles
        }
      }
    }, {})

    const {
      interventions,
      personaAbles,
      arcAbles,
      empathyAbles,
      referenceAbles,
      completeFluencyAbles,

      multiPersonaAbles,
      multiArcAbles,
      multiEmpathyAbles,
      multiReferenceAbles,
      multiCompleteFluencyAbles
    } = interventionConfigs
    const interveneParams = { interventionConfigs: interventions, botId }
    const rejectParams = removeEmpty({
      personaAbles: {
        ...personaAbles,
        multi: multiPersonaAbles
      },
      arcAbles: {
        ...arcAbles,
        multi: multiArcAbles
      },
      empathyAbles: {
        ...empathyAbles,
        multi: multiEmpathyAbles
      },
      referenceAbles: {
        ...referenceAbles,
        multi: multiReferenceAbles
      },

      completeFluencyAbles: {
        ...completeFluencyAbles,
        multi: multiCompleteFluencyAbles
      },

      botId
    })
    // const rejectApi = () => axios.post('/bot/config/saveAbleAndSearchConfig', rejectParams)
    const interveneApi = () =>
      axios.post('/bot/config/saveInterventionConfig', { ...interveneParams, ...rejectParams })
    // 拒识配合和干预配置分两个接口保存--修改为1个接口
    const api = () => Promise.all([/*rejectApi(),*/ interveneApi()])
    const [err] = await awaitTo(api(), setLoading, { showSuccess: true })
    if (err) return false
    onClose()
    onRefresh()
  }

  return (
    <MyModal
      width={1000}
      visible={visible}
      onCancel={onClose}
      confirmLoading={loading}
      onOk={onOk}
      title="原子能力配置"
      destroyOnHidden
    >
      <Table
        className="mb-[18px]"
        sticky={true}
        virtual={true}
        columns={columns}
        dataSource={list}
        pagination={false}
        scroll={{ y: 400 }}
        onRow={() => ({ className: '' })}
        loading={loading}
        rowKey={(record) => record.abilityCode}
      />
    </MyModal>
  )
}

InterModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onRefresh: PropTypes.func
}

export default InterModal
