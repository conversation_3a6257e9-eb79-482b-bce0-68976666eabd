export const webSchema = {
  toolRequestInput: [
    {
      id: 'ae4ecb6f-8d24-4cfa-aa7d-fd8c805293f9',
      name: 'uid',
      description: '用户id',
      type: 'number',
      location: 'body',
      required: true,
      defaultValue: '1',
      open: true,
      from: 2,
      startDisabled: false,
      nameErrMsg: '',
      descriptionErrMsg: ''
    },
    {
      id: '726dbbbd-bdd1-4159-a68a-2081ecb0f03f',
      name: 'chat_id',
      description: '会话id',
      type: 'string',
      location: 'body',
      required: true,
      defaultValue: '1',
      open: true,
      from: 2,
      startDisabled: false,
      nameErrMsg: '',
      descriptionErrMsg: ''
    },
    {
      id: 'dc6d16f3-3f2c-4716-8642-79e569aeb3c8',
      name: 'file_list',
      description: '文件列表',
      type: 'array',
      location: 'body',
      required: true,
      defaultValue: [],
      open: true,
      from: 2,
      startDisabled: true,
      nameErrMsg: '',
      children: [
        {
          id: 'db48f9db-fc26-4246-a74e-bd999f89f82f',
          name: '[Array Item]',
          description: '文件元素',
          type: 'object',
          location: 'query',
          required: true,
          open: true,
          from: 2,
          fatherType: 'array',
          arraySon: true,
          descriptionErrMsg: '',
          children: [
            {
              id: '2e9ddc7b-7c3f-4ce5-b319-7d8cf036f81f',
              name: 'file_name',
              description: '文件名称',
              type: 'string',
              location: 'query',
              required: true,
              defaultValue: '',
              open: true,
              from: 2,
              fatherType: 'object',
              arraySon: true,
              nameErrMsg: '',
              descriptionErrMsg: ''
            },
            {
              id: 'e9ea12ff-11b8-41ba-b28f-3d4f296676c6',
              name: 'file_url',
              description: '文件url',
              type: 'string',
              location: 'query',
              required: true,
              defaultValue: '',
              open: true,
              from: 2,
              startDisabled: true,
              fatherType: 'object',
              arraySon: true,
              nameErrMsg: '',
              descriptionErrMsg: ''
            },
            {
              id: '6add51bc-cdcd-4d91-82ed-a1c432977e19',
              name: 'file_biz_type',
              description: '文件类型',
              type: 'string',
              location: 'query',
              required: true,
              defaultValue: '',
              open: true,
              from: 2,
              startDisabled: true,
              fatherType: 'object',
              arraySon: true,
              nameErrMsg: '',
              descriptionErrMsg: ''
            }
          ],
          nameErrMsg: ''
        }
      ],
      descriptionErrMsg: ''
    },
    {
      id: 'f18b1a88-ea98-4dec-9100-f8a473b02f31',
      name: 'QQ',
      description: 'qq',
      type: 'object',
      location: 'body',
      required: true,
      open: true,
      from: 2,
      startDisabled: true,
      nameErrMsg: '',
      descriptionErrMsg: '',
      children: [
        {
          id: '78da59db-a2f6-43ed-8b0c-f0ddebe23794',
          name: 'WW',
          description: 'ww',
          type: 'string',
          location: 'query',
          required: true,
          defaultValue: '1',
          open: true,
          from: 2,
          fatherType: 'object',
          nameErrMsg: '',
          descriptionErrMsg: '',
          startDisabled: false
        },
        {
          id: '70dc682a-aa92-4630-b9df-57038084739a',
          name: 'EE',
          description: 'ee',
          type: 'array',
          location: 'query',
          required: true,
          defaultValue: [],
          open: true,
          from: 2,
          startDisabled: true,
          fatherType: 'object',
          children: [
            {
              id: '8c2d8749-a522-4963-84d1-dd89ea947ab9',
              name: '[Array Item]',
              description: 'ces',
              type: 'object',
              location: 'query',
              required: true,
              open: true,
              from: 2,
              fatherType: 'array',
              arraySon: true,
              children: [
                {
                  id: 'aacb13ea-dda6-4491-8909-c3e2cdb0be0d',
                  name: 'RR',
                  description: 'rr',
                  type: 'string',
                  location: 'query',
                  required: true,
                  defaultValue: '',
                  open: true,
                  from: 2,
                  fatherType: 'object',
                  arraySon: true,
                  nameErrMsg: '',
                  descriptionErrMsg: ''
                },
                {
                  id: '49bd595c-6b08-4103-9a13-b47bad9c2717',
                  name: 'BB',
                  description: 'bb',
                  type: 'string',
                  location: 'query',
                  required: true,
                  defaultValue: '',
                  open: true,
                  from: 2,
                  startDisabled: true,
                  fatherType: 'object',
                  arraySon: true,
                  nameErrMsg: '',
                  descriptionErrMsg: ''
                }
              ],
              descriptionErrMsg: '',
              nameErrMsg: ''
            }
          ],
          nameErrMsg: '',
          descriptionErrMsg: ''
        },
        {
          id: '147c2e79-3033-462a-ba24-1476c61acda6',
          name: 'DD',
          description: 'dd',
          type: 'string',
          location: 'query',
          required: true,
          defaultValue: '',
          open: true,
          from: 2,
          startDisabled: true,
          fatherType: 'object',
          nameErrMsg: '',
          descriptionErrMsg: ''
        },
        {
          id: '5e43a83f-6e15-42a8-a1c1-c214217aa002',
          name: 'FF',
          description: 'ff',
          type: 'string',
          location: 'query',
          required: true,
          defaultValue: '',
          open: true,
          from: 2,
          startDisabled: true,
          fatherType: 'object',
          nameErrMsg: '',
          descriptionErrMsg: ''
        },
        {
          id: 'a099d55f-6809-4c3d-9061-0fcca6262ec9',
          name: 'VVV',
          description: 'vv',
          type: 'array',
          location: 'query',
          required: true,
          defaultValue: [],
          open: true,
          from: 2,
          startDisabled: true,
          fatherType: 'object',
          children: [
            {
              id: '733fc8f6-eed1-47c6-925f-17a9026a58b3',
              name: '[Array Item]',
              description: '第二层',
              type: 'object',
              location: 'query',
              required: true,
              open: true,
              from: 2,
              fatherType: 'array',
              arraySon: true,
              descriptionErrMsg: '',
              children: [
                {
                  id: '0160f216-3a72-4c08-9157-9b6c2a596b01',
                  name: 'XX',
                  description: 'xx',
                  type: 'string',
                  location: 'query',
                  required: true,
                  defaultValue: '',
                  open: true,
                  from: 2,
                  fatherType: 'object',
                  arraySon: true,
                  nameErrMsg: '',
                  descriptionErrMsg: ''
                },
                {
                  id: 'b605598d-f47b-4b9d-a5b6-a47c2c863436',
                  name: 'ZZ',
                  description: 'zz',
                  type: 'string',
                  location: 'query',
                  required: true,
                  defaultValue: '',
                  open: true,
                  from: 2,
                  startDisabled: true,
                  fatherType: 'object',
                  arraySon: true,
                  nameErrMsg: '',
                  descriptionErrMsg: ''
                }
              ],
              nameErrMsg: ''
            }
          ],
          nameErrMsg: '',
          descriptionErrMsg: ''
        }
      ],
      defaultValue: []
    },
    {
      id: 'b61e12e2-889a-4e5a-a9d6-85991138708f',
      name: 'CCCC',
      description: 'ccc',
      type: 'array',
      location: 'body',
      required: true,
      defaultValue: ['rttt', 'rrr', 'yyyyy'],
      open: true,
      from: 2,
      startDisabled: true,
      nameErrMsg: '',
      descriptionErrMsg: '',
      children: [
        {
          id: 'cd8c97dc-788b-4b33-a4a3-0b2abcc5517b',
          name: '[Array Item]',
          description: '数组',
          type: 'string',
          location: 'query',
          required: true,
          defaultValue: '',
          open: true,
          from: 2,
          fatherType: 'array',
          arraySon: true,
          nameErrMsg: '',
          descriptionErrMsg: ''
        }
      ]
    }
  ],
  toolRequestOutput: [
    {
      id: '82904880-1f97-48a8-81c3-4bc2ebf738d8',
      name: 'code',
      description: 'ere',
      type: 'number',
      open: true,
      nameErrMsg: '',
      descriptionErrMsg: ''
    },
    {
      id: 'ba2d84cd-7c95-48e2-8b49-a198ef6ef3d2',
      name: 'desc',
      description: 'rev',
      type: 'string',
      open: true,
      nameErrMsg: '',
      descriptionErrMsg: ''
    }
  ]
}
