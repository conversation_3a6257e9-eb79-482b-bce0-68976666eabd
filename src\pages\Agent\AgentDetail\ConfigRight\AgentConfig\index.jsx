import { <PERSON>lapse, Space, Button } from 'antd'
import styles from './style.module.scss'
import { useState } from 'react'
import AgentBeforeModal from '../AgentBeforeModal'
import { useConfigRightResource } from '../hooks/useConfigRight'
import PropTypes from 'prop-types'

import { RightOutlined, TagOutlined } from '@ant-design/icons'
function IntervConfig({ type }) {
  const [configModalOpen, setConfigModalOpen] = useState(false)

  const [activeKey, setActiveKey] = useState(['agent'])

  const useKey = { 2: 'before', 3: 'after' }[type] || 'before'
  const { list, fetchResources } = useConfigRightResource(useKey, true)

  const title = type === 2 ? '模型前处理配置' : '模型后处理配置'

  const onAddClick = (e) => {
    e.stopPropagation()
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const items = [
    {
      key: 'agent',
      showArrow: false,
      collapsible: 'icon',
      label: (
        <div className={styles.labelWithIcon}>
          <TagOutlined
            className={styles.arrow}
            // className={activeKey.includes('agent') ? styles.arrowExpanded : styles.arrow}
          />
          <span>{title}</span>
        </div>
      ),
      children: [],
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>{list.length ? '已配置' : '未配置'}</div>
          <Button type="dashed" onClick={onAddClick} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]

  const onChange = (val) => {
    setActiveKey(val)
  }

  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        ghost
        onChange={onChange}
        expandIcon={() => null}
      />
      <AgentBeforeModal
        type={type}
        visible={configModalOpen}
        onClose={onConfigModalCancel}
        onRefresh={fetchResources}
      />
    </>
  )
}

IntervConfig.propTypes = {
  type: PropTypes.number.isRequired
}

export default IntervConfig
