import { useState, useEffect, useRef } from 'react'
import { Input, Switch, message, But<PERSON>, Select } from 'antd'
import PropTypes from 'prop-types'
import styles from './style.module.scss'
import { MyModal } from '@/components'
import { APP_ENV, cssVariables } from '@/utils/constant'
import ajax from '@/utils/http'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'

import PostProcess from './PostProcess'

const ModelConfigModal = ({ visible, onClose }) => {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const [loading, setLoading] = useState(false)

  const postProcessRef = useRef(null)
  const preProcessRef = useRef(null)

  const handleSave = async () => {
    const valid1 = await preProcessRef?.current.validate()
    const valid2 = await postProcessRef?.current.validate()

    if (valid1 && valid2) {
      const save1 = await preProcessRef?.current.save()
      const save2 = await postProcessRef?.current.save()
      if (save1 && save2) {
        message.success('保存成功')
        onClose()
      }
    } else {
      return message.error('所有字段校验通过后再进行保存')
    }
  }

  return (
    <MyModal
      title="模型前后处理"
      open={visible}
      onCancel={onClose}
      onOk={handleSave}
      width={800}
      destroyOnHidden
      confirmLoading={loading}
    >
      <div className={styles.container}>
        <PostProcess
          visible={visible}
          ref={preProcessRef}
          type={2}
          text={{ name: '大模型前处理', desc: '支持业务方API接入,对上行数据进行预处理。' }}
        />
        <PostProcess
          visible={visible}
          ref={postProcessRef}
          style={{ marginTop: 10 }}
          type={3}
          text={{ name: '大模型后处理', desc: '支持业务方API接入,对下行数据进行预处理。' }}
        />
      </div>
    </MyModal>
  )
}

ModelConfigModal.propTypes = {
  visible: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
}

export default ModelConfigModal
