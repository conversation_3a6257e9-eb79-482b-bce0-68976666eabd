import { useState, useEffect, useRef } from 'react'
import { Modal, Button, Tabs, Form } from 'antd'
import { useParams } from 'react-router-dom'
import LocalUpload from './LocalUpload'
import OnlineUpload from './OnlineUpload'
import { MyModal } from '@/components'

const DocAddModal = (props) => {
  const { repoId } = useParams()
  const { visible, setVisble, fileCurrent, setFileCurrent, getDocs } = props

  const localRef = useRef(null)
  const onlineRef = useRef(null)
  const [activeTab, setActiveTab] = useState('local')
  const [addLoading, setAddLoading] = useState(false)
  const [filteredTabItems, setFilterTabItems] = useState()

  const handleSubmit = () => {
    if (activeTab === 'local') {
      if (localRef.current) {
        localRef.current.confirm() // 调用 LocalUpload 组件暴露的方法
      }
    } else if (activeTab === 'online') {
      if (onlineRef.current) onlineRef.current.confirm()
    }
  }

  const finish = () => {
    setVisble(false)
    getDocs()
    setFileCurrent(null)
  }

  const tabItems = [
    {
      label: '本地文档',
      key: 'local',
      children: (
        <LocalUpload
          ref={localRef}
          repoId={repoId}
          fileCurrent={fileCurrent}
          addLoading={addLoading}
          setAddLoading={setAddLoading}
          finish={finish}
        />
      )
    },
    {
      label: '在线文档',
      key: 'online',
      children: (
        <OnlineUpload
          ref={onlineRef}
          repoId={repoId}
          fileCurrent={fileCurrent}
          addLoading={addLoading}
          setAddLoading={setAddLoading}
          finish={finish}
        />
      )
    }
  ]

  useEffect(() => {
    if (fileCurrent?.id) {
      let fileType = fileCurrent.docType
      let items
      if (fileType === 'url') {
        items = tabItems.filter((item) => {
          if (item.key === 'local') {
            return false // 隐藏这个 tab
          }
          return true
        })
        setActiveTab('online')
      } else {
        items = tabItems.filter((item) => {
          if (item.key === 'online') {
            return false // 隐藏这个 tab
          }
          return true
        })
        setActiveTab('local')
      }
      setFilterTabItems(items)
    } else {
      setFilterTabItems(tabItems)
    }
  }, [fileCurrent])
  return (
    <MyModal
      title="添加文档"
      open={visible}
      onCancel={() => setVisble(false)}
      width={660}
      footer={[
        // <Button key="cancel" onClick={() => setVisble(false)}>
        //   取消
        // </Button>,
        <Button key="primary" type="primary" loading={addLoading} onClick={handleSubmit}>
          确认
        </Button>
      ]}
    >
      <Tabs
        type="card"
        style={{
          marginBottom: 32
        }}
        activeKey={activeTab}
        items={filteredTabItems}
        onTabClick={(key) => setActiveTab(key)}
      />
    </MyModal>
  )
}

export default DocAddModal
