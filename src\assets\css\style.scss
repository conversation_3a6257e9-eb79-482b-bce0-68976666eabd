.aside {
  height: 100%;
  .collapse {
    position: fixed;
    left: 10px;
    bottom: 10px;
    z-index: 1;
    cursor: pointer;
  }
}
.right-side {
  width: 60px;
  height: 100%;
  border-left: 1px solid #eff1f1;
  transition: width 0.2s ease;
}
.right-side-open {
  width: 320px;
  transition: width 0.2s ease;
}

// https://github.com/ant-design/ant-design/issues/28158#issuecomment-737648133
// flex 布局计算自适应除了 flex: auto 外你还需要配置一个 min-width: 0 让浏览器做缩放依据：
.main {
  height: 100%;
  flex: auto;
  min-width: 0;
  overflow: auto;
}
.page-list {
  height: 100%;
}
.page {
  height: 100%;
  // width: 100%;
  // display: flex;
  // flex-flow: wrap;
  // background: linear-gradient(90deg,#fbf7f4, #eef7ff 100%, #efeffb 100%, #fbf7f4 100%);
  background: #fff;
  .page-top {
    padding: 0px 24px;
    width: 100%;
    height: 60px;
    font-size: 20px;
    display: flex;
    align-items: center;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -1px;
      width: 100%;
      height: 1px;
      background-color: #e4e7ed;
      z-index: 1;
    }
    .page-top-left {
      -webkit-box-flex: 1;
      -ms-flex: auto;
      flex: auto;
      padding-right: 48px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .page-title-operate {
        display: flex;
        align-items: center;
        span {
          display: inline-block;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .page-title-operate-time {
        font-size: 12px;
        font-weight: 400;
        color: var(--desc-color);
        line-height: 16px;
      }
    }
    .page-top-right {
      display: flex;
      align-items: center;
    }
  }
  .page-scroll {
    padding: 0px;
    height: calc(100% - 60px);
    flex: 1 1 auto;
    transition: max-width 0.3s ease 0s;
    overflow-y: auto;
    .editor {
      height: 100%;
      // display: flex;
      overflow: hidden;
      position: relative;
    }
  }
}

.shortcut-container {
  padding: 0;
  width: 400px;
  li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    height: 40px;
  }
}
.shortcut-description {
  font-size: 14px;
  color: #666;
}
.shortcut {
  display: flex;
  align-items: center;
  .tag {
    display: block;
    padding: 2px 13px;
    height: 30px;
    border-radius: 30px;
    background: #f4f5f5;
    text-align: center;
    line-height: 24px;
  }
}
