// 卡片样式
.itemCard {
  display: flex;
  align-items: center;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 8px;
  background: linear-gradient(180deg, #ffffff, #fafafa 100%);
  border: 1px solid #d1d5db;
  cursor: pointer;
  &:hover {
    box-shadow:
      0px 2px 4px -1px rgba(0, 0, 0, 0.06),
      0px 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .content {
    flex: 1;
  }
  .title {
    font-size: 15px;
    font-weight: 500;
    color: #111827;
    line-height: 18px;
  }

  // 副标题
  .subtitle {
    margin-left: 6px;
    font-size: 12px;
    font-weight: 400;
    color: #6b7280;
    line-height: 15px;
    margin-top: 4px;
  }
  // 描述文本
  .description {
    margin-top: 4px;
    font-size: 13px;
    font-weight: 400;
    color: #6b7280;
    line-height: 15px;
  }
}

.cardContainer {
  display: grid;
  // grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-template-columns: repeat(3, minmax(300px, 1fr));
  grid-gap: 12px;
}

.docNum {
  width: 81px;
  height: 25px;
  background: #fafafa;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  font-size: 12px;
  line-height: 24px;
  color: #374151;
  font-weight: 500;
  padding: 0 9px;
  img {
    width: 17px;
    height: 16px;
    margin-right: 2px;
  }
}
