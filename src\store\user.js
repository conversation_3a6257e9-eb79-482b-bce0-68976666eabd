import { create } from 'zustand'

export const useUserStore = create((set) => ({
  account: {},
  setAccount: (account) => set({ account }),

  limitCount: {},
  setLimitCount: (limitCount) => set({ limitCount }),

  selectedMenus: ['home'],
  setSelectedMenus: (selectedMenus) => set({ selectedMenus }),

  selectedAgentMenus: ['baseInfo'],
  setSelectAgentMenus: (selectedMenus) => set({ selectedMenus })
}))
