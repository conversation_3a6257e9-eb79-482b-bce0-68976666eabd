FROM artifacts.iflytek.com/hy-docker-repo/aiuifyptv30r10/nginx:1.18.0
ENV port 5752
ADD /nginx.conf /etc/nginx/nginx.conf
RUN echo "server { \
  server_name localhost; \
  listen ${port}; \
  index index.html; \
  root /var/www; \
  location ~* ^/static/(.*) { \
      alias /var/www/static/\$1; \
  } \
  location / { \
      rewrite .*  /index.html break;\
  }\
  location ~ .*\.(gif|jpg|svg|jpeg|png|PNG|bmp|swf|asp|cfm|xml|py|pl|lasso|cfc|afp|txt|zip|log|ico|csv|json|xls|pdf|mp3|mp4|apk)$ \
  { \
    expires 1y; \
    access_log off; \
  } \
  location ~ .*\.(js|css|html)?$ \
  { \
    expires 1y; \
    access_log off; \
  } \
}" >> /etc/nginx/conf.d/aihub.conf
RUN rm /etc/nginx/conf.d/default.conf
EXPOSE ${port}
# 这个是找根目录中的dist 文件夹，然后放到 镜像中的/var/www 目录下
ADD /dist /var/www
# ADD /dist/favicon.ico /var/www/static/favicon.ico
CMD ["nginx"]
