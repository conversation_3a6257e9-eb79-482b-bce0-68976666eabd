.container {
  // background-color: #f1f7ff;
  height: 100%;
}
.body {
  flex: 1;
  overflow: auto;
}
.header {
  background-color: #f1f7ff;
  height: 56px;
  padding: 0 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .headerText {
    font-size: 16px;
    font-weight: 600;
    color: var(--title-color2);
    line-height: 19px;
  }
}
.banner {
  width: 1200px;
  height: 160px;
  background: url(assets/images/<EMAIL>) center/contain no-repeat;
  padding-top: 30px;
  padding-left: 42px;
  border-radius: 8px;
  margin: 24px auto 0;

  .bannerLeftTitle {
    width: 96px;
    height: 45px;
    font-size: 32px;
    font-weight: 500;
    line-height: 38px;

    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-image: linear-gradient(180deg, #ffffff, #ffffff 100%, rgba(255, 255, 255, 0.3) 100%);
    // color: #dae2f4;
  }
  .bannerLeftDesc {
    height: 20px;
    font-size: 14px;
    font-weight: 400;
    color: #dae2f4;
    line-height: 16px;
    margin-top: 12px;
  }
}
.content {
}
.layout {
  height: 100vh;
  // width: 1280px;
  margin: 0 auto;
  background-color: #f1f7ff;
  display: flex;
  flex-direction: column;
}
.sider {
  background: #f5f5f5;
  padding: 24px 16px;
  position: relative;
  .footer {
    position: absolute;
    bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 188px;
    .leftSection {
      display: flex;
      align-items: center;
    }
    .avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: linear-gradient(90deg, #e53739, #d71e5f 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 13px;
      font-weight: 400;
    }
    .username {
      margin-left: 10px;
      font-size: 11px;
      font-family:
        PingFang SC,
        PingFang SC-400;
      font-weight: 400;
      color: #616670;
    }
    .circleButton {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: 2px solid #e6e7e9; //#E6E7E9
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.platTitle {
  // text-align: center;
  // font-size: 16px;
  // font-family:
  //   PingFang SC,
  //   PingFang SC-400;
  // font-weight: 400;
  // color: #171717;
  // line-height: 26px;
  margin-bottom: 20px;
}
.line {
  margin-top: 25px;
  margin-bottom: 4px;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}
.leftMenu {
  border-right: none;
  border-inline-end: none !important;
  background: #f5f5f5;
  height: calc(100% - 150px);
  overflow: auto;
  :global(.ant-menu-item) {
    padding-left: 12px !important;
    font-weight: 500;
    font-size: 15px;
  }
  :global(.ant-menu-item-selected) {
    font-weight: 600;
  }
  :global(.ant-menu-item-group-title) {
    padding: 0 12px;
    margin-top: 16px;
  }
}
