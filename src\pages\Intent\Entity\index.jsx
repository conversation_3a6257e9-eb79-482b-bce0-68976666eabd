import { useState, useEffect, useRef } from 'react'
import { MyModal } from '@/components'
import {
  Descriptions,
  Input,
  Button,
  Dropdown,
  message,
  Table,
  Space,
  Tag,
  Tooltip,
  Modal
} from 'antd'
import {
  EditOutlined,
  SearchOutlined,
  CheckOutlined,
  CloseOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import ajax from '@/utils/http'
import './style.module.scss'
import UploadButton from './UploadButton'

const EntityDetail = (props) => {
  const { entityDetailVisible, setEntityDetailVisible, entitySelected, refreshList } = props
  const [entityDetail, setEntityDetail] = useState()
  const [nameEdit, setNameEdit] = useState(false)
  const [dictId, setDictId] = useState()
  const [search, setSearch] = useState('')
  const [dictData, setDictData] = useState([])
  const [current, setCurrent] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [total, setTotal] = useState(0)
  const [buildLoading, setBuildLoading] = useState(false)
  const [tableLoading, setTableLoading] = useState(false)
  const inputRef = useRef() //实体input的ref
  const inputRefs = useRef({}) //词条input的ref
  const lemmaInputRefs = useRef({}) //别名input的ref
  const regOfEntry =
    /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*\%'`_ -]+$/
  const regOfLemma =
    /^[\u4e00-\u9faf\uff00-\uffef\u30a0-\u30ff\u3000-\u303f\u3040-\u309F\u0400-\u052f\u00a0-\u00ff\u0100-\u017f\u0180-\u024f\u4e00-\u9fffa-zA-Z0-9\(\)\+\.\*\%'_ =-]+$/
  const [coverVisible, setCoverVisible] = useState(false)

  const entityItems = [
    {
      key: '1',
      label: '实体名称',
      children: (
        <div>
          {nameEdit ? (
            <div className="flex items-center gap-3">
              <Input
                ref={inputRef}
                defaultValue={entityDetail?.zhName}
                onBlur={(e) => {
                  editEntityName(e.target.value)
                }}
                onPressEnter={(e) => {
                  editEntityName(e.target.value)
                }}
                style={{ width: '160px' }}
              />
              <CheckOutlined onMouseDown={() => editEntityName} style={{ cursor: 'pointer' }} />
              <CloseOutlined onMouseDown={() => setNameEdit(false)} style={{ cursor: 'pointer' }} />
            </div>
          ) : (
            <span>
              {entityDetail?.zhName || '---'}
              <EditOutlined
                className="ml-2"
                onClick={() => {
                  setNameEdit(true)
                }}
              />
            </span>
          )}
        </div>
      )
    },
    {
      key: '2',
      label: '英文标识',
      children: entityDetail?.name || '---'
    }
  ]

  const defaultColumns = [
    {
      title: '词条',
      dataIndex: 'value',
      width: 200,
      render: (text, row, index) => (
        <>
          {row.isEditing ? (
            <Input
              placeholder="输入词条，回车添加"
              ref={(el) => {
                if (el) {
                  inputRefs.current[index] = el // 保存当前输入框的 ref
                }
              }}
              value={row.value}
              onChange={(e) => {
                row.value = e.target.value
                setDictData([...dictData])
              }}
              onBlur={() => handleEditEntry(row, index)}
              onPressEnter={() => handleEditEntry(row, index)}
            />
          ) : (
            <div
              title={text}
              className="editable-cell"
              onClick={() => {
                row.isEditing = true
                setDictData([...dictData])
                setTimeout(() => {
                  inputRefs.current[index]?.focus()
                }, 0)
              }}
            >
              {text}
            </div>
          )}
        </>
      )
    },
    {
      title: '别名',
      dataIndex: 'lemmaArr',
      onCell: (row, rowIndex) => ({
        className: 'lemma-cell',
        onClick: () => handleLemmaEdit(row, rowIndex),
        style: {
          cursor: row.id === 0 ? 'not-allowed' : 'pointer'
        }
      }),
      render: (lemmaArr, row, index) => (
        <div>
          {lemmaArr.map((tag, i) => (
            <Tag key={i} closable onClose={() => handleDelLemma(i, row)}>
              {tag}
            </Tag>
          ))}
          {row.isAddLemma && (
            <Input
              style={{ width: 160 }}
              ref={(el) => {
                if (el) {
                  lemmaInputRefs.current[index] = el // 保存当前输入框的 ref
                }
              }}
              placeholder="输入别名，回车添加"
              value={row.addLemmaName}
              onChange={(e) => {
                row.addLemmaName = e.target.value
                setDictData([...dictData])
              }}
              onBlur={() => handleAddLemma(row, index)}
              onPressEnter={() => handleAddLemma(row, index)}
            />
          )}
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      render: (_, row, index) => (
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDictDelete(row, index)}
        ></Button>
      )
    }
  ]

  const getDetail = () => {
    let url = `/bot/entity/getEntityDetail`
    let params = {
      entityId: entitySelected?.id
    }
    ajax({
      url,
      data: params,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        setEntityDetail(res.data.data)
        let dictList = res.data.data.dictList || []
        if (dictList.length > 0) {
          setDictId(dictList[0]['dictId'])
        }
      }
    })
  }
  const editEntityName = async (nameVal) => {
    const value = nameVal.trim()
    if (!value) {
      message.error('实体名称不能为空')
      setNameEdit(false)
      return
    }
    const isValid = /^[\u4e00-\u9fa5_a-zA-Z0-9.]{1,32}$/.test(value)
    if (!isValid) {
      message.error('名称格式不合法：仅支持中英文、数字、小数点、下划线，且不超过32个字符')
      return false
    }
    if (value === entityDetail?.zhName) {
      setNameEdit(false)
      return
    }
    let url = `/bot/entity/edit`
    let params = {
      entityId: entityDetail?.id,
      value
    }
    let res = await ajax({
      url,
      data: params,
      method: 'post',
      useFormData: true
    })
    if (res.data.code === '0') {
      message.success('名称修改成功')
      setNameEdit(false)
      getDetail()
      refreshList()
    }
  }
  const getDictList = () => {
    let url = `/bot/entity/dict/getData`
    let params = {
      dictId: dictId,
      search: search,
      pageSize: pageSize,
      pageIndex: current
    }
    ajax({
      url,
      data: params,
      method: 'get'
    }).then((res) => {
      if (res.data.code === '0') {
        const list = res.data.data.results.map((item) => ({
          ...item,
          lemmaArr: item.lemma ? item.lemma.split('|') : []
        }))
        setDictData(list)
        setTotal(res.data.data.count)
        setTableLoading(false)
      }
    })
  }

  const addRow = () => {
    if (dictData[0]?.id === 0) {
      if (dictData[0].isEditing) return
      dictData[0].isEditing = true
      setDictData([...dictData])
      setTimeout(() => {
        inputRefs.current[0]?.focus()
      })
      return
    }
    setDictData([
      {
        id: 0,
        value: '',
        lemmaArr: [],
        addLemmaName: '',
        isEditing: true // 新增行默认处于编辑状态
      },
      ...dictData
    ])
    setTimeout(() => {
      if (inputRefs.current[0]) {
        inputRefs.current[0].focus()
      }
    })
  }
  const handleDictDelete = (row, index) => {
    if (row.id === 0) {
      // 如果是新增的行，直接删除
      dictData.splice(index, 1)
      setDictData([...dictData])
      return
    }
    ajax({
      url: '/bot/entity/dict/deleteData',
      data: {
        dictId,
        ids: row.id
      },
      method: 'post',
      useFormData: true
    })
      .then((res) => {
        if (res.data.code === '0') {
          getDictList()
        }
      })
      .catch((e) => {
        getDictList()
      })
  }

  const handleEditEntry = (row, index) => {
    if (!row.value) {
      row.isEditing = false
      setDictData([...dictData])
      return
    }
    if (!regOfEntry.test(row.value)) {
      let msg = "词条名仅支持中英文/数字/空格和._-'%`()*+"
      message.warning(msg)
      return
    }
    if (row.id === 0) {
      //新增词条
      ajax({
        url: '/bot/entity/dict/addData',
        data: {
          dictId,
          value: row.value
        },
        method: 'post',
        useFormData: true
      }).then((res) => {
        if (res.data.code === '0') {
          row.id = res.data.data.id
          row.isEditing = false
          setDictData([...dictData])
        }
      })
    } else {
      // 编辑词条
      row.isEditing = false
      setDictData([...dictData])
      ajax({
        url: '/bot/entity/dict/editData',
        data: {
          dictId,
          id: row.id,
          lemma: row.lemma,
          value: row.value
        },
        method: 'post',
        useFormData: true
      })
        .then((res) => {
          if (res.data.code != '0') {
            getDictList()
          }
        })
        .catch((e) => {
          getDictList()
        })
    }
  }

  const handleDelLemma = (index, row) => {
    row.lemmaArr.splice(index, 1)
    setDictData([...dictData])
    ajax({
      url: '/bot/entity/dict/editData',
      data: {
        dictId,
        id: row.id,
        lemma: row.lemmaArr.join('|'),
        value: row.value
      },
      method: 'post',
      useFormData: true
    })
      .then((res) => {
        if (res.data.code === '0') {
          getDictList()
        }
      })
      .catch((e) => {
        getDictList()
      })
  }

  const handleAddLemma = (row, index) => {
    if (row.id === 0) {
      //新增词条
      row.isAddLemma = false
      row.addLemmaName = ''
      setDictData([...dictData])
      message.warning('请先填写词条名创建词条')
      return
    }
    if (row.addLemmaName && row.addLemmaName.trim()) {
      if (row.addLemmaName.length > 128) {
        message.warning('别名长度不能超过128个字符')
        return
      }
      if (!regOfLemma.test(row.addLemmaName)) {
        let msg = "词条别名仅支持中英文/数字/空格和()._-'%*+="
        return message.warning(msg)
      }
      row.lemmaArr.push(row.addLemmaName)
      row.addLemmaName = ''
      row.isAddLemma = false
      setDictData([...dictData])
      ajax({
        url: '/bot/entity/dict/editData',
        data: {
          dictId,
          id: row.id,
          lemma: row.lemmaArr.join('|'),
          value: row.value
        },
        method: 'post',
        useFormData: true
      })
        .then((res) => {
          if (res.data.code === '0') {
            getDictList()
          }
        })
        .catch((e) => {
          getDictList()
        })
    } else {
      // 如果别名为空，直接取消添加状态
      row.isAddLemma = false
      row.addLemmaName = ''
      setDictData([...dictData])
    }
  }

  const handleLemmaEdit = (row, index) => {
    if (row.id === 0) return
    row.isAddLemma = true
    setDictData([...dictData])
    setTimeout(() => {
      if (lemmaInputRefs.current[index]) {
        lemmaInputRefs.current[index].focus()
      }
    })
  }

  const handleBuildEntity = () => {
    setBuildLoading(true)
    ajax({
      url: '/bot/entity/compile',
      data: {
        entityId: entityDetail?.id
      },
      method: 'post',
      useFormData: true
    })
      .then((res) => {
        if (res.data.code === '0') {
          setBuildLoading(false)
          refreshList()
          message.success('实体构建成功')
        }
      })
      .catch((e) => {
        setBuildLoading(false)
      })
  }

  const downloadExcel = () => {
    window.open('https://aiui-file.cn-bj.ufileos.com/DemoEntity.xlsx', '_self')
  }
  const exportExcel = () => {
    ajax({
      url: '/bot/entity/doExcelExport',
      method: 'post',
      data: {
        name: entityDetail?.zhName,
        dictId
      },
      useFormData: true,
      config: {
        responseType: 'blob'
      }
    }).then((res) => {
      const urlObject = window.URL || window.webkitURL || window
      const contentDisposition = res.headers['content-disposition']
      let fileName
      if (contentDisposition) {
        fileName = window.decodeURI(res.headers['content-disposition'].split('=')[1], 'UTF-8')
      }
      const url = urlObject.createObjectURL(
        new Blob([res.data], { type: 'application/octet-stream' })
      )
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    })
  }
  const batchItems = [
    {
      key: '1',
      label: (
        <a
          onClick={() => {
            setCoverVisible(true)
          }}
        >
          批量覆盖
        </a>
      )
    },
    {
      key: '2',
      label: (
        <UploadButton
          dictId={dictId}
          options={{ color: 'default', type: 0, text: '批量追加' }}
          setLoad={(loading) => setTableLoading(loading)}
          getEntryList={getDictList}
          // setErrInfo={(msg, showDialog) => console.warn('error', msg)}
          close={() => setCoverVisible(false)}
        />
      )
    },
    {
      key: '3',
      label: <a onClick={exportExcel}>导出实体</a>
    },
    {
      key: '4',
      label: <a onClick={downloadExcel}>下载模版</a>
    }
  ]

  useEffect(() => {
    getDetail()
  }, [])

  useEffect(() => {
    if (nameEdit && inputRef.current) {
      inputRef.current.focus()
    }
  }, [nameEdit])

  useEffect(() => {
    if (!dictId) return
    getDictList()
  }, [dictId])

  return (
    <MyModal
      footer={null}
      // title="实体基本信息"
      title={
        <div className="flex justify-between pr-6">
          <span>实体基本信息</span>
          <Space className="relative bottom-[8px]">
            <Tooltip title="实体是指意图中需要提取的关键词，例如：地点、时间等">
              <QuestionCircleOutlined />
            </Tooltip>
            <Button type="primary" onClick={handleBuildEntity} loading={buildLoading}>
              构建实体
            </Button>
          </Space>
        </div>
      }
      open={entityDetailVisible}
      width={860}
      onCancel={() => setEntityDetailVisible(false)}
      destroyOnHidden
    >
      <div>
        <Descriptions title={null} column={2} items={entityItems} />
        <h4 className="my-4 font-semibold text-[15px] border-l-2 border-blue-700 pl-4 border-solid">
          词条管理
        </h4>
        <div className="flex items-center">
          <div>
            <Button type="primary" className="mr-2" onClick={addRow}>
              添加词条
            </Button>
            <Dropdown menu={{ items: batchItems }} placement="top">
              <Button>批量操作</Button>
            </Dropdown>
          </div>
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="搜索词条"
            style={{ width: '400px', marginLeft: 'auto', marginRight: '15px' }}
            suffix={<SearchOutlined onClick={getDictList} style={{ cursor: 'pointer' }} />}
            onPressEnter={getDictList}
          ></Input>
        </div>
        <Table
          rowKey="id"
          rowClassName={() => 'editable-row'}
          dataSource={dictData}
          columns={defaultColumns}
          loading={tableLoading}
          pagination={{
            position: ['bottomRight '],
            current,
            pageSize,
            total,
            hideOnSinglePage: true
          }}
          style={{ margin: '12px 0' }}
        />
      </div>

      <MyModal
        title={
          <span>
            <ExclamationCircleOutlined style={{ color: 'red', marginRight: 8 }} />
            确定批量导入覆盖吗？
          </span>
        }
        open={coverVisible}
        onCancel={() => setCoverVisible(false)}
        footer={null}
      >
        <p>一旦导入成功，技能现有信息将被完全覆盖！</p>
        <div style={{ textAlign: 'right' }}>
          <Button onClick={() => setCoverVisible(false)} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button>
            <UploadButton
              dictId={dictId}
              options={{ color: '#F5222D', type: 1, text: '覆盖' }}
              setLoad={(loading) => setTableLoading(loading)}
              getEntryList={getDictList}
              setErrInfo={(msg, showDialog) => console.warn('error', msg)}
              close={() => setCoverVisible(false)}
            />
          </Button>
        </div>
      </MyModal>
    </MyModal>
  )
}

export default EntityDetail
