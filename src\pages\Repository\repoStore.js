import { create } from 'zustand'
import { getFileType } from './service'

export const useRepoStore = create((set) => ({
  repoId: '',
  setRepoId: (repoId) => set({ repoId }),

  repoInfo: {},
  setRepoInfo: (info) => set({ repoInfo: info }),

  editState: false,
  setEditState: (val) => set({ editState: val }),

  refreshMethod: null, //刷新文件数据的方法
  setRefreshMethod: (method) => set({ refreshMethod: method }),

  // checkMethod: null, //检查采编是否有编辑过的方法
  // setCheckMethod: (method) => set({ checkMethod: method }),

  fileTypes: [],
  getFileTypes: async () => {
    let data = await getFileType()
    let typeData = data.data.data
    set({ fileTypes: typeData })
  }
}))
