import { Button, Space, Form, Input, message, Select, Card, InputNumber } from 'antd'
import {
  CloseCircleOutlined,
  CloseOutlined,
  PlusCircleOutlined,
  CaretRightOutlined,
  CaretDownOutlined
} from '@ant-design/icons'

import { useCallback, useEffect, useRef, useState } from 'react'

const { Option } = Select
import { DATA_TYPES_MAP, METHOD_TYPES_MAP, SOURCE_TYPES_MAP } from '@/utils/constant'

const FormItemRenderer = ({ field, remove, form, path }) => {
  const schema = form.getFieldValue([...path, 'schema'])
  console.log(schema, 'schema')

  const fieldType = form.getFieldValue([...path, 'type']) // 使用 path 获取正确的字段类型

  console.log(form.getFieldValue([...path], '打印看看'))

  const renderFormItem = () => {
    switch (fieldType) {
      case 'array<object>':
      case 'object':
        return (
          <div className="pl-[14px] ">
            <Form.List name={[field.name, 'schema']}>
              {(subFields, subOpt) => {
                add = subOpt.add
                return (
                  <>
                    {subFields.map((subField) => (
                      <Space
                        key={`${field.key}_${subField.key}`} // 确保子项 key 唯一
                        size={4}
                        className="relative"
                      >
                        <FormItemRenderer
                          field={subField}
                          remove={subOpt.remove}
                          form={form}
                          path={[...path, 'schema', subField.name]}
                          closeable={true}
                          hasExpand={true}
                          onNameChange={onNameChange}
                        />
                        {/* <div className="absolute z-10 h-[1px] w-[8px] top-[50%] left-[-8px] bg-[#dcdcdf]"></div> */}
                      </Space>
                    ))}
                  </>
                )
              }}
            </Form.List>
          </div>
        )

      default:
        return null
    }
  }
  let add = null

  const FormItemDom = renderFormItem()
  return (
    <div>
      <Space size={4} align="start">
        <Form.Item name={[field.name, 'name']}>
          <Input placeholder="请输入参数名称"></Input>
        </Form.Item>

        <Form.Item name={[field.name, 'desc']}>
          <Input placeholder="请输入参数描述"></Input>
        </Form.Item>

        <Form.Item name={[field.name, 'type']} initialValue="String">
          <Select option={DATA_TYPES_MAP}></Select>
        </Form.Item>

        <Form.Item name={[field.name, 'method']} initialValue="Body">
          <Select options={METHOD_TYPES_MAP}></Select>
        </Form.Item>

        <Form.Item name={[field.name, 'isRequired']}>
          <Input>
            <Radio></Radio>
          </Input>
        </Form.Item>

        <Form.Item name={[field.name, 'source']}>
          <Select options={SOURCE_TYPES_MAP}></Select>
        </Form.Item>
      </Space>

      {FormItemDom}
    </div>
  )
}

export default FormItemRenderer
