import { useEffect, useState } from 'react'
import { LinkSegmented } from '@/components'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import styles from './style.module.scss'

const workSpace = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [currentTab, setCurrentTab] = useState('智能体')

  useEffect(() => {
    let pathname = location.pathname.toLowerCase()
    if (pathname.includes('repo')) {
      setCurrentTab('知识库')
    } else if (pathname.includes('agent')) {
      setCurrentTab('智能体')
    } else if (pathname.includes('flow')) {
      setCurrentTab('工作流')
    } else if (pathname.includes('intent')) {
      setCurrentTab('意图库')
    } else if (pathname.includes('plugin')) {
      setCurrentTab('插件')
    } else if (pathname.includes('tool')) {
      setCurrentTab('工具')
    } else {
      setCurrentTab('智能体')
    }
  }, [location])
  return (
    <div className="main-container">
      <div className={styles.workHeader}>
        <LinkSegmented
          defaultValue="插件"
          value={currentTab}
          options={['智能体', '插件', '工作流', '知识库', '意图库']}
          onChange={(value) => {
            setCurrentTab(value)
            switch (value) {
              case '智能体':
                navigate('/workspace/agent')
                break
              case '插件':
                navigate('/workspace/plugin')
                break
              case '工作流':
                navigate('/workspace/flow')
                break
              case '知识库':
                navigate('/workspace/repo')
                break
              case '意图库':
                navigate('/workspace/intent')
                break
              case '工具':
                navigate('/workspace/tool')
            }
          }}
        />
      </div>
      <div className={styles.workContent} style={{ marginTop: '10px' }}>
        <Outlet />
      </div>
    </div>
  )
}

export default workSpace
