import { useState, useEffect, useRef } from 'react'
import {
  Space,
  Table,
  Input,
  Button,
  Modal,
  message,
  Checkbox,
  Select,
  Pagination,
  Empty,
  Tooltip,
  Flex
} from 'antd'
import ajax from '@/utils/http'
import styles from './style.module.scss'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { APP_ENV } from '@/utils/constant'
import { useNavigate } from 'react-router-dom'
import { MyModal } from '@/components'
import microAppRouter from '@/hooks/useMicroAppRouter.js'
import CenterLoading from '@/components/CenterLoading'
import { CustomEmpty } from '@/components'

const { Option } = Select
const { Search } = Input
const { baseRouter } = microAppRouter()

const IntentModal = ({ open, onCancel, pluginId }) => {
  const navigate = useNavigate()
  const [loading, setLoading] = useState(false)
  const [intentList, setIntentList] = useState([])

  const rawItems = useRef([])

  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10 // 每页10条数据

  // 计算当前页数据
  const currentData = intentList.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  const gotoConfig = () => {
    //  跳转到智能体列表页
    if (APP_ENV === 'base') {
      navigate(`/workspace/agent`)
    } else if (APP_ENV === 'auto') {
      navigate(`/`)
    }
  }

  const modalTitle = (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <div>关联意图</div>
      {/* <div style={{ marginLeft: '10px', fontSize: '12px', color: '#ababa9' }}>
        <Space size={2}>
          <QuestionCircleOutlined /> <span>意图示例说法需要到智能体配置，构建生效。 </span>
          <Button type="link" size="mini" onClick={gotoConfig}>
            {'前往配置>>'}
          </Button>
        </Space>
      </div> */}
    </div>
  )

  const getIntentList = () => {
    const parentData = window.microApp?.getData()
    setLoading(true)
    const params = {
      pluginId: pluginId,
      pageSize: 9999,
      pageIndex: 1
    }
    ajax({
      url: `/aiui-agent/intent/published/list`,
      data: params,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          let myList = (res.data?.data?.data || [])
            .filter((it) => {
              if (APP_ENV === 'auto') {
                if (it.official === 1) {
                  return true
                } else {
                  if (parentData?.orgCode) {
                    return it.groupName === `auto:${parentData?.orgCode}`
                  } else {
                    return true
                  }
                }
              } else {
                return true
              }
            })
            .map((item) => {
              const version = item.version
                ? item.version
                : item.versionList.find((i) => i.quote)?.version || item?.versionList[0].version
              const versionObj = item.versionList.find((it) => it.version === version)
              return {
                ...item,
                version,
                intentName: versionObj?.intentName,
                intentNameEn: versionObj?.intentNameEn,
                intentDesc: versionObj?.intentDesc,
                corpusCount: versionObj?.corpusCount,
                quote: versionObj?.quote,
                entityCount: versionObj?.entityCount
              }
            })
            .sort((a, b) => {
              if (a.quote === b.quote) {
                return 0 // 如果 quote 值相同，保持原有顺序
              }
              return a.quote ? -1 : 1 // 如果 a.quote 为 true，a 排在前面；否则 b 排在前面
            })
          setIntentList(myList)
          rawItems.current = myList.slice()
          setLoading(false)
        }
      })
      .catch((err) => {})
  }

  // 处理 Checkbox 变化
  const handleCheckboxChange = (intentId, version, val) => {
    console.log(intentId, version, val, '```````````````intentId,version,val')

    ajax({
      url: '/aiui-agent/plugin/intent/quote',
      method: 'post',
      data: {
        pluginId: pluginId,
        intentId: intentId,
        intentVersion: version,
        quoteFlag: val
      }
    })
      .then((res) => {
        setIntentList((prevItems) =>
          prevItems.map((item) =>
            item.intentId === intentId ? { ...item, quote: !item.quote } : item
          )
        )
        message.success('操作成功')
        // getIntentList()
      })
      .catch((err) => {})
  }

  const handleVersionChange = (intentId, selectedVersion) => {
    console.log(intentId, selectedVersion, '版本切换的intentId, selectedVersion')
    setIntentList((prevItems) => {
      return prevItems.map((item) => {
        if (item.intentId !== intentId) return item

        // 找到选中的版本对象
        const versionObj = item.versionList.find((it) => it.version === selectedVersion)
        if (!versionObj) return item

        // 返回更新后的完整数据
        return {
          ...item,
          version: selectedVersion,
          intentName: versionObj?.intentName,
          intentNameEn: versionObj?.intentNameEn,
          intentDesc: versionObj?.intentDesc,
          corpusCount: versionObj?.corpusCount,
          quote: versionObj?.quote,
          entityCount: versionObj.entityCount
          // 其他需要同步的字段...
        }
      })
    })
  }

  // 处理分页变化
  const handlePaginationChange = (page, pageSize) => {
    setTableParams((prev) => ({
      ...prev,
      pagination: { ...prev.pagination, current: page, pageSize }
    }))
  }

  const handleCancel = () => {
    onCancel?.()
  }

  const gotoIntentDetail = (intentId, item) => {
    if (item.isAuthor) {
      if (APP_ENV === 'base') {
        navigate(`/workspace/intent/${intentId}/corpus`)
      } else if (APP_ENV === 'auto') {
        // navigate(`/intent/${intentId}/corpus`)
        // baseRouter.push(`/agent/intent`)
        baseRouter.push(`/agent/intent?id=${intentId}&page=corpus`)
      }
    } else {
      message.warning('暂无权限')
    }
  }

  const onSearch = (value) => {
    const searchItems = rawItems.current.filter(
      (it) =>
        it.versionList[0]?.intentName.toLowerCase().includes(value.toLowerCase()) ||
        it.versionList[0]?.intentNameEn.toLowerCase().includes(value.toLowerCase())
    )
    setCurrentPage(1)
    setIntentList(searchItems)
  }

  const gotoCreate = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/intent')
    } else if (APP_ENV === 'auto') {
      baseRouter.open(`/agent/intent`)
    }
  }

  useEffect(() => {
    if (open && pluginId) {
      console.log('执行了getIntentList')
      getIntentList()
    } else {
      setIntentList([])
    }
  }, [open, pluginId])

  return (
    <div>
      <MyModal
        title={modalTitle}
        open={open}
        onCancel={handleCancel}
        destroyOnHidden
        width={762}
        footer={null}
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <Flex justify={'space-between'} align={'center'}>
          <Button type="link" onClick={gotoCreate} style={{ margin: '8px 0' }}>
            创建意图
          </Button>
          <Search
            placeholder="输入意图中英文名称搜索"
            onSearch={onSearch}
            allowClear
            style={{ width: 300 }}
          />
        </Flex>
        {currentData.length > 0 ? (
          <>
            <div className={styles.container}>
              {currentData.map((item) => (
                <div key={item.intentId} className={styles.itemCard}>
                  <Checkbox
                    checked={item.quote}
                    onChange={(e) =>
                      handleCheckboxChange(item.intentId, item.version, e.target.checked)
                    }
                    className={styles.checkbox}
                  />
                  <div className={styles.content}>
                    <div className={styles.title}>
                      {item.intentName} <span className={styles.subtitle}>{item.intentNameEn}</span>
                    </div>

                    <Tooltip title={item.intentDesc.length > 20 ? item.intentDesc : ''}>
                      <div className={styles.description}>{item.intentDesc}</div>
                    </Tooltip>
                    {/* <div className={styles.description}>{item.intentDesc}</div> */}
                  </div>
                  <Select
                    value={item.version}
                    className={styles.select}
                    style={{ width: 200 }}
                    placeholder="请选择版本"
                    onChange={(val) => handleVersionChange(item.intentId, val)}
                  >
                    {item.versionList.map((it) => {
                      return (
                        <Option value={it.version} key={it.version}>
                          {it.version} <span>{it.remark}</span>
                        </Option>
                      )
                    })}
                  </Select>

                  <Button type="default" className={styles.button}>
                    {item.corpusCount}示例说法
                    {item?.entityCount > 0 ? ` , ${item.entityCount}实体` : ''}
                  </Button>
                  <Button type="link" onClick={() => gotoIntentDetail(item.intentId, item)}>
                    修改
                  </Button>
                </div>
              ))}
            </div>

            {/* 分页器 */}
            {intentList.length > pageSize && (
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={intentList.length}
                onChange={(page) => setCurrentPage(page)}
                style={{ marginTop: 16, textAlign: 'center' }}
                align="end"
              />
            )}
          </>
        ) : loading ? (
          <CenterLoading height={300} />
        ) : (
          <CustomEmpty />
        )}
      </MyModal>
    </div>
  )
}

export default IntentModal
