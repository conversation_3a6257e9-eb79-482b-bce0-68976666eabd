.agentContent {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 让整个布局占据视口高度 */
  .agentHeader {
    margin: 20px 0 16px 0;
    display: flex;
    .headerLeft {
      color: #333;
      font-size: 22px;
      margin-right: auto;
    }
    .headerRight {
      display: flex;
      align-items: center;
      gap: 13px;
    }
  }
  .agentList {
    flex: 1; /* 占据剩余空间 */
    overflow-y: auto; /* 内容超出时滚动 */
    padding-bottom: 60px;
  }

  .footer {
    position: sticky;
    bottom: 0px;
  }
}

// 容器样式
.container {
}

.search {
  width: 200px;
  margin-bottom: 16px;
}

// 卡片样式
.itemCard {
  display: flex;
  align-items: center;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 8px;
  background: linear-gradient(180deg, #ffffff, #fafafa 100%);
  border: 1px solid #d1d5db;
  cursor: pointer;
  &:hover {
    box-shadow:
      0px 2px 4px -1px rgba(0, 0, 0, 0.06),
      0px 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

// 复选框
.checkbox {
  margin-right: 12px;
}

// 内容部分
.content {
  flex: 1;
}

// 标题
.title {
  font-size: 15px;
  font-weight: 500;
  color: #111827;
  line-height: 18px;
}

// 副标题
.subtitle {
  margin-left: 6px;
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  line-height: 15px;
  margin-top: 4px;
}

// 描述文本
.description {
  margin-top: 4px;
  font-size: 13px;
  font-weight: 400;
  color: #6b7280;
  line-height: 15px;
}

// 版本选择框
.select {
  width: 80px;
  margin-right: 12px;
}

// 按钮
.button {
  white-space: nowrap;
}
