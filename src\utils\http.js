import Axios from 'axios'
import { message } from 'antd'
import handleLoginExpire from '@/utils/handleLoginExpire'

if (window.microApp?.getData()) {
  window.__DATA_FROM_PARENT__ = window.microApp?.getData()
  console.log('--------data come from parent-----------', window.window.microApp?.getData())
}

const BASEURL = import.meta.env.VITE_API_BASE_URL || '/iflycloud/api'

const timeout = 60000
Axios.defaults.timeout = timeout
Axios.defaults.validateStatus = function (status) {
  return status >= 200 && status < 400 //默认
}

let headers = {}
if (window.__DATA_FROM_PARENT__) {
  headers['X-Auto-Token'] = window.__DATA_FROM_PARENT__.cookie
  // headers['X-Auto-Token'] = 'testcookie'
}

let axios = Axios.create({
  headers,
  baseURL: BASEURL,
  validateStatus: function (status) {
    return status >= 200 && status < 400 //默认
  },
  withCredentials: !window.__MICRO_APP_ENVIRONMENT__ // 微前端环境写不携带cookie
})

axios.interceptors.request.use(
  function (config) {
    if (config.params) {
      config.params.ts = new Date().getTime()
    } else {
      config.params = {
        ts: new Date().getTime()
      }
    }
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)

axios.interceptors.response.use(
  (response) => {
    let data = response.data
    if (
      (data.code != undefined && (data.code == -3 || data.code == 100)) ||
      response.status === 401
    ) {
      handleLoginExpire()
    }
    return response
  },
  (error) => {
    console.log(error, '--------------拦截器中的error')
    if (error.status === 401 || error.response.status === 401 || error.response.data.code === 100) {
      handleLoginExpire()
    }
    return Promise.reject(error)
  }
)

const http = ({
  baseURL,
  url,
  data = {},
  method = 'get',
  timeout = 80000,
  useFormData = false,
  config
}) => {
  method = method.toLowerCase()
  let option = {
    url,
    method,
    headers: {
      'Content-Type': 'application/json'
    },
    timeout, // 默认超时时间
    baseURL: baseURL || BASEURL
  }
  if (useFormData) {
    const formData = new FormData()
    for (const key in data) {
      formData.append(key, data[key])
    }
    option.data = formData
    option.headers['Content-Type'] = 'multipart/form-data'
  } else {
    option[method === 'get' ? 'params' : 'data'] = data
    // if (method === 'get') {
    //   option.headers['Content-Type'] = 'application/x-www-form-urlencoded'
    // }
  }
  if (config?.responseType) {
    option.responseType = config.responseType
  }

  option = Object.assign({}, option, config)
  return new Promise((resolve, reject) => {
    axios(option)
      .then((res) => {
        if (res.status === 200) {
          if (
            res.data.type === 'application/octet-stream' ||
            res.data.type === 'application/vnd.ms-excel'
          ) {
            // console.log('封装res',res.data);
            return resolve(res)
          }

          if (Number(res.data.code) === 0 || Number(res.data.code) === 999) {
            return resolve(res)
          } else if (Number(res.data.code) === -3) {
            message.error(res.data.desc)
            return reject(res)
          } else {
            message.error(res.data.desc)
            return reject(res)
          }
        } else {
          reject(res)
        }
      })
      .catch((error) => {
        console.log('网络异常，请重试...')
        reject(error)
      })
  })
}

const request = ({
  baseURL,
  url,
  data = {},
  method = 'get',
  timeout = 80000,
  config,
  useFormData = false
}) => {
  return http({ baseURL, url, data, method, timeout, config, useFormData })
}

export { axios }

export default request
