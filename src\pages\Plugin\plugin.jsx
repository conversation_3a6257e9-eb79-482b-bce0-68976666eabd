import { useState, useEffect, useRef } from 'react'
import { LinkSegmented } from '@/components'
import { Outlet, useNavigate } from 'react-router-dom'
import {
  Input,
  Space,
  Button,
  Pagination,
  App,
  Modal,
  message,
  Form,
  Alert,
  Tooltip,
  Popconfirm,
  Tag
} from 'antd'
import IconEdit from '@/assets/svgs/icon-edit.svg?react'
import IconCopy from '@/assets/svgs/icon-copy.svg?react'
import IconContect from '@/assets/svgs/icon-contect.svg?react'
import IconDelete from '@/assets/svgs/icon-delete.svg?react'
import IconDeleteRed from '@/assets/svgs/icon-delete-red.svg?react'
import { MyModal } from '@/components'

import {
  SearchOutlined,
  ExclamationCircleOutlined,
  CopyOutlined,
  TagOutlined
} from '@ant-design/icons'
import styles from './style.module.scss'
// import Card from './Card.jsx'
import PluginModal from './PluginModal.jsx'
import ajax from '@/utils/http'
import { APP_ENV } from '@/utils/constant'
import CardList from '@/components/CardList'
import IntentModal from './IntentModal'

import { modalEvents } from '@/utils/eventSystem.js'
import microAppRouter from '@/hooks/useMicroAppRouter.js'

const PluginPage = (props) => {
  const { baseRouter } = microAppRouter()
  const title = APP_ENV === 'base' ? '插件' : '智能体'

  const [modal, contextHolder] = Modal.useModal()
  const navigate = useNavigate()

  const [messageApi] = message.useMessage()

  const [searchVal, setSearchVal] = useState('')

  const [pluginList, setPluginList] = useState([])

  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(9)
  const [total, setTotal] = useState(0)
  const [modalVisible, setModalVisible] = useState(false)
  const [pluginInfo, setPluginInfo] = useState(null)

  const [copyModalVisible, setCopyModalVisible] = useState(false)
  const [copyForm] = Form.useForm()
  const [currenId, setCurrentId] = useState(false)

  const [visible, setVisible] = useState(false)
  const currentPlugin = useRef(null)
  const cardListRef = useRef(null) // 用于获取 CardList 的 ref

  const goCreatePlugin = () => {
    setModalVisible(true)
  }

  const goEditPlugin = (pluginInfo) => {
    setPluginInfo(pluginInfo)
    setModalVisible(true)
  }

  const handleCloseModal = () => {
    setPluginInfo({})
    setModalVisible(false)
  }

  // 处理表单提交
  const handleFormSubmit = (formData, pluginId) => {
    const data = pluginId
      ? {
          pluginName: formData.pluginName,
          pluginDesc: formData.pluginDesc,
          pluginType: APP_ENV === 'base' ? formData?.pluginType : 0,
          pluginId: pluginId
        }
      : {
          pluginName: formData.pluginName,
          pluginDesc: formData.pluginDesc,
          pluginType: APP_ENV === 'base' ? formData?.pluginType : 0
        }
    if (pluginId) {
      editPlugin(data)
    } else {
      createPlugin(data, formData.pluginName)
    }
    // setModalVisible(false)
  }

  const createPlugin = (data, pluginName) => {
    const url = `/aiui-agent/plugin/create`
    ajax({
      url,
      data,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('创建成功')
          setModalVisible(false)
          cardListRef?.current.reload()

          if (APP_ENV === 'base') {
            navigate(`/workspace/plugin/${res.data.data.pluginId}/detail`)
          } else if (APP_ENV === 'auto') {
            // navigate(`/plugin/${res.data.data.pluginId}/detail`)
            baseRouter.push(`/agent/${res.data.data.pluginId}/detail?agentName=${pluginName}`)
          }
        }
      })
      .catch((err) => {})
  }

  const doDeletePlugin = (id, pluginType) => {
    const url = `/aiui-agent/plugin/delete`
    const data = {
      pluginId: id
    }
    ajax({
      url,
      data,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          console.log(pluginType, '删除的pluginType')
          message.success('操作成功')
          // 汽车模版删除的时候，传pluginId通知下
          if (APP_ENV === 'auto' && pluginType === 0) {
            window.microApp?.dispatch({
              pluginId: id,
              type: 'AGENT_DELETE'
            })
          }
          cardListRef?.current.reload()
        }
      })
      .catch((err) => {})
  }

  const copyPlugin = (id, name) => {
    setCurrentId(id)
    if (name && name.length < 29) {
      const data = {
        pluginId: id
      }
      const url = `/aiui-agent/plugin/copy`
      ajax({
        url,
        data,
        method: 'post'
      })
        .then((res) => {
          if (res.data.code === '0') {
            message.success('操作成功')

            cardListRef?.current.reload()

            copyModalClose()
          }
        })
        .catch((err) => {})
    } else if (name.length >= 29) {
      setCopyModalVisible(true)
    }
  }

  const handleCopyOk = () => {
    copyForm.validateFields().then((values) => {
      const data = {
        pluginId: currenId,
        pluginName: values.copyName
      }
      const url = `/aiui-agent/plugin/copy`
      ajax({
        url,
        data,
        method: 'post'
      })
        .then((res) => {
          if (res.data.code === '0') {
            message.success('操作成功')
            copyModalClose()

            cardListRef?.current.reload()
          }
        })
        .catch((err) => {})
    })
  }

  const copyModalClose = () => {
    setCurrentId(null)
    setCopyModalVisible(false)
    copyForm.resetFields()
  }

  const editPlugin = (data) => {
    const url = `/aiui-agent/plugin/edit`
    ajax({
      url,
      data,
      method: 'post'
    })
      .then((res) => {
        if (res.data.code === '0') {
          message.success('操作成功')

          setModalVisible(false)

          cardListRef?.current.reload()
          setPluginInfo(null)
        }
      })
      .catch((err) => {})
  }

  const [isHovered, setIsHovered] = useState(false)

  const optItems = [
    {
      key: 'edit',
      label: (
        <Space>
          <IconEdit />
          <div>编辑</div>
        </Space>
      )
    },
    {
      key: 'relate-intent',
      label: (
        <Space>
          <IconContect />
          <div>关联意图</div>
        </Space>
      )
    },

    {
      key: 'copy',
      label: (
        <Space>
          <IconCopy />
          <div>{APP_ENV === 'auto' ? '复制' : '复刻'}</div>
        </Space>
      )
    },

    {
      key: 'delete',
      label: (
        <Space
          className={'delete-menu-item'}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <Popconfirm
            title={`删除${APP_ENV === 'base' ? '插件' : '智能体'}`}
            description={`确认删除该 ${APP_ENV === 'base' ? '插件' : '智能体'}？`}
            onConfirm={(e) =>
              doDeletePlugin(currentPlugin.current?.pluginId, currentPlugin.current?.pluginType)
            }
            okText="是"
            okButtonProps={{ loading: false }}
            cancelText="否"
            onPopupClick={(e) => e.stopPropagation()}
          >
            <Space>
              {isHovered ? <IconDeleteRed /> : <IconDelete />}
              <div
                style={{
                  color: isHovered ? '#f15858' : '#374151'
                }}
              >
                删除
              </div>
            </Space>
          </Popconfirm>
        </Space>
      ),
      style: { backgroundColor: isHovered ? '#fee2e2' : '#fff' }
    }
  ]

  const onDropItemsClick = (e, l) => {
    const key = e.key
    e.domEvent.stopPropagation()
    switch (key) {
      case 'edit':
        goEditPlugin(l)
        break

      case 'relate-intent':
        // onRelateIntentClick(l)
        currentPlugin.current = l
        setVisible(true)
        break
      case 'copy':
        copyPlugin(l.pluginId, l.pluginName)
        break
      case 'delete':
        currentPlugin.current = l
        break
    }
  }

  const doDelete = () => {
    handleDelete(cardData.pluginId)
  }

  const doCopy = () => {
    handleCopy(cardData.pluginId, cardData.pluginName)
  }

  const doEdit = () => {
    handleEdit(cardData)
  }
  const connectIntent = () => {
    setVisible(true)
  }

  const onIntentModalCancel = () => {
    setVisible(false)
  }

  const gotoDetail = (cardData) => {
    if (APP_ENV === 'base') {
      navigate(`/workspace/plugin/${cardData.pluginId}/detail`)
    } else if (APP_ENV === 'auto') {
      if (cardData.pluginType === 0) {
        baseRouter.push(`/agent/${cardData.pluginId}/detail?agentName=${cardData.pluginName}`)
        // civi的详情跳转  汽车模版是跳转他们写的页面
        // navigate(`/plugin/${cardData.pluginId}/detail`)
      } else {
        navigate(`/plugin/${cardData.pluginId}/detail`)
      }
    }
  }

  useEffect(() => {
    const unsubscribe = modalEvents.listen('OPEN_PLUGIN', () => {
      setModalVisible(true)
    })
    return () => {
      unsubscribe() // 使用返回的清理函数
      // 或者直接使用：modalEvents.unlisten('OPEN_B_MODAL', handler);
    }
  }, [])

  const renderFooterTag = (cardData) => {
    if (APP_ENV === 'base') {
      return (
        <div>
          {cardData.pluginType === 0 && (
            <Tag color="blue" icon={<TagOutlined />}>
              汽车模版
            </Tag>
          )}

          {cardData.pluginType === 1 && (
            <Tag color="green" icon={<TagOutlined />}>
              OpenAPI协议
            </Tag>
          )}

          {cardData.pluginType === 3 &&
            JSON.parse(cardData?.extraParam || '{}')?.coze_template?.type === 'workflow' && (
              <Tag color="purple" icon={<TagOutlined />}>
                coze工作流
              </Tag>
            )}

          {cardData.pluginType === 3 &&
            JSON.parse(cardData?.extraParam || '{}')?.coze_template?.type === 'bot' && (
              <Tag color="purple" icon={<TagOutlined />}>
                coze智能体
              </Tag>
            )}

          {cardData.pluginType === 4 &&
            JSON.parse(cardData?.extraParam || '{}')?.maas_template?.type === 'workflow' && (
              <Tag color="purple" icon={<TagOutlined />}>
                星辰工作流
              </Tag>
            )}

          {cardData.pluginType === 4 &&
            JSON.parse(cardData?.extraParam || '{}')?.maas_template?.type === 'bot' && (
              <Tag color="purple" icon={<TagOutlined />}>
                星辰智能体
              </Tag>
            )}

          {/* {cardData.pluginType === 3 && !cardData?.extraParam && (
            <Tag color="cyan" icon={<TagOutlined />}>
              coze协议
            </Tag>
          )}
  
          {cardData.pluginType === 4 && !cardData?.extraParam && (
            <Tag color="cyan" icon={<TagOutlined />}>
              星辰协议
            </Tag>
          )} */}
        </div>
      )
    } else {
      return (
        <div>
          {cardData?.orgName ? (
            <Tag color="orange" icon={<TagOutlined />}>
              {cardData?.orgName}
            </Tag>
          ) : (
            <div>
              {cardData.pluginType === 0 && (
                <Tag color="blue" icon={<TagOutlined />}>
                  汽车模版
                </Tag>
              )}

              {cardData.pluginType === 1 && (
                <Tag color="green" icon={<TagOutlined />}>
                  OpenAPI协议
                </Tag>
              )}

              {cardData.pluginType === 3 &&
                JSON.parse(cardData?.extraParam || '{}')?.coze_template?.type === 'workflow' && (
                  <Tag color="purple" icon={<TagOutlined />}>
                    coze工作流
                  </Tag>
                )}

              {cardData.pluginType === 3 &&
                JSON.parse(cardData?.extraParam || '{}')?.coze_template?.type === 'bot' && (
                  <Tag color="purple" icon={<TagOutlined />}>
                    coze智能体
                  </Tag>
                )}

              {cardData.pluginType === 4 &&
                JSON.parse(cardData?.extraParam || '{}')?.maas_template?.type === 'workflow' && (
                  <Tag color="purple" icon={<TagOutlined />}>
                    星辰工作流
                  </Tag>
                )}

              {cardData.pluginType === 4 &&
                JSON.parse(cardData?.extraParam || '{}')?.maas_template?.type === 'bot' && (
                  <Tag color="purple" icon={<TagOutlined />}>
                    星辰智能体
                  </Tag>
                )}

              {/* {cardData.pluginType === 3 && !cardData?.extraParam && (
              <Tag color="cyan" icon={<TagOutlined />}>
                coze协议
              </Tag>
            )}
    
            {cardData.pluginType === 4 && !cardData?.extraParam && (
              <Tag color="cyan" icon={<TagOutlined />}>
                星辰协议
              </Tag>
            )} */}
            </div>
          )}
        </div>
      )
    }
  }

  // end----------------

  return (
    <App>
      <CardList
        title={title}
        ref={cardListRef}
        searchConfig={{
          url: '/aiui-agent/plugin/list',
          method: 'post',
          searchKey: 'searchKey',
          pagination: {
            page: 'pageIndex',
            pageSize: 'pageSize'
          },
          dataFormatter: (data) => {
            return {
              list: data?.data?.data || [],
              total: data?.data?.totalSize
            }
          },
          extraParams: {
            pluginTypes: APP_ENV === 'auto' ? [0, 3, 4] : [] //汽车环境 把openApi从插件里拆分，
          }
        }}
        cardConfig={{
          title: 'pluginName',
          description: 'pluginDesc',
          updateTime: 'updateTime',
          id: 'pluginId'
        }}
        dropdown={{
          optItems,
          onDropItemsClick
        }}
        renderFooterTag={renderFooterTag}
        events={{
          onAddClick: goCreatePlugin,
          onCardClick: gotoDetail
        }}
      />

      <PluginModal
        visible={modalVisible}
        onClose={handleCloseModal}
        onSubmit={handleFormSubmit}
        pluginInfo={pluginInfo}
      ></PluginModal>

      <MyModal
        title={`创建${APP_ENV === 'base' ? '插件' : '智能体'}副本`}
        open={copyModalVisible}
        onCancel={copyModalClose}
        onClose={copyModalClose}
        onOk={handleCopyOk}
      >
        <span>
          {`*保存成功将复刻一个当前${APP_ENV === 'base' ? '插件' : '智能体'}，支持单独编辑使用`}{' '}
        </span>
        <Alert
          message="副本名称超过字数限制，请重新命名"
          type="warning"
          size="small"
          style={{ marginBottom: 10 }}
        />
        <Form form={copyForm} name="basic" layout="vertical">
          <Form.Item
            name="copyName"
            label={APP_ENV === 'base' ? '插件名称' : '智能体名称'}
            rules={[
              { required: true, message: '请输入名称', trigger: 'blur' },
              {
                pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
                message:
                  '名称只能包含中文、英文字母、数字、小数点、短横线和下划线,长度不超过32个字符',
                trigger: 'blur'
              }
            ]}
          >
            <Input placeholder="支持中英文/数字/小数点、短横线、下划线，不超过32个字符"></Input>
          </Form.Item>
        </Form>
      </MyModal>

      <IntentModal
        open={visible}
        pluginId={currentPlugin?.current?.pluginId}
        onCancel={onIntentModalCancel}
      />
      {contextHolder}
    </App>
  )
}

export default PluginPage
