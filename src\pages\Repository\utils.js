export const identifierOptions = [
  {
    label: '空两格',
    value: '  '
  },
  {
    label: '空格',
    value: ' '
  },
  // {
  //   label: '换行',
  //   value: '\n'
  // },
  // {
  //   label: '连续换行',
  //   value: '\n\n'
  // },
  {
    label: '中文逗号',
    value: '，'
  },
  {
    label: '英文逗号',
    value: ','
  },
  {
    label: '中文感叹号',
    value: '！'
  },
  {
    label: '英文感叹号',
    value: '!'
  },
  {
    label: '中文句号',
    value: '。'
  },
  {
    label: '英文句号',
    value: '.'
  },
  {
    label: '中文问号',
    value: '？'
  },
  {
    label: '英文问号',
    value: '?'
  },
  {
    label: '制表符',
    value: '\t'
  }
]

export const getDisplayStatus = (record) => {
  const status = record.status
  const extractStatus = record.extractStatus
  const buildStatus = record.buildStatus
  let displayStatus
  if (status === 1) {
    if (record.isAble === 1) {
      // 发布成功
      displayStatus = 1
    } else {
      // 已失效
      displayStatus = 2
    }
  } else {
    if (extractStatus == 0 || extractStatus == 2) {
      // 解析中
      displayStatus = 3
    } else if (extractStatus == 3) {
      // 解析失败
      displayStatus = 4
    } else {
      if (buildStatus == 0) {
        // 待发布
        displayStatus = 5
      }
      if (buildStatus == 2) {
        // 构建中
        displayStatus = 6
      }
      if (buildStatus == 3) {
        // 构建失败
        displayStatus = 7
      }
    }
  }
  return displayStatus
}

export const displayStatusMap = {
  // 解析中 解析失败 待发布 构建中 构建失败 发布成功 已失效（可逗号分隔）
  1: {
    text: '发布成功',
    textStyle: { color: '#05F19A' },
    iconClass: 'success'
  },
  2: {
    text: '已失效',
    textStyle: { color: '#B0B0B0' },
    iconClass: 'progress'
  },
  3: {
    text: '解析中',
    textStyle: { color: '#FAA72A' },
    iconClass: 'progress'
  },
  4: {
    text: '解析失败',
    textStyle: { color: '#fa4e4e' },
    iconClass: 'fail',
    allowRetry: true,
    retryType: 'parse'
  },
  5: {
    text: '待发布', //待构建
    textStyle: { color: '#2174FF' },
    iconClass: 'progress'
  },
  6: {
    text: '构建中',
    textStyle: { color: '#FAA72A' },
    iconClass: 'progress'
  },
  7: {
    text: '构建失败',
    textStyle: { color: '#fa4e4e' },
    iconClass: 'fail',
    allowRetry: true,
    retryType: 'build'
  }
}

export function formatFileSize(bytes) {
  if (bytes === 0 || !bytes || bytes === '') return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
