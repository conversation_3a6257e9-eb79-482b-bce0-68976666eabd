import React, { useEffect, useState } from 'react'
import { Button, Form, Input, Select, Switch, InputNumber, Popover, Checkbox, Divider } from 'antd'
const { Option } = Select

const SceneLocalizationSelector = (props) => {
  const { id, value, onChange, domains, disabled, ...rest } = props
  const [localValue, setLocalValue] = useState(value)

  const handleChange = (newValue) => {
    // 当值为 null 或 undefined 时（用户删除所有内容），设置为最小值 1
    // if (newValue === null || newValue === undefined) {
    //   setVal(1)
    //   onChange && onChange(1)
    // } else {
    //   setVal(newValue)
    //   onChange && onChange(newValue)
    // }
  }

  //   value: [
  //     {
  //         "name": "美食",
  //         "partial": true
  //     }
  // ]

  const onItemChange = (e, item) => {
    if (e.target.checked) {
      const newValue = (localValue || []).concat([{ name: item.label, partial: false }])
      setLocalValue(newValue)
      onChange?.(newValue)
    } else {
      const newValue = (localValue || []).filter((it) => it.name !== item.label)
      setLocalValue(newValue)
      onChange?.(newValue)
    }
  }

  const onItemPartialChange = (e, item) => {
    if (e.target.checked) {
      let newValue = []
      const index = (localValue || []).findIndex((it) => it.name === item.label)
      if (index === -1) {
        newValue = (localValue || []).concat([{ name: item.label, partial: false }])
      } else {
        newValue = (localValue || []).map((it) => {
          return {
            ...it,
            partial: it.name === item.label ? false : it.partial
          }
        })
      }

      setLocalValue(newValue)
      onChange(newValue)
    } else {
      const newValue = (localValue || []).map((it) => {
        return {
          ...it,
          partial: it.name === item.label ? true : it.partial
        }
      })
      setLocalValue(newValue)
      onChange(newValue)
    }
  }
  const content = (
    <div style={{ maxHeight: 400, overflow: 'auto' }}>
      {domains.map((item) => {
        const itemChecked = (localValue || []).find((it) => it.name === item.label)
        const itemPartialChecked = (localValue || []).find(
          (it) => it.name === item.label && it.partial === false
        )
        return (
          <div key={item.id} style={{ padding: '10px 0', borderBottom: '1px solid #eee' }}>
            <Checkbox checked={itemChecked} onChange={(e) => onItemChange(e, item)}>
              {item.label}
            </Checkbox>
            <Checkbox checked={itemPartialChecked} onChange={(e) => onItemPartialChange(e, item)}>
              覆盖全领域
            </Checkbox>
          </div>
        )
      })}
    </div>
  )

  return (
    <span id={id}>
      {/* <InputNumber min={1} max={10} step={1} value={value || val} onChange={handleChange} {...rest} /> */}
      {disabled ? (
        <Button disabled>领域选择</Button>
      ) : (
        <Popover placement="right" title={null} content={content}>
          <Button>领域选择</Button>
        </Popover>
      )}
    </span>
  )
}

export default SceneLocalizationSelector
