.container {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.itemCard {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #d1d5db;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.checkbox {
  margin-right: 12px;
}

.content {
  flex: 1;
}

.title {
  font-size: 14px;
  color: #1f2329;
  display: flex;
  align-items: center;
}

.subtitle {
  color: #6b7280;
  font-size: 12px;
  margin-left: 8px;
}

.selectedPackage {
  margin-top: 4px;
  font-size: 12px;
  color: #1677ff;
}

.configTable {
  width: 100%;
  border-collapse: collapse;

  th,
  td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
  }

  th {
    background-color: #fafafa;
    font-weight: 500;
  }

  td {
    vertical-align: middle;
  }

  .description {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
  }
}

.editButton {
  padding: 0;
  height: auto;
  color: #6b7280;
  margin-left: 12px;

  &:hover {
    color: #1677ff;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.editContainer {
  padding: 16px;

  h3 {
    font-size: 16px;
    color: #1f2329;
    margin-bottom: 8px;
  }

  p {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 16px;
  }

  :global {
    .ant-table-wrapper {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 500;
      }

      .ant-table-tbody > tr > td {
        vertical-align: middle;
      }
    }
  }
}
