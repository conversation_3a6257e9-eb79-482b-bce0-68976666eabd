import styles from './card.module.scss'
import { Button } from 'antd'
import rightArrow from 'assets/images/general/right-arrow.png'

const Card = (props) => {
  const tryOut = () => {
    console.log('try out')
  }
  return (
    <div className={styles.card}>
      <div className={styles.cardHeader}>
        <div className={styles.cardIcon}></div>
        <div className={styles.cardTitle}>
          <div className={styles.name}>{props.cardData.name}</div>
          <div className={styles.type}>{'官方'}</div>
        </div>
      </div>
      <div className={styles.dsp}>{props.cardData.description}</div>
      <div className={styles.cardFooter}>
        <Button style={{ marginRight: '8px' }} onClick={tryOut}>
          立即试用
        </Button>
        <Button type="primary">
          应用
          <img src={rightArrow} style={{ width: '18px', height: '18px' }} />
        </Button>
      </div>
    </div>
  )
}

export default Card
