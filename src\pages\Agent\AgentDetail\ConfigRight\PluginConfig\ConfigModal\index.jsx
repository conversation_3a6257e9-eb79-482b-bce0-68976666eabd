import React, { useState, useEffect, useRef } from 'react'
import {
  Modal,
  Table,
  Select,
  Button,
  Checkbox,
  Empty,
  message,
  Tag,
  Tooltip,
  Flex,
  Input,
  Pagination
} from 'antd'
import { useNavigate } from 'react-router-dom'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import { cloneDeep } from 'lodash'
import ajax from '@/utils/http'
import IntentModal from '../../Common/IntentModal'
import { APP_ENV } from '@/utils/constant'
import { TagOutlined } from '@ant-design/icons'
import styles from './style.module.scss'
import IconReactAi from 'assets/svgs/react-ai.svg?react'
import { MyModal } from '@/components'
// import frame from '@/assets/images/frame.png'
import microAppRouter from '@/hooks/useMicroAppRouter.js'

import SceneLocalizationSelector from '@/components/SceneLocalizationSelector'
import CenterLoading from '@/components/CenterLoading'
import { CustomEmpty } from '@/components'

const { Option } = Select
const { Search } = Input

const PLUGINS_MAP = {
  0: '汽车模板',
  1: 'openAPI协议',
  3: 'coze协议',
  4: '星辰协议'
}

const PLUGIN_COLOR_MAP = {
  0: 'blue',
  1: 'green',
  3: 'purple',
  4: 'purple'
}

const NAMES_MAP = {
  auto: '智能体',
  base: '插件',
  aiui: '插件'
}

const AgentPluginConfigModal = ({ visible, onClose, onRefresh }) => {
  const { baseRouter } = microAppRouter()
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)

  const router = useAgentDetailStore((state) => state.router)

  const navigate = useNavigate()

  const originPlugins = useRef([])
  const [domains, setDomains] = useState([])
  const [plugins, setPlugins] = useState([])
  const [bussinessId, setBussinessId] = useState('')
  const [intentModalOpen, setIntentModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 10 // 每页10条数据

  // 多阶
  const originHighOrderPlugins = useRef([])
  const highOrderPluginRef = useRef({})
  const [highOrderPlugins, setHighOrderPlugins] = useState([])
  const [highOrderLoading, setHighOrderLoading] = useState([])
  const [highOrderCurrentPage, setHighOrderCurrentPage] = useState(1)

  // 计算当前页数据
  const currentData = plugins.slice((currentPage - 1) * pageSize, currentPage * pageSize)

  // 高阶插件数据

  const currentHighOrderData = highOrderPlugins.slice(
    (highOrderCurrentPage - 1) * pageSize,
    highOrderCurrentPage * pageSize
  )

  useEffect(() => {
    if (agentDetail?.boxBot?.botId && visible) {
      getPlugins(agentDetail?.boxBot?.botId)
      getDomains(agentDetail?.boxBot?.botId)

      getHighOrderPlugins(agentDetail?.boxBot?.botId)
    } else {
      setPlugins([])
      setCurrentPage(1)

      setHighOrderPlugins([])
      setHighOrderCurrentPage(1)
    }
  }, [visible, agentDetail])

  const getPlugins = async (boxBotId) => {
    const parentData = window.microApp?.getData()

    setLoading(true)
    const res = await ajax({
      url: '/bot/config/getBotAgentPlugins',
      data: {
        botId: boxBotId,
        pageIndex: 1,
        pageSize: 9999
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      const originData = (res.data?.data || [])
        .filter((it) => {
          if (APP_ENV === 'auto') {
            if (it.official === 1) {
              return true
            } else {
              if (parentData?.orgCode) {
                return it.groupName === `auto:${parentData?.orgCode}`
              } else {
                return true
              }
            }
          } else {
            return true
          }
        })
        .map((it) => {
          return {
            ...it,
            domains: it.classifyType === 1 ? it.domains : undefined
          }
        })
      originPlugins.current = cloneDeep(originData)

      setPlugins(
        originData.sort((a, b) => {
          if (a.selected === b.selected) {
            return 0 // 如果 quote 值相同，保持原有顺序
          }
          return a.selected ? -1 : 1 // 如果 a.quote 为 true，a 排在前面；否则 b 排在前面
        })
      )
      setLoading(false)
    }
  }

  const getDomains = async (boxBotId) => {
    const res = await ajax({
      url: '/bot/config/getAllAgentTemplates',
      data: {
        botId: boxBotId
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      setDomains(
        (res.data?.data || []).map((it) => {
          return {
            label: it.agentName,
            value: it.id
          }
        })
      )
    }
  }

  const getHighOrderPlugins = async (boxBotId) => {
    setHighOrderLoading(false)
    try {
      const res = await ajax({
        url: '/bot/config/getMultiPlugins',
        data: {
          botId: boxBotId,
          pageIndex: 1,
          pageSize: 9999
        },
        method: 'get'
      })
      if (res.data.code === '0') {
        console.log(res)
        setHighOrderPlugins(res.data?.data || [])
        originHighOrderPlugins.current = res.data?.data || []
      }
    } catch (e) {
    } finally {
      setHighOrderLoading(false)
    }
  }

  const onClassifyTypeChange = (val, record) => {
    setPlugins((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          classifyType: it.id === record.id ? val : it.classifyType,
          domains: it.id === record.id ? (val === 2 ? undefined : it.domains) : it.domains,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const onDomainChange = (val, record) => {
    setPlugins((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          domain: it.id === record.id ? val : it.domain,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const onDomainsChange = (val, record) => {
    setPlugins((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          domains: it.id === record.id ? val : it.domains,
          changed: it.id === record.id ? true : it.changed
        }
      })
    )
  }

  const handleCheck = (checked, record) => {
    setPlugins((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          selected: it.id === record.id ? checked : it.selected
        }
      })
    )
  }

  const handleHighOrderCheck = (checked, record) => {
    setHighOrderPlugins((plugins) =>
      plugins.map((it) => {
        return {
          ...it,
          selected: it.id === record.id ? checked : it.selected
        }
      })
    )

    // 记录变动数据
    highOrderPluginRef.current[record.pluginId] = checked
  }

  const convertPluginData = (originalData) => {
    return Object.keys(originalData).map((pluginId) => {
      const operation = originalData[pluginId] ? 'open' : 'close'
      return { pluginId, operation }
    })
  }

  const handleSave = async () => {
    let addPlugins = []
    let delPlugins = []
    let updatePlugins = []

    plugins.forEach((f) => {
      const originF = originPlugins.current.find((it) => it.id === f.id)
      if (f.selected && !originF.selected) {
        addPlugins.push({
          pluginId: f.pluginId,
          pluginName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
      if (!f.selected && originF.selected) {
        delPlugins.push({
          configId: f.configId,
          pluginId: f.pluginId,
          pluginName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
      if (f.selected && originF.selected && f.changed) {
        updatePlugins.push({
          configId: f.configId,
          pluginId: f.pluginId,
          pluginName: f.name,
          classifyType: f.classifyType,
          domains: f.domains,
          intents: f.intents
        })
      }
    })

    // 计算
    let multiPlugins = convertPluginData(highOrderPluginRef.current)
    if (
      addPlugins.length === 0 &&
      delPlugins.length === 0 &&
      updatePlugins.length === 0 &&
      multiPlugins.length === 0
    ) {
      return message.warning('无配置更新')
    }

    let params = {
      botId: agentDetail?.boxBot?.botId,
      addPlugins,
      delPlugins,
      updatePlugins,
      multiPlugins
    }
    const res = await ajax({
      url: '/bot/config/saveAgentPluginConfig',
      data: params,
      method: 'post'
    })
    if (res.data.code === '0') {
      message.success('保存成功')
      onRefresh()
      onClose()
    }
  }

  const onRelateClick = (record) => {
    setBussinessId(record.pluginId)
    console.log(record, '关联的record')
    setSelectIntents(record.intents)
    setIntentModalOpen(true)
  }

  const onIntentModalCancel = () => {
    setIntentModalOpen(false)
    setBussinessId('')
  }

  const [selectIntents, setSelectIntents] = useState([])

  const onIntentSubmit = (pluginId, intents) => {
    console.log('intents', intents)
    setPlugins((p) =>
      p.map((it) => {
        return {
          ...it,
          intentCount: it.pluginId === pluginId ? intents.length : it.intentCount,
          intents: it.pluginId === pluginId ? intents : it?.intents || [],
          changed: it.pluginId === pluginId ? true : it.changed
        }
      })
    )

    setIntentModalOpen(false)
  }

  const gotoCreate = () => {
    if (APP_ENV === 'base') {
      navigate('/workspace/plugin')
    } else if (APP_ENV === 'auto') {
      // navigate('/plugin')
      baseRouter.push('/agent/plugin')
    }
  }

  const onSearch = (value) => {
    const searchItems = originPlugins.current.filter((it) =>
      it.pluginName.toLowerCase().includes(value.toLowerCase())
    )
    setCurrentPage(1)
    setPlugins(searchItems)
  }

  return (
    <>
      <MyModal
        title={APP_ENV === 'auto' ? '添加智能体' : '添加插件'}
        visible={visible}
        onCancel={onClose}
        onOk={handleSave}
        width={1000}
        destroyOnHidden
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <div style={{ marginBottom: 10 }}>{`单阶${NAMES_MAP[APP_ENV]}`}</div>
        <Flex justify={'space-between'} align={'center'} style={{ marginBottom: 10 }}>
          <Button
            type="link"
            onClick={gotoCreate}
          >{`创建${APP_ENV === 'base' ? '插件' : '智能体'}`}</Button>

          <Search
            placeholder="输入插件中文名称搜索"
            onSearch={onSearch}
            allowClear
            style={{ width: 300 }}
          />
        </Flex>
        {currentData.length > 0 ? (
          <>
            {currentData.map((item) => (
              <div key={item.id} className={styles.itemCard}>
                <Checkbox
                  checked={item.selected}
                  onChange={(e) => handleCheck(e.target.checked, item)}
                  className={styles.checkbox}
                ></Checkbox>

                <div className={styles.content}>
                  <div className={styles.title}>
                    {item.pluginName}
                    <span className={styles.subtitle}>
                      <Tag color={PLUGIN_COLOR_MAP[item.pluginType]} icon={<TagOutlined />}>
                        {PLUGINS_MAP[item.pluginType]}
                      </Tag>
                    </span>
                  </div>

                  <Tooltip title={item?.pluginDesc?.length > 25 ? item.pluginDesc : ''}>
                    <div className={styles.description}>{item.pluginDesc}</div>
                  </Tooltip>
                </div>

                <Button
                  onClick={() => onRelateClick(item)}
                  className={styles.button}
                  icon={<IconReactAi />}
                >{`${item.intentCount} 个意图`}</Button>

                <Select
                  value={item.classifyType}
                  style={{ width: 100 }}
                  key={item.id}
                  placeholder="请选择"
                  onChange={(val) => onClassifyTypeChange(val, item)}
                  className={styles.select}
                >
                  <Option value={1}>场景</Option>
                  <Option value={2}>三方</Option>
                </Select>

                <SceneLocalizationSelector
                  value={item.domains}
                  domains={domains}
                  onChange={(val) => onDomainsChange(val, item)}
                  disabled={item.classifyType !== 1}
                />
              </div>
            ))}
            {/* 分页器 */}
            {plugins.length > pageSize && (
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={plugins.length}
                onChange={(page) => setCurrentPage(page)}
                style={{ marginTop: 16, textAlign: 'center' }}
                align="end"
              />
            )}
          </>
        ) : loading ? (
          <CenterLoading height={300} />
        ) : (
          <CustomEmpty />
        )}

        {/* 多阶插件部分 */}

        {router?.auth?.multiPlugin === 1 && (
          <>
            <div style={{ marginBottom: 10 }}>{`多阶${NAMES_MAP[APP_ENV]}`}</div>
            {currentHighOrderData.length > 0 ? (
              <>
                {currentHighOrderData.map((item) => (
                  <div key={item.id} className={styles.itemCard}>
                    <Checkbox
                      checked={item.selected}
                      onChange={(e) => handleHighOrderCheck(e.target.checked, item)}
                      className={styles.checkbox}
                    ></Checkbox>

                    <div className={styles.content}>
                      <div className={styles.title}>{item.pluginName}</div>

                      <Tooltip title={item?.pluginDesc?.length > 25 ? item.pluginDesc : ''}>
                        <div className={styles['long-description']}>{item.pluginDesc}</div>
                      </Tooltip>
                    </div>
                  </div>
                ))}
                {/* 分页器 */}
                {highOrderPlugins.length > pageSize && (
                  <Pagination
                    current={highOrderCurrentPage}
                    pageSize={pageSize}
                    total={highOrderPlugins.length}
                    onChange={(page) => setHighOrderCurrentPage(page)}
                    style={{ marginTop: 16, textAlign: 'center' }}
                    align="end"
                  />
                )}
              </>
            ) : highOrderLoading ? (
              <CenterLoading height={300} />
            ) : (
              <CustomEmpty />
            )}
          </>
        )}
      </MyModal>
      <IntentModal
        bussinessType="plugin"
        bussinessId={bussinessId}
        open={intentModalOpen}
        botId={agentDetail?.boxBot?.botId}
        onCancel={onIntentModalCancel}
        onSubmit={onIntentSubmit}
        selectIntents={selectIntents}
      />
    </>
  )
}

export default AgentPluginConfigModal
