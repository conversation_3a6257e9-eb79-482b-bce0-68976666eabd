import { useState, useEffect, useCallback } from 'react'
import { Input, Space, Row, Col, Modal, Form, Card, Radio, App, message } from 'antd'
import ajax from '@/utils/http'
import { APP_ENV, cssVariables } from '@/utils/constant'
import IconPlugin from 'assets/svgs/icon-plugin.svg?react'
import { MyModal } from '@/components'

const PluginModal = ({ visible, onClose, onSubmit, pluginInfo, modalTitle }) => {
  const [title, setTitle] = useState(`创建${APP_ENV === 'base' ? '插件' : '智能体'}`)
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const { TextArea } = Input
  const [pluginId, setPluginId] = useState(null)

  const [pluginTypeList, setpluginTypeList] = useState([])

  const pluginType = Form.useWatch('pluginType', form)

  const handleSubmit = () => {
    setLoading(true)
    form
      .validateFields()
      .then((values) => {
        onSubmit(values, pluginId)
        setLoading(false)
      })
      .catch((err) => {
        setLoading(false)
      })
  }

  const handleCancel = () => {
    onClose({})
    setTitle(`创建${APP_ENV === 'base' ? '插件' : '智能体'}`)
    setPluginId(null)
  }

  useEffect(() => {
    if (pluginInfo?.pluginId) {
      setTitle(`编辑${APP_ENV === 'base' ? '插件' : '智能体'}`)
      form.setFieldsValue({
        pluginName: pluginInfo.pluginName,
        pluginDesc: pluginInfo.pluginDesc,
        pluginType: pluginInfo.pluginType
      })
      setPluginId(pluginInfo.pluginId)
    } else {
      setTitle(`创建${APP_ENV === 'base' ? '插件' : '智能体'}`)
      setPluginId(null)
      form.resetFields()
    }
  }, [visible])

  const getPluginTypeList = () => {
    const url = `/aiui-agent/plugin/type/all`
    ajax({
      method: 'get',
      url
    })
      .then((res) => {
        if (res.data.code === '0') {
          let arr = res.data.data.map((item) => {
            return {
              label: item.name,
              value: item.code
            }
          })
          setpluginTypeList(arr)
        }
      })
      .catch((err) => {
        message.error(err.desc)
      })
  }

  useEffect(() => {
    if (visible) {
      getPluginTypeList()
    }
  }, [visible])

  return (
    <App>
      <div>
        <MyModal
          title={
            APP_ENV === 'base' ? (
              <Space size={4}>
                <IconPlugin /> {title}
              </Space>
            ) : (
              title
            )
          }
          open={visible}
          onCancel={handleCancel}
          onOk={handleSubmit}
          okText={title.includes('编辑') ? '保存' : '创建'}
          width={600}
          confirmLoading={loading}
          destroyOnHidden={true}
        >
          <Form form={form} layout="vertical" style={{ marginBottom: '15px' }}>
            <Form.Item
              name="pluginName"
              label={APP_ENV === 'base' ? '插件名称' : '智能体名称'}
              rules={[
                { required: true, message: '请输入名称', trigger: 'blur' },
                {
                  pattern: /^[\u4e00-\u9fa5a-zA-Z0-9._-]{0,32}$/,
                  message:
                    '名称只能包含中文、英文字母、数字、小数点、短横线和下划线,长度不超过32个字符',
                  trigger: 'blur'
                }
              ]}
            >
              <Input
                placeholder="支持中英文/数字/小数点、短横线、下划线，不超过32个字符"
                maxLength={32}
                showCount
              ></Input>
            </Form.Item>
            <Form.Item
              name="pluginDesc"
              label={APP_ENV === 'base' ? '插件描述' : '智能体描述'}
              rules={[
                { required: true, message: '请输入描述', trigger: 'blur' },
                { max: 250, message: '描述不能超过250个字符', trigger: 'blur' }
              ]}
            >
              <TextArea
                placeholder="请输入描述，不超过250个字符"
                showCount
                maxLength={250}
                allowClear
                style={{
                  height: 120,
                  resize: 'none'
                }}
              ></TextArea>
            </Form.Item>

            {APP_ENV === 'base' && (
              <Form.Item
                name="pluginType"
                label="来源"
                rules={[{ required: true, message: '请选择来源' }]}
              >
                <Radio.Group disabled={title.includes('编辑')} optionType="radio">
                  {pluginTypeList.map((option) => (
                    <Space size="middle" key={option.value}>
                      <Radio.Button
                        value={option.value}
                        style={{
                          width: 200,
                          height: 40,
                          margin: '8px 15px',
                          padding: '8px',
                          lineHeight: '22px',
                          textAlign: 'center',
                          border:
                            pluginType === option.value
                              ? '1px solid var(--sparkos-primary-color)'
                              : '1px solid #d9d9d9',
                          boxShadow:
                            pluginType === option.value
                              ? '0 0 3px rgba(24, 144, 255, 0.5)'
                              : 'none',
                          background: pluginType === option.value ? '#f0faff' : '#fff',
                          borderRadius: '5px'
                        }}
                      >
                        {option.label}
                      </Radio.Button>
                    </Space>
                  ))}
                </Radio.Group>
              </Form.Item>
            )}
          </Form>
        </MyModal>
      </div>
    </App>
  )
}

export default PluginModal
