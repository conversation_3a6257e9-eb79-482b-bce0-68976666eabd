.main {
  height: 100%;
  flex: auto;
  min-width: 0;
  overflow: auto;
}

.intentHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  margin-bottom: 24px;
  .return {
    cursor: pointer;
    width: 32px;
    height: 32px;
    border: 1px solid #e6e7e9;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
  }
  .division {
    color: #afb2c0;
    margin: 0 10px;
  }
  .intentName {
    font-size: 20px;
    color: #111827;
    font-weight: 600;
  }
}
.exampleStatement,
.entity {
  border: 1px solid #e5e5e5;
  border-radius: 16px;
  padding: 16px 25px;
}
.intentList {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  .card {
    width: 370px;
    width: 370px;
    height: 179px;
    background: linear-gradient(180deg, #ffffff, #fafafa 100%);
    border: 1px solid #d1d5db;
    border-radius: 12px;
    padding: 16px 24px;
    display: flex;
    flex-direction: column;

    cursor: pointer;
    &:hover {
      box-shadow:
        0px 2px 4px -1px rgba(0, 0, 0, 0.06),
        0px 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
  }

  .cardName {
    padding-right: 20px;
    height: 21px;
    color: #111827;
    font-size: 15px;
    font-family:
      PingFang SC,
      PingFang SC-500;
    font-weight: 500;
    text-align: LEFT;
    color: #111827;
    line-height: 18px;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .cardMid {
    display: flex;
    .cardId {
      font-size: 12px;
      font-family:
        PingFang SC,
        PingFang SC-400;
      font-weight: 400;
      text-align: LEFT;
      color: #6b7280;
      line-height: 14px;
    }
    .moreIcon {
      margin-left: auto;
    }
  }

  .desc {
    margin-top: 15px;
    margin-bottom: 5px;
    font-size: 13px;
    font-family:
      PingFang SC,
      PingFang SC-400;
    font-weight: 400;
    text-align: LEFT;
    color: #6b7280;
    line-height: 15px;
    word-wrap: break-word;
  }

  .footer {
    margin-top: auto;
    display: flex;
    .time {
      margin-left: auto;
    }
  }
}

:global(.ant-table) {
  .click-row {
    cursor: pointer;
  }
}
.delete-icon {
  cursor: pointer;
  position: relative;
  display: inline-block;
  width: 17px;
  height: 17px;
  transform: translateY(3px);
  .icon-normal {
    display: block;
  }

  .icon-hover {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
  }

  &:hover {
    .icon-normal {
      display: none;
    }

    .icon-hover {
      display: block;
    }
  }
}
