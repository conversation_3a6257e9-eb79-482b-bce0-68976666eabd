import { useState, useEffect } from 'react'
import { Tag, Dropdown, Tooltip, Modal, Pagination, Empty, Button, Space } from 'antd'
import { Link, useNavigate } from 'react-router-dom'
// import styles from './card.module.scss'
import styles from './cardNew.module.scss'
import IconAgent from 'assets/svgs/menu/agent.svg?react'
import { TagOutlined, CopyOutlined } from '@ant-design/icons'
import DateIcon from '@/assets/svgs/date.svg?react'
import MoreIcon from '@/assets/svgs/more2.svg?react'
import IntentModal from '@/pages/Plugin/IntentModal/index.jsx'
import { APP_ENV } from '@/utils/constant'

const CardList = (props) => {
  const { cardData, handleEdit, handleDelete, handleCopy } = props
  const navigate = useNavigate()
  const [visible, setVisible] = useState(false)

  const items = [
    {
      key: '1',
      label: '编辑',
      onClick: (e) => {
        e.domEvent.stopPropagation()
        doEdit()
      }
    },
    {
      key: '2',
      label: '关联意图',
      onClick: (e) => {
        e.domEvent.stopPropagation()
        connectIntent()
      }
    },
    {
      key: '3',
      label: APP_ENV === 'auto' ? '复制' : '复刻',
      onClick: (e) => {
        e.domEvent.stopPropagation()
        doCopy()
      }
    },
    {
      key: '4',
      label: '删除',
      onClick: (e) => {
        e.domEvent.stopPropagation()
        doDelete()
      }
    }
  ]

  const doDelete = () => {
    handleDelete(cardData.pluginId)
  }

  const doCopy = () => {
    handleCopy(cardData.pluginId, cardData.pluginName)
  }

  const doEdit = () => {
    handleEdit(cardData)
  }
  const connectIntent = () => {
    setVisible(true)
  }

  const onIntentModalCancel = () => {
    setVisible(false)
  }

  const gotoDetail = () => {
    if (APP_ENV === 'base') {
      navigate(`/workspace/plugin/${cardData.pluginId}/detail`)
    } else if (APP_ENV === 'auto') {
      navigate(`/plugin/${cardData.pluginId}/detail`)
    }
  }

  useEffect(() => {}, [])

  return (
    <div>
      <div>
        <div className={styles.cardItem} onClick={gotoDetail}>
          <Tooltip title={cardData.pluginName.length > 27 ? cardData.pluginName : null}>
            <div className={styles.cardName}>{cardData.pluginName}</div>
          </Tooltip>
          <div className={styles.cardMid}>
            <div className={styles.cardId}>{cardData.pluginId}</div>
            <div className={styles.moreIcon}>
              <Dropdown menu={{ items }} trigger={['hover']}>
                <a onClick={(e) => e.stopPropagation()}>
                  <MoreIcon />
                </a>
              </Dropdown>
            </div>
          </div>

          <Tooltip title={cardData.pluginDesc.length > 90 ? cardData.pluginDesc : null}>
            <div className={styles.desc}>
              {cardData.pluginDesc.length > 90
                ? `${cardData.pluginDesc.slice(0, 90)} . . . . . . `
                : cardData.pluginDesc}
            </div>
          </Tooltip>

          <div className={styles.footer}>
            <div className={styles.tag}>
              {cardData.pluginType === 0 && (
                <Tag color="blue" icon={<TagOutlined />}>
                  汽车模版
                </Tag>
              )}

              {cardData.pluginType === 1 && (
                <Tag color="green" icon={<TagOutlined />}>
                  OpenAPI协议
                </Tag>
              )}
              {cardData.pluginType === 3 && (
                <Tag color="purple" icon={<TagOutlined />}>
                  coze协议
                </Tag>
              )}
              {cardData.pluginType === 4 && (
                <Tag color="cyan" icon={<TagOutlined />}>
                  星辰协议
                </Tag>
              )}
            </div>

            <div className={styles.updateTime}>
              <Space size={2}>
                <DateIcon /> 更新于:{cardData.updateTime}
              </Space>
            </div>
          </div>
        </div>
      </div>

      {/* <div className={styles.card} onClick={gotoDetail}>
        <div className={styles.pluginTag}>
          {cardData.pluginType === 0 && (
            <Tag color="blue" icon={<TagOutlined />}>
              汽车模版
            </Tag>
          )}

          {cardData.pluginType === 1 && (
            <Tag color="green" icon={<TagOutlined />}>
              OpenAPI协议
            </Tag>
          )}
          {cardData.pluginType === 3 && (
            <Tag color="purple" icon={<TagOutlined />}>
              coze协议
            </Tag>
          )}
          {cardData.pluginType === 4 && (
            <Tag color="cyan" icon={<TagOutlined />}>
              星辰协议
            </Tag>
          )}
        </div>

        <div className={styles.pluginName}>{cardData.pluginName}</div>

        <Tooltip placement="top" title={cardData.pluginDesc}>
          <div className={styles.pluginDesc}>
            {cardData.pluginDesc.length > 90
              ? `${cardData.pluginDesc.slice(0, 90)} . . . . . . `
              : cardData.pluginDesc}
          </div>
        </Tooltip>

        <div className={styles.footer}>
          <div className={styles.updateTime}>
            <DateIcon /> 更新时间： {cardData.updateTime}
          </div>

          <div className={styles.operate}>
            <Dropdown menu={{ items }} trigger={['click']}>
              <a onClick={(e) => e.stopPropagation()}>
                <MoreIcon />
              </a>
            </Dropdown>
          </div>
        </div>
      </div> */}

      {/* 关联意图 */}
      {<IntentModal open={visible} pluginId={cardData.pluginId} onCancel={onIntentModalCancel} />}
    </div>
  )
}

export default CardList
