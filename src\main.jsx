import { Children, StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Router<PERSON>rovider } from 'react-router-dom'
import IconArrowRight from 'assets/svgs/arrow-right.svg?react'
import { ArrowRightOutlined } from '@ant-design/icons'

import zhCN from 'antd/locale/zh_CN'
import { ConfigProvider, message } from 'antd'
import { router } from '@/router'
import './index.css'
import { APP_ENV, cssVariables } from '@/utils/constant'
import microApp from '@micro-zoe/micro-app'
import { CustomEmpty } from '@/components'

message.config({
  getContainer: () => document.querySelector('#root') || document.body
})

// microApp.start({
//   tagName: 'micro-app-rag'
// })

// 如果是子项目，取得cookie信息
if (window.microApp?.getData()) {
  window.__DATA_FROM_PARENT__ = window.microApp?.getData()
  console.log('--------data come from parent-----------', window.microApp?.getData())
}

// 设置 CSS 变量的工具函数
const setCSSVariables = (variables) => {
  Object.entries(variables).forEach(([key, value]) => {
    document.documentElement.style.setProperty(key, value)
  })
}

setCSSVariables(cssVariables[APP_ENV])

// 设置 data-app-env 属性用于 CSS 选择器
document.documentElement.setAttribute('data-app-env', APP_ENV)

const modalStyles = {
  footer: {
    borderTop: '1px solid red'
  }
}

if (APP_ENV === 'auto') {
  import('@/assets/css/antd_overwrite/index.scss')
    .then(() => {
      console.log('antd_overwrite.scss loaded')
    })
    .catch((err) => {
      console.error('Failed to load antd_overwrite.scss', err)
    })
}

createRoot(document.getElementById('root')).render(
  // <StrictMode>
  <ConfigProvider
    locale={zhCN}
    theme={{
      cssVar: { key: 'civi', hashed: false },
      token: {
        // Seed Token，影响范围大
        // colorPrimary: '#31353E', //'#4d53e8'
        colorPrimary: cssVariables[APP_ENV]['--sparkos-primary-color'],
        colorText: '#495464',
        colorTextPlaceholder: '#98a2b2',
        colorBorder: '#DCDCDF',
        colorWarning: 'rgba(255,161,84,1)',
        colorWarningBg: 'rgba(255,188,133,0.2)'
        // borderRadius: 2,

        // 派生变量，影响范围小
        // colorBgContainer: '#f6ffed'
      },
      components: {
        Collapse: {
          // contentPadding: '16px 0',
          // headerPadding: '10px 0 !important'
        },
        Divider: {
          verticalMarginInline: 2
        },
        Button: {
          primaryShadow: '',
          colorPrimary: cssVariables[APP_ENV]['--sparkos-button-color'],
          algorithm: true
        },
        Menu: {
          itemMarginInline: '0',
          itemSelectedBg: 'rgba(59,81,246,0.10)',
          itemSelectedColor: '#4D53E8',
          itemPaddingInline: 12,
          iconMarginInlineEnd: 8,
          groupTitleColor: '#1D1F25',
          groupTitleFontSize: 13,
          itemHeight: 36
        },
        Modal: {
          styles: { modalStyles }
        },
        Table: {
          rowHoverBg: '#fafafa',
          rowSelectedBg: '#fafafa',
          rowSelectedHoverBg: '#f0f0f0'
        }
        // Input: {
        //   colorBorder: '#d7d7d7', // 设置边框颜色为蓝色
        //   hoverBorderColor: '#40a9ff', // 悬停状态颜色
        //   activeBorderColor: 'rgba(0, 157, 255)', // 激活状态颜色
        //   colorTextPlaceholder: 'rgba(0,0,0,0.25)'
        //   // borderRadius: '4px',
        //   // border: '1px solid #e4e4e4'
        // }
      }
    }}
    renderEmpty={() => <CustomEmpty />}
  >
    <RouterProvider router={router} />
  </ConfigProvider>
  // </StrictMode>,
)
