import { Tree, ConfigProvider } from 'antd'

const LinkTree = (props) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Tree: {
            nodeSelectedBg: 'rgba(59,81,246,0.10);',
            nodeSelectedColor: '#4D53E8',
            indentSize: 0,
            titleHeight: 18
          }
        }
      }}
    >
      <Tree {...props} />
    </ConfigProvider>
  )
}

export default LinkTree
