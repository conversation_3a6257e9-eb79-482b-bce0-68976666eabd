import { But<PERSON>, Collapse, Select, Space, Tag } from 'antd'
// import styles from './style.module.scss'
import styles from '../style.module.scss'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import { useState } from 'react'
import { useEffect } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import PluginConfigModal from './ConfigModal'
import ResourceTag from '../Common/Tag'
import { APP_ENV } from '@/utils/constant'
import ResourceTags from '../Common/Tags'
import { values } from 'lodash'

function PluginConfig() {
  const agentDetail = useAgentDetailStore((state) => state.agentDetail)
  const router = useAgentDetailStore((state) => state.router)
  const [totalCount, setTotalCount] = useState(0)
  const [plugins, setPlugins] = useState([])

  const [configModalOpen, setConfigModalOpen] = useState(false)

  useEffect(() => {
    if (agentDetail?.boxBot?.botId) {
      getAllPlugins(agentDetail?.boxBot?.botId)
    }
  }, [agentDetail])

  const onRefresh = () => {
    if (agentDetail?.boxBot?.botId) {
      getAllPlugins(agentDetail?.boxBot?.botId)
    }
  }

  const onChange = (val) => {
    console.log('onChaneg', val)
    setActiveKey(val)
  }

  const [activeKey, setActiveKey] = useState([])

  // useEffect(() => {
  //   if (totalCount > 0) {
  //     setActiveKey(['plugin'])
  //   }
  // }, [totalCount])

  useEffect(() => {
    const displayPlugins = plugins
      .map((it) => {
        return {
          pluginId: it.pluginId,
          isAuthor: it.isAuthor,
          id: it.id,
          name: it.pluginName,
          multi: it.multi
        }
      })
      .filter(router?.auth?.multiPlugin === 1 ? Boolean : (it) => !it.multi)

    if (displayPlugins.length > 0) {
      setActiveKey(['plugin'])
    }
  }, [plugins, router?.auth?.multiPlugin])

  const getAllPlugins = (botId) => {
    Promise.all([getPlugins(botId), getHighOrderPlugins(botId)]).then((values) => {
      let data1 = values[0]
      let data2 = values[1]
      let data = [...data1, ...data2]
      setPlugins((data || []).filter((item) => item.selected))
      setTotalCount((data || []).filter((item) => item.selected).length)
    })
  }

  const getPlugins = async (boxBotId) => {
    return new Promise((resolve, reject) => {
      ajax({
        url: '/bot/config/getBotAgentPlugins',
        data: {
          botId: boxBotId,
          pageIndex: 1,
          pageSize: 1000
        },
        method: 'get'
      }).then((res) => {
        if (res.data.code === '0') {
          resolve(res.data?.data || [])
        }
      })
    })
  }

  const getHighOrderPlugins = async (boxBotId) => {
    return new Promise((resolve, reject) => {
      ajax({
        url: '/bot/config/getMultiPlugins',
        data: {
          botId: boxBotId,
          pageIndex: 1,
          pageSize: 1000
        },
        method: 'get'
      }).then((res) => {
        if (res.data.code === '0') {
          resolve(
            (res.data?.data || []).map((it) => {
              return {
                ...it,
                multi: true
              }
            })
          )
        }
      })
    })
  }

  const onAddClick = () => {
    setConfigModalOpen(true)
  }

  const onConfigModalCancel = () => {
    setConfigModalOpen(false)
  }

  const displayPlugins = plugins
    .map((it) => {
      return {
        pluginId: it.pluginId,
        isAuthor: it.isAuthor,
        id: it.id,
        name: it.pluginName,
        multi: it.multi
      }
    })
    .filter(router?.auth?.multiPlugin === 1 ? Boolean : (it) => !it.multi)

  const items = [
    {
      key: 'plugin',
      label: <div>{APP_ENV === 'auto' ? '智能体' : '插件'}</div>,
      children: <ResourceTags type="plugin" data={displayPlugins}></ResourceTags>,
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>
            {displayPlugins.length > 0 ? (
              <>
                已配置&nbsp;<span>{displayPlugins.length}</span>
              </>
            ) : (
              <>未配置</>
            )}
          </div>
          <Button type="dashed" onClick={onAddClick} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]
  return (
    <>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        collapsible={'icon'}
        ghost
        className=""
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
        onChange={onChange}
      />
      <PluginConfigModal
        visible={configModalOpen}
        onClose={onConfigModalCancel}
        onRefresh={onRefresh}
      />
    </>
  )
}
export default PluginConfig
