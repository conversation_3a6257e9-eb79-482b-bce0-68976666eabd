import { Modal, Form, Input, Button, Checkbox, Radio, Select, Tooltip, InputNumber } from 'antd'
import { useEffect, useState } from 'react'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { identifierOptions } from './utils'
import styles from './config.module.scss'

const SplitConfig = ({ form, parseType, setParseType }) => {
  const strategy = Form.useWatch('strategy', form)
  // const [symbolOptions, setSymbolOptions] = useState(identifierOptions)

  return (
    <div className={styles['split-config']}>
      <div
        className={`${styles['option-split']} ${parseType === 1 ? styles['option-active'] : ''}`}
        onClick={() => setParseType(1)}
      >
        <div className="flex">
          <div className={styles['option-radio']}>
            <div className={styles['option-radio-inner']} />
          </div>
          <div className={styles['option-title']}>
            <p className={styles['title']}>系统分段</p>
            <p className={styles['dsp']}>按文档标题、标点符号进行自动合并拆分，保证语意通畅。</p>
          </div>
        </div>
      </div>

      <div
        className={`${styles['option-split']} ${parseType === 2 ? styles['option-active'] : ''}`}
        onClick={() => setParseType(2)}
      >
        <div>
          <div className={styles['flex']}>
            <div className={styles['option-radio']}>
              <div className={styles['option-radio-inner']} />
            </div>
            <div className={styles['option-title']}>
              <p className={styles['title']}>自定义分段</p>
              <p className={styles['dsp']}>自定义分段规则、分段长度以及预处理规则等参数</p>
            </div>
          </div>
          {parseType === 2 && (
            <>
              <div className={styles['line']} />
              <Form.Item
                // layout="horizontal"
                label="分段策略"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                name="strategy"
                rules={[{ required: true, message: '至少选择一种分段策略' }]}
              >
                <Checkbox.Group>
                  <Checkbox value="title">标题分割</Checkbox>
                  <Checkbox value="separator">标识符分割</Checkbox>
                </Checkbox.Group>
              </Form.Item>
              {strategy && strategy.includes('separator') && (
                <Form.Item
                  // layout="horizontal"
                  colon={false}
                  labelCol={{ span: 6 }}
                  name="symbolType"
                  // rules={[{ required: true, message: '请选择标识符' }]}
                >
                  <Radio.Group>
                    <Radio value="cutOff">截断符</Radio>
                    <Radio value="separator">
                      分隔符(优先分块大小)
                      <Tooltip
                        title={
                          <>
                            标识符分割支持两种指定方式：
                            <br />
                            1. 截断符：遇到必拆，超过最大分段长度内容将被忽略； <br />
                            2. 分隔符：优先合并到最大分块长度，再往前找指定分隔符。
                          </>
                        }
                      >
                        <QuestionCircleOutlined style={{ marginLeft: 16 }} />
                      </Tooltip>
                    </Radio>
                  </Radio.Group>
                </Form.Item>
              )}
              {strategy && strategy.includes('separator') && (
                <Form.Item
                  label="分段标识符"
                  labelCol={{ span: 6 }}
                  wrapperCol={{ span: 18 }}
                  name="identifier"
                  // layout="horizontal"
                >
                  <Select
                    mode="tags"
                    placeholder="支持自定义输入，按回车添加选项"
                    style={{ width: '100%' }}
                  >
                    {identifierOptions.map((item, index) => (
                      <Select.Option key={index} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )}
              <Form.Item
                label="最大分段长度"
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                name="maxLength"
                // layout="horizontal"
                rules={[
                  { required: true, message: '请输入最大分段长度' },
                  {
                    type: 'number',
                    min: 16,
                    max: 1024,
                    message: '最大分段长度需要在 16 到 1024 之间'
                  }
                ]}
              >
                <InputNumber style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default SplitConfig
