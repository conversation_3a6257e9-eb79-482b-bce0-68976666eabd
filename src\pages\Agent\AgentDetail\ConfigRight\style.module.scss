.config-scroll {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding-left: 18px;
  padding-right: 14px;

  &::-webkit-scrollbar {
    width: 2px;
  }

  &::-webkit-scrollbar-track {
    border-radius: 16px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 16px;
  }
}

.config {
  width: 100%;
}

.buttonWrap {
  // width: 68px;
  height: 25px;
  background: #f5f5f9;
  border-radius: 6px;
  line-height: 25px;
  padding: 0 10px;
  font-size: 12px;
  color: #6b7280;
  span {
    color: #374151;
    font-weight: 600;
  }
}

.tagWrap {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.config-wrap {
  padding: 24px;
  border: 1px solid #e5e5e5;
  border-radius: 16px;
  margin-bottom: 14px;
}

.resouce-title {
  color: #111827;
  font-size: 14px;
  line-height: 20px;
  margin-bottom: 14px;
  font-weight: 500;
}

.resource-line {
  background-color: #e8e9eb;
  width: 100%;
  height: 1px;
  margin: 16px 0;
}
