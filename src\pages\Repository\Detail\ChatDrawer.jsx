import { useState, useRef, useEffect } from 'react'
import { Drawer, Input, Avatar, Typography, InputNumber, message } from 'antd'
import {
  DoubleRightOutlined,
  DeleteOutlined,
  SendOutlined,
  RobotFilled,
  UserOutlined
} from '@ant-design/icons'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import styles from './style.module.scss'
const { Paragraph } = Typography

const ChatDrawer = (props) => {
  const { repoId, open, setOpen, threshold, setThreshold } = props
  const [dialogList, setDialogList] = useState([
    { type: 'answer', data: { answer: '你好，我是智能的小飞~' } }
  ])
  const [qsInput, setQsInput] = useState('')
  const [thresholdEdit, setThresholdEdit] = useState(false)
  const [isSubmiting, setIsSubmiting] = useState(false)
  const listRef = useRef(null)
  const dataRef = useRef() //处理setState异步问题
  const inputRef = useRef(null)
  const abortController = new AbortController()

  const onClose = () => {
    setOpen(false)
  }
  const clearChat = () => {
    setDialogList([{ type: 'answer', data: { answer: '你好，我是智能的小飞~' } }])
  }
  const updateDialogData = (currentIndex, currentChat) => {
    setDialogList((prevList) => {
      // 创建新的数组副本（不可变更新）
      const newList = [...prevList]
      // 创建新的对象副本
      newList[currentIndex] = {
        ...newList[currentIndex], // 保留原有属性
        data: {
          ...newList[currentIndex].data, // 保留data中的其他属性
          ...currentChat // 用currentChat覆盖或添加新属性
        }
      }

      return newList
    })
  }
  const handleSend = () => {
    if (isSubmiting) return
    if (!qsInput.trim()) return message.warning('输入问题不能为空')
    let url = `${import.meta.env.VITE_API_BASE_URL || '/iflycloud/api'}/aiuiKnowledge/knowledge/chat`
    let historyAnswerArr = dialogList
      .map((chat) => {
        if (!chat.data.remove) {
          return {
            content: chat.data[chat.type],
            role: chat.type === 'answer' ? 'assistant' : 'user'
          }
        }
      })
      .filter((chat) => chat !== undefined)
    let historyAnswer = JSON.stringify(historyAnswerArr.slice(-10))
    let formData = new FormData()
    formData.append('searchText', qsInput)
    formData.append('threshold', threshold)
    formData.append('repoDocList', JSON.stringify([{ repoId: repoId }]))
    formData.append('historyAnswer', historyAnswer)
    // setDialogList((prev) => [...prev, { type: "question", data: { question: qsInput } }]);
    dataRef.current = [...dialogList, { type: 'question', data: { question: qsInput } }]
    setDialogList(dataRef.current)
    setQsInput('')

    fetchEventSource(url, {
      method: 'POST',
      signal: abortController.signal,
      body: formData,
      headers: { 'X-Auto-Token': window?.__DATA_FROM_PARENT__?.cookie },
      async onopen(response) {
        if (response.ok) {
          // that.addDialog('answer', {
          //   answer: '',
          // })
          // setDialogList((prev) => [...prev, { type: "answer", data: { answer: '' } }]);
          dataRef.current = [...dataRef.current, { type: 'answer', data: { answer: '' } }]
          setDialogList(dataRef.current)
        } else {
        }
      },
      onmessage(event) {
        try {
          const result = JSON.parse(event.data || '{}')
          console.log('------------result----------------', result)
          if (result.code == '300001') {
            location.reload()
          }
          // 处理每一条信息
          const data = result.data
          handleOutput(data, abortController)
        } catch (e) {
          console.log('e结束', e)
          // that.isReplying = false
          // that.submitting = false
          setIsSubmiting(false)
        }
      },
      onerror(error) {
        console.log('EventSource error', error)
        // that.submitting = false
        setIsSubmiting(false)
      }
    })
  }
  const handleOutput = (data, controller) => {
    try {
      // let currentIndex = dialogList.length - 1
      // let currentChat = {...dialogList[currentIndex]['data']}
      let currentIndex = dataRef.current.length - 1
      let currentChat = { ...dataRef.current[currentIndex]['data'] }
      if (data.reference) {
        currentChat.reference = JSON.parse(data.reference)
        let refarr = JSON.parse(data.reference)
        console.log('reference', refarr)
      }
      if (data.refereq) {
        // currentChat.json = JSON.parse(data.refereq)
        currentChat.json = data.refereq
      }
      let text = JSON.parse(data.text)
      console.log('output text', text)
      if (text.payload) {
        let newContent = text.payload.choices.text[0]['content']
        currentChat.answer = currentChat.answer + newContent
        currentChat.sid = text.header.sid
        if (text.header.status === 2) {
          currentChat.showJson = true
          setIsSubmiting(false)
        }
        // this.dialogList[currentIndex]['data'] = currentChat
        // updateDialogData(currentIndex, currentChat)

        dataRef.current[currentIndex]['data'] = currentChat
        setDialogList([...dataRef.current])
        // console.log('dataRef.current1',dataRef.current);
      } else {
        if (text.header.status === 2) {
          // this.submitting = false
          setIsSubmiting(false)
        }
        if (text.sid) {
          currentChat.flag = true
          currentChat.sid = data.data.sid
        } else {
          currentChat.flag = false
        }
        currentChat.answer = text.header.message
        currentChat.remove = true //不计入历史回复
        // this.dialogList[currentIndex]['data'] = currentChat
        // updateDialogData(currentIndex, currentChat)

        dataRef.current[currentIndex]['data'] = currentChat
        // console.log('dataRef.current2',dataRef.current);
        setDialogList([...dataRef.current])
      }
    } catch (e) {
      console.log(e)
      controller.abort()
      setIsSubmiting(false)
    }
  }
  useEffect(() => {
    console.log('========dialogList', dialogList)

    if (listRef.current) {
      listRef.current.scrollTop = listRef.current.scrollHeight
    }
  }, [dialogList])

  useEffect(() => {
    if (thresholdEdit) {
      inputRef.current.focus()
    }
  }, [thresholdEdit])

  const ChatHeader = () => {
    return (
      <div className={styles.chatHeader}>
        <span>知识体验</span>
        <div className={styles.debugThreshold}>
          {!thresholdEdit && <span onClick={() => setThresholdEdit(true)}>阈值：{threshold}</span>}
          {thresholdEdit && (
            <InputNumber
              ref={inputRef}
              value={threshold}
              onChange={(val) => setThreshold(val)}
              onBlur={(e) => setThresholdEdit(false)}
              onPressEnter={(e) => setThresholdEdit(false)}
              size="small"
              min={0}
              max={1}
              step="0.1"
              precision
            />
          )}
        </div>
      </div>
    )
  }

  const ChatFooter = () => {
    return (
      <div className={styles.chatFooter}>
        <div className={styles.msgSend}>
          <div className={styles.clear} onClick={clearChat}>
            <DeleteOutlined style={{ width: '16px', height: '16px' }} />
          </div>
          <Input
            value={qsInput}
            onChange={(e) => setQsInput(e.target.value)}
            placeholder="输入问题，回车体验"
            onPressEnter={handleSend}
            // suffix={<SendOutlined style={{cursor:'pointer'}}/>}
          />
        </div>
        <p className={styles.tip}>内容由AI生成, 无法确保真实准确, 仅供参考。</p>
      </div>
    )
  }
  return (
    <Drawer
      onClose={onClose}
      open={open}
      width={476}
      styles={{
        header: { borderBottom: 'none', padding: '18px 20px' },
        body: { padding: '0 20px' },
        footer: { borderTop: 'none', padding: '0 20px' }
      }}
      closeIcon={<DoubleRightOutlined />}
      title={ChatHeader()}
      maskClosable={false}
      mask={false}
      footer={ChatFooter()}
    >
      <div className={styles.chatContent} ref={listRef}>
        {dialogList.map((item, index) => (
          <div key={index} className={styles.chat}>
            <Avatar
              shape="square"
              icon={item.type === 'answer' ? <RobotFilled /> : <UserOutlined />}
              style={{ width: '32px', minWidth: '32px' }}
            />
            <div className={`${styles.qsText} ${styles[`qsText-${item.type}`]}`}>
              {item.type === 'answer' && <Typography.Text>{item.data.answer}</Typography.Text>}
              {item.type === 'question' && <span>{item.data.question}</span>}
            </div>
          </div>
        ))}
      </div>
    </Drawer>
  )
}

export default ChatDrawer
