import { Button, Collapse, Space, message } from 'antd'
// import styles from './style.module.scss'
import IconArrow from 'assets/svgs/drop-arrow.svg?react'
import { useState, useEffect } from 'react'
import { useAgentDetailStore } from '@/pages/Agent/AgentDetail/store'
import ajax from '@/utils/http'
import ResourceTags from '../Common/Tags'
import styles from '../style.module.scss'
import { APP_ENV } from '@/utils/constant'
import KnowledgeConfigModal from './ConfigModal'

function KnowledgeConfig() {
  const { agentDetail } = useAgentDetailStore()

  const [repoList, setRepoList] = useState([])
  const [totalCount, setTotalCount] = useState(0)

  const [modalVisible, setModalVisible] = useState(false)
  const addRepo = () => {
    setModalVisible(true)
  }

  const items = [
    {
      key: 'knowledge',
      label: <div>知识库</div>,
      children: (
        // <div className={styles.tagWrap}>
        //   {repoList.map((it, index) => {
        //     return <ResourceTag key={it.id} type="knowledge" name={it.name}></ResourceTag>
        //   })}
        // </div>
        <ResourceTags type="knowledge" data={repoList}></ResourceTags>
      ),
      extra: (
        <Space size={4}>
          <div className={styles.buttonWrap}>
            {/* 已配置&nbsp;<span>{totalCount}</span> */}
            {totalCount > 0 ? (
              <>
                已配置&nbsp;<span>{totalCount}</span>
              </>
            ) : (
              <>未配置</>
            )}
          </div>
          <Button type="dashed" onClick={addRepo} size="small">
            +
          </Button>
        </Space>
      )
    }
  ]

  const onConfigModalCancel = () => {
    setModalVisible(false)
  }

  //   详情配置里面的 展示用
  const getRepoList = async (boxBotId) => {
    const res = await ajax({
      url: '/bot/config/getBotRagRepos',
      data: {
        botId: boxBotId,
        pageIndex: 1,
        pageSize: 1000
      },
      method: 'get'
    })
    if (res.data.code === '0') {
      setRepoList(res.data?.data.repos.filter((item) => item.selected)) // 外层已配置知识库 过滤一下
      setTotalCount(res.data?.data.repos.filter((item) => item.selected).length)
    }
  }

  const onRefresh = () => {
    if (agentDetail?.boxBot?.botId) {
      getRepoList(agentDetail?.boxBot?.botId)
    }
  }

  useEffect(() => {
    if (modalVisible) {
      getRepoList(agentDetail?.boxBot?.botId)
    }
  }, [modalVisible])

  useEffect(() => {
    if (agentDetail?.boxBot?.botId) {
      getRepoList(agentDetail?.boxBot?.botId)
    }
  }, [agentDetail])

  const onChange = (val) => {
    console.log('onChaneg', val)
    setActiveKey(val)
  }

  const [activeKey, setActiveKey] = useState([])

  useEffect(() => {
    if (totalCount > 0) {
      setActiveKey(['knowledge'])
    }
  }, [totalCount])

  return (
    <div>
      <Collapse
        items={items}
        activeKey={activeKey}
        size="small"
        collapsible={'icon'}
        ghost
        className=""
        expandIcon={({ isActive }) => (
          <IconArrow
            style={{ transform: isActive ? 'rotate(0deg)' : 'rotate(-90deg)' }}
          ></IconArrow>
        )}
        onChange={onChange}
      />

      <KnowledgeConfigModal
        visible={modalVisible}
        onClose={onConfigModalCancel}
        onRefresh={onRefresh}
      />

      {/* <Modal
        open={modalVisible}
        title="选择知识库"
        onCancel={handleClose}
        onClose={handleClose}
        onOk={handleSubmit}
        width={700}
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            检索方法 ：
            <Select
              value={channel}
              style={{ width: 120, marginRight: '10px' }}
              options={[
                { value: 1, label: '单向量召回' },
                { value: 2, label: '多路召回' }
              ]}
              onChange={changeChannel}
            ></Select>
            使用语义向量召回知识点
          </div>
          <Table dataSource={repoTableData} columns={columns} rowKey="id"></Table>
        </Space>
      </Modal> */}
    </div>
  )
}

export default KnowledgeConfig
