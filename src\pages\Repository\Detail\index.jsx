import { useState, useEffect } from 'react'
import { Outlet, useNavigate, useLocation, useParams } from 'react-router-dom'
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>, message, Modal, App } from 'antd'
import { MailOutlined, ArrowLeftOutlined } from '@ant-design/icons'
import { getRepoInfo, buildRepo } from '../service'
import { useUserStore } from '@/store/user'
import { useRepoStore } from '../repoStore'
import { APP_ENV } from '@/utils/constant.js'
import styles from './style.module.scss'
import ChatDrawer from './ChatDrawer'

const Module = () => {
  const { repoId } = useParams()
  const navigate = useNavigate()
  const location = useLocation()
  const [modal, contextHolder] = Modal.useModal()

  const { setRepoId, repoInfo, setRepoInfo, getFileTypes } = useRepoStore()
  const refreshMethod = useRepoStore((state) => state.refreshMethod)
  const editState = useRepoStore((state) => state.editState)
  const [current, setCurrent] = useState('base-info')
  const [buildLoading, setBuildLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [threshold, setThreshold] = useState(0.1)

  const items = [
    {
      label: '基础信息',
      key: 'base-info'
      // icon: <MailOutlined />
    },
    {
      label: '文档管理',
      key: 'doc-manage'
      // icon: <MailOutlined />
    },
    {
      label: '知识管理',
      key: 'chunk-manage'
      // icon: <MailOutlined />
    }
  ]
  const onClick = (key) => {
    setCurrent(key)
    switch (key) {
      case 'base-info':
        navigate('baseInfo')
        break
      case 'doc-manage':
        navigate('docManage')
        break
      case 'chunk-manage':
        navigate('chunkManage')
        break
    }
  }

  const back = () => {
    if (APP_ENV === 'auto') {
      navigate('/repo')
    } else {
      navigate('/workspace/repo')
    }
  }

  const checkEdit = async () => {
    let pathname = location.pathname.toLowerCase()
    console.log('pathname', pathname)
    if (pathname.includes('chunkmanage')) {
      if (editState) {
        const confirmed = await modal.confirm({
          title: '提示',
          content: '当前文件存在未保存的修改，是否仍要构建',
          okText: '确认',
          cancelText: '取消'
        })
        return confirmed
      } else {
        return true
      }
    } else {
      return true
    }
  }

  const handleBuild = async () => {
    let checkRes = await checkEdit()
    if (!checkRes) return
    setBuildLoading(true)
    try {
      const data = await buildRepo({ repoId: repoId })
      let res = data.data
      if (res.code == 0) {
        setBuildLoading(false)
        message.success('构建成功')
      }
    } catch (err) {
      console.log(err)
      setBuildLoading(false)
    }
    if (refreshMethod) {
      refreshMethod()
    }
  }

  const openChat = () => {
    setOpen(true)
  }

  useEffect(() => {
    useUserStore.setState({ selectedMenus: ['workspace'] })
    setRepoId(repoId)
    getRepoInfo({ repoId }).then((data) => {
      let res = data.data
      setRepoInfo(res.data)
    })
    getFileTypes()
  }, [])

  useEffect(() => {
    // console.log('当前path', location.pathname)
    let pathname = location.pathname
    if (pathname.includes('baseInfo')) {
      setCurrent('base-info')
    } else if (pathname.includes('docManage')) {
      setCurrent('doc-manage')
    } else if (pathname.includes('chunkManage')) {
      setCurrent('chunk-manage')
    }
  }, [location.pathname])
  return (
    <div className="main-container">
      <div className={styles['repoHeader']}>
        <div className={styles['return']} onClick={back}>
          <ArrowLeftOutlined />
        </div>
        我的知识库
        <span className={styles['division']}>/</span>
        <span className={styles['repoName']}>{repoInfo?.name}</span>
      </div>
      <Tabs
        activeKey={current}
        items={items}
        onChange={(key) => onClick(key)}
        tabBarExtraContent={
          <>
            <Button type="primary" loading={buildLoading} onClick={handleBuild}>
              构建
            </Button>
            <Button onClick={openChat} style={{ marginLeft: '12px' }}>
              知识体验
            </Button>
          </>
        }
        indicator={{
          size: (origin) => origin - 12,
          align: 'center'
        }}
      />
      <div style={{ height: 'calc(100% - 94px)', overflow: 'auto' }}>
        <Outlet />
      </div>
      <ChatDrawer
        repoId={repoId}
        open={open}
        threshold={threshold}
        setOpen={setOpen}
        setThreshold={setThreshold}
      />
      {contextHolder}
    </div>
  )
}

export default Module
