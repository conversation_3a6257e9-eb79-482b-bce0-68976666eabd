.layout {
  display: flex;
  justify-content: flex-end;
}
.input {
  float: right;
  width: 300px;
  border-radius: 20;
  margin-bottom: 20px;
}

.list {
  clear: both;
}

.content {
  min-height: 500px;
  height: auto;
  // background-color: #f5f5f5;
}

.label {
  margin: 10px 10px;
  color: #897171;
  display: flex;
  align-items: center;
  .debugBtn {
    margin-left: auto;
  }
  .addICon {
    float: right;
    cursor: pointer;
    font-size: 16px;
  }
}

.jsonShow {
  width: 100%;
  max-height: 300px;
  overflow: auto;
  background: #f0f0f0;
  padding: 8px;
  border-radius: 6px;
  margin-bottom: 12px;
}

.step2Form {
  :global(.ant-form-item-explain-error) {
    font-size: 11px;
  }
}
