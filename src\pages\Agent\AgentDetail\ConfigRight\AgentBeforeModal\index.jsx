import { MyModal } from '@/components'
import { Switch, Space, message } from 'antd'
import PropTypes from 'prop-types'
import { useRef, forwardRef, useState, useEffect } from 'react'
import PostProcess from './PostProcess'
import { useConfigRightResource } from '../hooks/useConfigRight'
import { Spin } from 'antd'

const AgentBeforeModal = forwardRef(({ visible, onClose, type, onRefresh }) => {
  const postProcessRef = useRef(null)
  const [isOpen, setIsOpen] = useState(false)
  // const [loading, setLoading] = useState(false)
  const useKey = { 2: 'before', 3: 'after' }[type] || 'before'
  const { list, loading, fetchResources, setLoading } = useConfigRightResource(useKey, visible)

  useEffect(() => {
    visible && fetchResources()
  }, [visible, fetchResources])

  useEffect(() => {
    onSwitchChange(list.length)
  }, [list])

  const onSwitchChange = (status) => {
    setIsOpen(status)
    postProcessRef?.current?.updateSwitchStatus(status)
  }

  const titleNode = () => {
    const title = type === 2 ? '模型前处理配置' : '模型后处理配置'
    const desc =
      type === 2
        ? '支持业务方API接入,对上行数据进行预处理'
        : '支持业务方API接入,对下行数据进行预处理'

    return (
      <Space className="flex items-center">
        <Switch
          value={isOpen}
          size="small"
          className="relative -top-[2px]"
          onChange={onSwitchChange}
        />
        <span>{title}</span>
        <span className="text-[12px] text-gray-500 font-[500]">{desc}</span>
      </Space>
    )
  }

  const updateSwitchStatus = (status) => {
    setIsOpen(status)
  }

  const onOk = async () => {
    let valid = true
    if (isOpen) valid = await postProcessRef?.current.validate()
    if (!valid) return message.error('所有字段校验通过后再进行保存')
    setLoading(true)
    const res = await postProcessRef?.current.save()
    setLoading(false)
    if (!res) return message.error('保存失败')
    message.success('保存成功')
    onClose()
    onRefresh()
  }

  return (
    <MyModal
      width={800}
      title={titleNode()}
      confirmLoading={loading}
      visible={visible}
      onCancel={onClose}
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <PostProcess
          ref={postProcessRef}
          type={type}
          visible={visible}
          updateSwitchStatus={updateSwitchStatus}
        />
      </Spin>
    </MyModal>
  )
})

AgentBeforeModal.displayName = 'AgentBeforeModal'

AgentBeforeModal.propTypes = {
  visible: PropTypes.bool,
  onClose: PropTypes.func,
  type: PropTypes.number,
  onRefresh: PropTypes.func
}

export default AgentBeforeModal
