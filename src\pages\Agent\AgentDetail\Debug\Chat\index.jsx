import React, { useState, useEffect, useRef } from 'react'
import styles from './style.module.scss'
import Inputing from './Inputing'
import MarkdownIt from 'markdown-it'
import { message, Tooltip, Input } from 'antd'

import IconSend from '@/assets/images/chat-send.png'
import ImgAgent from '@/assets/images/agent.png'

import { fetchEventSource } from '@microsoft/fetch-event-source'
import handleLoginExpire from '@/utils/handleLoginExpire'
import genUUID from '@/utils/genUUID'
import { DeleteOutlined } from '@ant-design/icons'
import IconClear from 'assets/svgs/chat/clear.svg?react'

const md = new MarkdownIt({ breaks: true })

const Chat = ({ flowId }) => {
  const uuid = useRef(genUUID())
  const [inputText, setInputText] = useState('')
  const [chatList, setChatList] = useState([
    // {
    //   people: 'ai',
    //   con: '你好，我是智能的小飞~',
    //   type: 'text',
    //   logId: '',
    //   preset: true
    // }
  ])
  const [isReplying, setIsReplying] = useState(false)

  const chatboxRef = useRef(null)

  const abortController = useRef(new AbortController())

  useEffect(() => {
    return () => {
      stopResponseSSE()
    }
  }, [])

  useEffect(() => {
    scrollMessage()
  }, [chatList])

  const stopResponseSSE = () => {
    abortController.current.abort()
    abortController.current = new AbortController()
  }

  const scrollMessage = () => {
    if (chatboxRef.current) {
      chatboxRef.current.scrollTop = chatboxRef.current.scrollHeight
    }
  }

  const handleInputChange = (e) => {
    setInputText(e.target.value)
  }

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMsgSse()
    }
  }

  const sendMsgSse = () => {
    if (isReplying) {
      message.error('请让机器人回答完问题再进行回复')
      return
    }
    if (!inputText.trim()) {
      message.error('输入文本不能为空')
      return
    }
    if (inputText.length > 1000) {
      message.error('输入文本不能超过1000字符')
      return
    }

    const newChatList = [
      ...chatList,
      { people: 'me', con: inputText, type: 'text' },
      { people: 'ai', type: 'loading' }
    ]
    setChatList(newChatList)
    setIsReplying(true)

    let baseUrl = '/iflycloud/api/user/chat'
    let formData = new FormData()
    formData.append('version', 'vflowtest')
    formData.append('expUid', uuid.current)
    formData.append('query', inputText)
    formData.append('flowId', flowId)
    // flowId

    setInputText('')

    fetchEventSource(baseUrl, {
      method: 'POST',
      openWhenHidden: true,
      body: formData,
      async onopen(response) {
        if (response.ok) {
          console.log('连接了')
          setIsReplying(true)
        } else {
        }
      },

      onmessage(event) {
        try {
          const result = JSON.parse(event.data || '{}')
          // console.log(result)
          // 处理每一条信息
          if (result.code == '300001') {
            handleLoginExpire()
            setIsReplying(false)
          } else if (result.code == '0') {
            const data = result.data
            handleMessage(data)
          } else {
            message.error(result.desc || '未知错误')
            setChatList((chatList) => chatList.filter((it) => it.type !== 'loading'))
            setIsReplying(false)
          }
        } catch (e) {
          console.log(e)
          setIsReplying(false)
        }
      },
      onclose() {
        console.info('断开了')
        setIsReplying(false)
      },
      onerror(err) {
        // console.info('报错了')
        // throw new Error(err)
        console.log(err)
        setIsReplying(false)
        throw err
      },
      signal: abortController.current.signal
    })
  }

  const handleMessage = (data) => {
    console.log(data)
    if (data.finish || data.nlpFinish) {
      setIsReplying(false)
    }

    if (data.type === 'nlp') {
      // console.log(data)
      setChatList((message) => {
        const index = message.findIndex((item) => item.id === data.logId)

        if (index === -1) {
          // 没找到第一次添加回复数据

          const newMessage = {
            id: data.logId,
            people: 'ai',
            con: data.text
          }
          return [...message.filter((it) => it.type !== 'loading'), newMessage]
        } else {
          return message
            .filter((it) => it.type !== 'loading')
            .map((item) => {
              if (item.people !== 'ai') {
                return item
              } else {
                return {
                  ...item,
                  con: item.id === data.logId ? item.con + data.text : item.con
                }
              }
            })
        }
      })
    }
  }

  const cleanHistory = () => {
    // 这里可以清空各节点的debugData

    setChatList(chatList.filter((item) => item.preset))
    uuid.current = genUUID()
    stopResponseSSE()
    setIsReplying(false)
  }

  //   const copyJson = (data) => {
  //     navigator.clipboard.writeText(JSON.stringify(data, null, '    '))
  //   }

  return (
    <div className={`${styles['content-right']}`}>
      {/* <div className={styles.title}>
        <span>智能助手</span>
        <a onClick={cleanHistory}>+ &nbsp;新建聊天</a>
      </div> */}
      {/* <div className={styles['top-guide']}>
        <div className={styles['top-guide-head']}></div>
        <div className={styles['top-guide-body']}>
          <div className={styles['top-guide-title']}>你好，我是你的智能小助手</div>
          <div className={styles['top-guide-desc']}>
            你可以向我询问产品功能或提问"如何使用交互大模型"，也可以问我其他问题，我会的很多哦
          </div>
        </div>
      </div> */}
      <div className={styles['rightChatboxContainer']}>
        <div className={styles['rightChatbox']} ref={chatboxRef}>
          {chatList.map((item, index) => (
            <div key={index} className={`${styles.chatbox} ${styles[item.people]}`}>
              <div className={styles.con}>
                {item.type === 'loading' ? (
                  <>
                    <div className={styles['icon-ai']}></div>
                    <div>
                      <span>正在处理</span>&nbsp;
                      <Inputing />
                    </div>
                  </>
                ) : (
                  <div>
                    {item.people === 'me' ? (
                      <>
                        <div className={styles['icon-me']}></div>
                        {item.con}
                      </>
                    ) : (
                      <>
                        <div className={styles['icon-ai']}></div>
                        <div dangerouslySetInnerHTML={{ __html: md.render(item.con || '') }} />
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        {chatList.length === 0 && (
          <div className={styles['empty-chat']}>
            <img src={ImgAgent} className={styles['agent-img']} />
            <div className={styles['topic']}>我的智能体</div>
            <div className={styles['tip-text']}>嗨, 你好! 我对很多新奇事物都很了解哦。</div>
          </div>
        )}
      </div>
      <div className={styles['control-bar-wrap']}>
        <div className={styles.msgSend}>
          <div className={styles.clear} onClick={cleanHistory}>
            <IconClear style={{ width: '16px', height: '16px' }} />
          </div>
          <Input
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="输入问题，回车体验"
            onPressEnter={sendMsgSse}
            disabled
            suffix={
              <div className={styles['send-suffix']}>
                <div className={styles['line']}></div>
                <img src={IconSend} className={styles['chat-send']} />
              </div>
            }
          />
        </div>
        <p className={styles.tip}>内容由AI生成, 无法确保真实准确, 仅供参考。</p>
      </div>
      {/* {showJson && (
        <div className={styles['debug-json-dialog']}>
          <div className={styles['request-json']}>
            <i
              className={styles['ic-r-copy']}
              onClick={() => copyJson(resJson)}
              title="复制代码"
            ></i>
            <pre className={styles['json-wrap']}>{JSON.stringify(resJson, null, 2)}</pre>
          </div>
        </div>
      )} */}
    </div>
  )
}

export default Chat
