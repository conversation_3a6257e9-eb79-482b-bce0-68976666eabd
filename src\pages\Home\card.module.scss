.card {
  width: 100%;
  height: 172px;
  background: linear-gradient(0deg, #fafafa 0%, #ffffff);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  .cardHeader {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    .cardIcon {
      width: 44px;
      height: 44px;
      border-radius: 8px;
      background: linear-gradient(135deg, #746ee2, #6376d9 100%);
      margin-right: 16px;
    }
    .name {
      color: #111827;
      font-size: 16px;
      font-family:
        PingFang SC,
        PingFang SC-600;
      font-weight: 600;
    }
    .type {
      font-size: 13px;
      font-family:
        PingFang SC,
        PingFang SC-400;
      font-weight: 400;
      color: #6b7280;
    }
  }
  .dsp {
    font-size: 14px;
    font-family:
      PingFang SC,
      PingFang SC-400;
    font-weight: 400;
    color: #4b5563;
    height: 42px;
    line-height: 21px;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .cardFooter {
    text-align: right;
    :global(.ant-btn) {
      height: 28px;
      font-size: 12px;
    }
  }
}
