import ajax from '@/utils/http'
import { APP_ENV } from '@/utils/constant'

export const getRepoList = (params) => {
  return ajax({
    url: `aiuiKnowledge/knowledge/repo/${APP_ENV === 'auto' ? 'autocar' : 'iflycloud'}/getList`,
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const createRepo = (params) => {
  return ajax({
    url: `aiuiKnowledge/knowledge/repo/${APP_ENV === 'auto' ? 'autocar' : 'iflycloud'}/create`,
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const getRepoInfo = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/repo/info',
    data: params,
    method: 'get'
  })
}

export const updateRepo = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/repo/update',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const deleteRepo = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/repo/delete',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const getRepoFolders = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/folder/getRepoFolders',
    data: params,
    method: 'get'
  })
}

export const updateRepoFolders = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/folder/saveRepoFolder',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const updateDocFolder = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/folder/moveDocIntoFolder',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const getDocList = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/doc/repoDocList',
    data: params,
    method: 'get'
  })
}

export const getFileType = () => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/doc/getClassifyDocTypes',
    method: 'get'
  })
}

export const saveDoc = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/doc/saveRepoDoc',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const buildDoc = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/buildDoc',
    method: 'get',
    data: params
  })
}

export const updateParse = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/doc/updateParse',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const delDoc = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/repo/file/delete',
    method: 'get',
    data: params
  })
}

export const changeDoc = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/knowledge/allAble',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const buildRepo = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/buildRepo',
    data: params,
    method: 'post',
    useFormData: true
  })
}

export const downloadDoc = (params) => {
  return ajax({
    url: 'aiuiKnowledge/knowledge/file/download',
    data: params,
    method: 'get',
    config: {
      responseType: 'blob'
    }
  })
}
