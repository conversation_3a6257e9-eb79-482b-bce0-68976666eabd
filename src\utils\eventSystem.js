export function createEventSystem() {
  const listeners = new Map()

  return {
    listen(event, callback) {
      if (!listeners.has(event)) {
        listeners.set(event, new Set())
      }
      listeners.get(event).add(callback)
      return () => this.unlisten(event, callback)
    },
    unlisten(event, callback) {
      if (listeners.has(event)) {
        listeners.get(event).delete(callback)
      }
    },
    emit(event, data) {
      if (listeners.has(event)) {
        listeners.get(event).forEach((callback) => callback(data))
      }
    }
  }
}

export const modalEvents = createEventSystem()
