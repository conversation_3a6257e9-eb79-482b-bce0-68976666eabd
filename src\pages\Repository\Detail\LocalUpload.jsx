import { useState, useEffect, forwardRef, useImperativeHandle } from 'react'
import { Upload, Button, Form, InputNumber, message } from 'antd'
import { InboxOutlined } from '@ant-design/icons'
import { getFileType, getRepoInfo, saveDoc } from '../service'
import SplitConfig from '../SplitConfig'
const { Dragger } = Upload

const LocalUpload = (props, ref) => {
  const { addLoading, setAddLoading, fileCurrent, repoId, finish } = props
  const [form] = Form.useForm()
  const [fileType, setFileType] = useState('')
  // const [fileList, setFileList] = useState([]);
  const [parseType, setParseType] = useState(1)

  const confirm = () => {
    form.validateFields().then(async (values) => {
      // console.log('表单values', values);
      let upFiles = values.fileList || []
      let params = {
        repoId,
        upType: 1,
        parseType
      }
      if (parseType === 2) {
        let parseConfig = {
          chunkType: values.strategy.join(','),
          lengthRange: JSON.stringify([16, values.maxLength])
        }
        if (values.strategy.includes('separator')) {
          parseConfig[values.symbolType] = JSON.stringify(values.identifier)
        }
        params.parseConfig = JSON.stringify(parseConfig)
      }
      if (fileCurrent?.id) {
        params['docId'] = fileCurrent.id
        if(parseType == 1 && fileCurrent.parseType == 1) return
        if(fileCurrent.parseConfig == params.parseConfig) return
      } else {
        params['fileListStr'] = JSON.stringify(
          upFiles.map((file) => {
            return {
              fileName: file.name,
              filePath: file.response.data[0] || '',
              fileSize: file.size
            }
          })
        )
      }

      setAddLoading(true)
      try {
        let resData = await saveDoc(params)
        if (resData.data.code == 0) {
          setAddLoading(false)
          message.success('保存成功，开始为您解析文档!')
          finish()
        }
      } catch (e) {
        setAddLoading(false)
      }
    })
  }

  //对外暴露confirm方法
  useImperativeHandle(ref, () => ({
    confirm
    // reset: getDetail
  }))

  const getDetail = () => {
    getRepoInfo({ repoId }).then((data) => {
      let res = data.data
      fillRepoForm(res.data)
    })
  }
  const fillRepoForm = (repoData) => {
    let repoConfig = JSON.parse(repoData.repoConfig)
    if (repoConfig.parseSplitModel) {
      let parseModelInfo = JSON.parse(repoConfig.parseSplitModel.modelInfo)
      // console.log('分段类型',parseModelInfo);
      setParseType(parseModelInfo.parseType || 1)
      let parseConfig = parseModelInfo.parseConfig
      if (parseConfig) {
        form.setFieldsValue({
          strategy: parseConfig.chunkType.split(','),
          identifier: JSON.parse(parseConfig.cutOff || parseConfig.separator || '[]'),
          maxLength: JSON.parse(parseConfig.lengthRange)[1],
          symbolType: parseConfig.separator ? 'separator' : 'cutOff'
        })
      }
    }
  }
  const handleBeforeUpload = (file, fileList) => {
    let type = file.name.split('.').pop().toLowerCase()
    let singleFileSize = file.size
    if (fileList.length > 10) {
      message.destroy()
      message.error(`单次上传不超过10个文件`)
    }
    if (fileType.indexOf(type) === -1) {
      message.error(`请检查${file.name}文件格式`)
      setAddLoading(false)
      return Upload.LIST_IGNORE
    }
    if (type === 'txt' || type === 'md') {
      if (singleFileSize > 10485760) {
        //10485760
        message.error(`${type}文件大小不能超过 10MB!`)
        setAddLoading(false)
        return Upload.LIST_IGNORE
      }
    } else {
      if (singleFileSize > 5 * 10485760) {
        //31457280
        message.error(`${type}文件大小不能超过 50MB!`)
        setAddLoading(false)
        return Upload.LIST_IGNORE
      }
    }
    return true
  }

  const handleUploadChange = ({ file, fileList }) => {
    const uploadingFiles = fileList.filter(
      (fileItem) => fileItem.status === 'ready' || fileItem.status === 'uploading'
    ).length
    if (uploadingFiles > 0) {
      setAddLoading(true)
    } else {
      setAddLoading(false)
    }
  }

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e
    }
    return e?.fileList
  }

  useEffect(() => {
    getFileType().then((data) => {
      let res = data.data
      setFileType(
        res.data
          .filter((item) => item != 'url')
          .map((item) => '.' + item)
          .join(',')
      )
    })
  }, [])

  useEffect(() => {
    // console.log('当前file',fileCurrent);
    if (fileCurrent?.id) {
      form.setFieldsValue({
        fileList: [
          { uid: '1', name: fileCurrent.docName, status: 'done', url: fileCurrent.filePath }
        ]
      })
      setParseType(fileCurrent.parseType)
      if (fileCurrent.parseConfig) {
        let parseConfig = JSON.parse(fileCurrent.parseConfig)
        form.setFieldsValue({
          strategy: parseConfig.chunkType.split(','),
          identifier: JSON.parse(parseConfig.cutOff || parseConfig.separator || '[]'),
          maxLength: JSON.parse(parseConfig.lengthRange)[1],
          symbolType: parseConfig.separator ? 'separator' : 'cutOff'
        })
      }
    } else {
      form.setFieldsValue({ fileList: [] })
      getDetail()
    }
  }, [fileCurrent])

  return (
    <Form form={form} labelCol={{ span: 4 }}>
      <Form.Item
        label="文件上传"
        name="fileList"
        valuePropName="fileList"
        getValueFromEvent={normFile}
        rules={[{ required: true, message: '请上传文件' }]}
      >
        <Dragger
          accept={fileType}
          action={`${import.meta.env.VITE_API_BASE_URL || '/iflycloud/api'}/aiuiKnowledge/knowledge/file/uploadFile`}
          withCredentials
          multiple
          headers={{
            'X-Auto-Token': window?.__DATA_FROM_PARENT__?.cookie
          }}
          beforeUpload={handleBeforeUpload}
          onChange={handleUploadChange}
          disabled={fileCurrent?.id}
          maxCount={10}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击上传或拖拽文件到这里</p>
          <p className="ant-upload-hint" style={{ fontSize: '12px' }}>
            单次最多上传10个文档，txt、Md文件不超过10M，其他文件(docx,doc,pdf)不超过50M
          </p>
        </Dragger>
      </Form.Item>
      <Form.Item label="拆分方法">
        <SplitConfig form={form} parseType={parseType} setParseType={setParseType} />
      </Form.Item>
    </Form>
  )
}

export default forwardRef(LocalUpload)
