import React, { useState, useEffect } from 'react'
import { Modal, Input, Button, message } from 'antd'
import { saveDoc } from '../service'
import { MyModal } from '@/components'

const RenameModal = (props) => {
  const { fileInfo, visible, setVisible, getDocs } = props
  const [fileNameNew, setFileNameNew] = useState('')

  const handleSave = (type, val, record) => {
    let params = {
      repoId: fileInfo.repoId,
      docId: fileInfo.id
    }
    params[type] = val
    if (type === 'docName') {
      if (val.trim().length == 0) {
        return message.warning('文件名不可为空')
      }
      setVisible(false)
      if (record.docType != 'url') {
        params[type] = val + '.' + record.docType
      }
    }

    saveDoc(params).then((data) => {
      let res = data.data
      if (res.code == 0) {
        message.success('保存成功')
        onClose()
        getDocs()
      }
    })
  }

  const onClose = () => {
    setVisible(false)
  }

  useEffect(() => {
    if (visible) {
      let nameIndex = fileInfo.docName.length
      if (fileInfo.docType != 'url') {
        nameIndex = fileInfo.docName.lastIndexOf('.')
      }
      let nameNew = fileInfo.docName.slice(0, nameIndex < 0 ? fileInfo.docName.length : nameIndex)
      setFileNameNew(nameNew)
    }
  }, [visible])

  return (
    <MyModal
      title="重命名"
      open={visible}
      onCancel={onClose}
      footer={[
        // <Button key="cancel" onClick={onClose}>
        //   取消
        // </Button>,
        <Button
          key="save"
          type="primary"
          onClick={() => handleSave('docName', fileNameNew, fileInfo)}
        >
          保存
        </Button>
      ]}
    >
      <Input
        value={fileNameNew}
        onChange={(e) => setFileNameNew(e.target.value)}
        placeholder="请输入文件名"
        addonAfter={fileInfo?.docType === 'url' ? null : `.${fileInfo?.docType}`}
        style={{ marginTop: 16 }}
      />
    </MyModal>
  )
}

export default RenameModal
